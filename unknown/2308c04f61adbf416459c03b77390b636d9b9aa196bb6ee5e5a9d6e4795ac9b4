// import "reflect-metadata";
// import { container } from "tsyringe";
// // import { config } from "dotenv";
// // config({ path: "/etc/palette/api/.env" });

// import InventoryRepo from "../inventories.repo";
// import { typeorm, utils } from "../../../../common";
// import CategoryRepo from "../../../../categories/apps/categories/categories.repo";
// import AttributeRepo from "../../attributes/attribute.repo";
// import { vectorStoreFactory } from "../../../../common/lib/chatbot/vector-stores/vector-stores-factory";
// import { embeddingFactory } from "../../../../common/lib/chatbot/llm-factory";
// import {
//     EMBEDDING_MODEL,
//     VECTOR_INDEX_NAME,
//     VECTOR_STORE_PROVIDER,
// } from "../../../../common/base/types/typing";

// const repo = container.resolve(InventoryRepo);
// const categoryRepo = container.resolve(CategoryRepo);
// const attrRepo = container.resolve(AttributeRepo);
// const vectorStore = vectorStoreFactory(
//     VECTOR_STORE_PROVIDER.ELASTIC_SEARCH,
//     embeddingFactory(EMBEDDING_MODEL.OPENAI_EMBEDDING),
//     {
//         index: VECTOR_INDEX_NAME.INVENTORIES,
//     },
// );

// const run = async () => {
//     await typeorm.AppDataSource.getInstance().initialize();

//     const categories = await categoryRepo.findByQuery({});
//     const categoryMapping = Object.fromEntries(
//         categories.map(({ id, name }) => {
//             return [id, name];
//         }),
//     );
//     const inventories = await repo.findByQuery({});

//     const attributes = await attrRepo.findByQuery({});
//     const attrMapping = attributes.reduce(
//         (acc, { inventoryId, key, value }) => {
//             if (utils.isNil(acc[inventoryId])) {
//                 acc[inventoryId] = [];
//             }

//             acc[inventoryId].push(`${key}: ${value}`);

//             return acc;
//         },
//         {} as Record<number, string[]>,
//     );

//     await vectorStore.index(
//         inventories.map((inventory) => {
//             let description = `محصول ${inventory.name} (id: ${inventory.id}) در دسته بندی ${categoryMapping[inventory.categoryId]} با قیمت ${inventory.price} تومان`;
//             if (utils.isNotNil(attrMapping[inventory.id])) {
//                 description =
//                     description +
//                     " " +
//                     `و ویژگی‌‌های ${attrMapping[inventory.id]?.join(",")}`;
//             }
//             return {
//                 pageContent: description,
//                 metadata: { userId: inventory.userId },
//                 id: inventory.id.toString(),
//             };
//         }),
//     );
// };

// run().catch((err) => {
//     console.error(err);
//     process.exit(1);
// });
