import { embeddingFactory } from "../embeddings/embeddings-factory";
import type { Document } from "@langchain/core/documents";
import { vectorStoreFactory } from "../vector-stores/vector-stores-factory";
import {
    EMBEDDING_MODEL,
    VECTOR_STORE_PROVIDER,
} from "../../../base/types/typing";

export class ElasticRetriever {
    private _embeddings;

    private _vectorStore;
    private _retriever;

    embeddingModelName: EMBEDDING_MODEL;
    vectorStoreName: VECTOR_STORE_PROVIDER.ELASTIC_SEARCH;

    constructor(args: { index: string }, filters: { userId: number }) {
        this.embeddingModelName = process.env.EMBEDDING as EMBEDDING_MODEL;
        this.vectorStoreName = VECTOR_STORE_PROVIDER.ELASTIC_SEARCH;

        try {
            this._embeddings = embeddingFactory(this.embeddingModelName);

            this._vectorStore = vectorStoreFactory(
                this.vectorStoreName,
                this._embeddings,
                { index: args.index },
            ).vectorStore;

            this._retriever = this._vectorStore.asRetriever({
                k: 5, // Limit to 5 results due to Instagram's character limitations
                filter: [
                    {
                        operator: "term",
                        field: "userId",
                        value: filters.userId,
                    },
                ],
            });
        } catch (error) {
            console.error(
                "[ElasticRetriever] Error during initialization:",
                error,
            );
            throw error;
        }
    }

    get vectorStore() {
        return this._vectorStore;
    }

    get retriever() {
        return this._retriever;
    }

    index = async (documents: Document[]) => {
        try {
            await this._vectorStore.addDocuments(documents);
        } catch (error) {
            throw error;
        }
    };
}
