// import "reflect-metadata";

// import { config } from "dotenv";
// // config({ path: "/etc/palette/api/.env" });
// config()

// import { container } from "tsyringe";
// import { PostgresPool } from "../memory";
// import { PostgresSaver } from "@langchain/langgraph-checkpoint-postgres";

// const run = async () => {
//     const PgPool = container.resolve(PostgresPool);
//     const memory = new PostgresSaver(PgPool.pool);
//     await memory.setup();
// };

// run().catch(console.error);
