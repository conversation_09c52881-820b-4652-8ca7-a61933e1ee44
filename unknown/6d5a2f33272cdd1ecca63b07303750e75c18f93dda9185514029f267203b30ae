import { type Embeddings } from "@langchain/core/embeddings";
import { Client, type ClientOptions } from "@elastic/elasticsearch";
import {
    type ElasticClientArgs,
    ElasticVectorSearch,
} from "@langchain/community/vectorstores/elasticsearch";
import { BaseVectorStore } from "./base-vector-store";
import { VectorStore } from "@langchain/core/vectorstores";

export interface ElasticVectorStoreArgs {
    index: string;
}

export class ElasticVectorStore extends BaseVectorStore {
    protected _vectorStore: VectorStore;
    constructor(embeddings: Embeddings, args: ElasticVectorStoreArgs) {
        super();

        const elasticConfig: ClientOptions = {
            node: process.env.ELASTIC_URI,
        };

        const elasticClientArgs: ElasticClientArgs = {
            client: new Client(elasticConfig),
            indexName: args.index,
        };

        this._vectorStore = new ElasticVectorSearch(
            embeddings,
            elasticClientArgs,
        );
    }
}
