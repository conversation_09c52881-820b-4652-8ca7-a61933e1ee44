import { Pool } from "pg";
import { TABLE_NAMES } from "../../../base/types/typing";

interface MessageData {
  threadId: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
}

interface Message extends MessageData {
  id: number;
  createdAt: Date;
}

export class MessageHistoryRepo {
  private readonly tableName = TABLE_NAMES.LLM_CHAT_HISTORY;

  constructor(private readonly pool: Pool) {}

  async saveMessage(data: MessageData): Promise<void> {
    console.log(`[MessageRepo] Saving message to ${this.tableName}:`, {
      threadId: data.threadId,
      role: data.role,
      contentLength: data.content.length
    });

    const query = `
      INSERT INTO ${this.tableName} (thread_id, content, role, created_at)
      VALUES ($1, $2, $3, NOW())
    `;

    try {
      await this.pool.query(query, [
        data.threadId,
        data.content,
        data.role
      ]);
      console.log(`[MessageRepo] Message saved successfully to ${this.tableName}`);
    } catch (error) {
      console.error(`[MessageRepo] Error saving message to ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Get messages for a specific thread with performance optimizations
   *
   * @param threadId - The ID of the conversation thread
   * @param options - Optional parameters to limit results
   * @param options.limit - Maximum number of messages to retrieve (default: 100)
   * @param options.hoursBack - Only retrieve messages from the last X hours (default: null, meaning no time limit)
   * @returns Array of messages in chronological order (oldest first)
   *
   * Performance considerations:
   * - Limiting the number of messages improves database query performance
   * - Using DESC order with LIMIT gets the most recent messages efficiently
   * - Reversing the results maintains chronological order for the LLM
   * - Time filtering is optional and only applied when explicitly requested
   */
  async getMessages(
    threadId: string,
    options: { limit?: number; hoursBack?: number | null } = {}
  ): Promise<Message[]> {
    // Default to 100 messages with NO time limit
    const limit = options.limit || 100;
    const hoursBack = options.hoursBack || null; // null means no time limit

    let queryLog = `[MessageRepo] Getting up to ${limit} messages`;
    if (hoursBack !== null) {
      queryLog += ` from last ${hoursBack} hours`;
    } else {
      queryLog += ` (no time limit)`;
    }
    queryLog += ` for threadId: ${threadId}`;

    console.log(queryLog);

    // Build the query based on whether we have a time limit
    let query = `
      SELECT id, thread_id as "threadId", content, role, created_at as "createdAt"
      FROM ${this.tableName}
      WHERE thread_id = $1
    `;

    // Add time filter only if hoursBack is specified
    if (hoursBack !== null) {
      query += ` AND created_at > NOW() - INTERVAL '${hoursBack} hours'`;
    }

    // Complete the query with ordering and limit
    query += `
      ORDER BY created_at DESC
      LIMIT $2
    `;

    try {
      const result = await this.pool.query(query, [threadId, limit]);
      console.log(`[MessageRepo] Found ${result.rows.length} messages for threadId: ${threadId}`);

      // Reverse to get chronological order (oldest first) which is expected by the LLM
      return result.rows.reverse();
    } catch (error) {
      console.error(`[MessageRepo] Error getting messages for threadId: ${threadId}`, error);
      throw error;
    }
  }

  async clearMessages(threadId: string): Promise<void> {
    console.log(`[MessageRepo] Clearing messages for threadId: ${threadId}`);

    const query = `
      DELETE FROM ${this.tableName}
      WHERE thread_id = $1
    `;

    try {
      const result = await this.pool.query(query, [threadId]);
      console.log(`[MessageRepo] Cleared ${result.rowCount} messages for threadId: ${threadId}`);
    } catch (error) {
      console.error(`[MessageRepo] Error clearing messages for threadId: ${threadId}`, error);
      throw error;
    }
  }
}
