import { type VectorStore } from "@langchain/core/vectorstores";
import { type Document } from "@langchain/core/documents";

export abstract class BaseVectorStore {
    protected abstract _vectorStore: VectorStore;

    get vectorStore() {
        return this._vectorStore;
    }

    index = async (documents: Document[]) => {
        await this._vectorStore.addDocuments(documents);
    };

    //**This function creates an index using he same ID in PostgresSQL*/
    index‌BySpecificId = async (documents: Document[]) => {
        await this._vectorStore.addDocuments(documents, {
            ids: documents.map((doc) => doc.id),
        });
    };

    delete = async (id: string) => {
        await this._vectorStore.delete({ ids: [id] });
    };
}
