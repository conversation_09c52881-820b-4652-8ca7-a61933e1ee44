import { inject, injectable } from 'tsyringe';
import Redis from 'ioredis';
import {RedisConnection} from './index';
import { v4 as uuidv4 } from 'uuid';
import { Cart } from '../../base/types/typing';


@injectable()
export class RedisCartManager {
    private redis: Redis;
    private prefix: string = `${process.env.REDIS_PREFIX}:cart:`;
    private lockPrefix: string = `${process.env.REDIS_PREFIX}:cart-lock:`;
    private expiryTime: number = 60 * 60 * 7; // 7 hours in seconds
    private lockTimeout: number = 10; // 10 seconds lock timeout

    constructor(
        @inject(RedisConnection) private redisConnection: RedisConnection
    ) {
        this.redis = redisConnection.getConnection();
    }

    private async acquireLock(threadId: string): Promise<string | null> {
        const lockKey = this.lockPrefix + threadId;
        const lockValue = uuidv4();

        // Try to set the lock with NX (only if it doesn't exist)
        const result = await this.redis.set(lockKey, lockValue, 'EX', this.lockTimeout, 'NX');

        if (result === 'OK') {
            return lockValue; // Lock acquired successfully
        }

        return null; // Failed to acquire lock
    }

    private async releaseLock(threadId: string, lockValue: string): Promise<boolean> {
        const lockKey = this.lockPrefix + threadId;

        // Only release the lock if it's still the one we set
        const script = `
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        `;

        const result = await this.redis.eval(script, 1, lockKey, lockValue);
        return result === 1;
    }

    async getCart(threadId: string): Promise<Cart> {
        const key = this.prefix + threadId;
        const cartData = await this.redis.get(key);

        if (!cartData) {
            return {
                items: [],
                pendingConfirmation: false
            };
        }

        try {
            return JSON.parse(cartData);
        } catch (error) {
            console.error("Error parsing cart data from Redis:", error);
            return {
                items: [],
                pendingConfirmation: false
            };
        }
    }

    async saveCart(threadId: string, cart: Cart): Promise<void> {
        const key = this.prefix + threadId;
        await this.redis.set(key, JSON.stringify(cart), 'EX', this.expiryTime);
    }

    async updateCart(threadId: string, updateFn: (cart: Cart) => Cart): Promise<Cart> {
        let attempts = 0;
        const maxAttempts = 5;

        while (attempts < maxAttempts) {
            const lockValue = await this.acquireLock(threadId);

            if (!lockValue) {
                // Wait a bit and retry if we couldn't get the lock
                await new Promise(resolve => setTimeout(resolve, 200));
                attempts++;
                continue;
            }

            try {
                // Get the current cart
                const cart = await this.getCart(threadId);

                // Apply the update function
                const updatedCart = updateFn(cart);

                // Save the updated cart
                await this.saveCart(threadId, updatedCart);

                return updatedCart;
            } finally {
                // Always release the lock
                await this.releaseLock(threadId, lockValue);
            }
        }

        throw new Error(`Failed to update cart after ${maxAttempts} attempts`);
    }

    async clearCart(threadId: string): Promise<void> {
        const key = this.prefix + threadId;
        await this.redis.del(key);
    }
}