# Instagram Disconnect Implementation

## Overview
This implementation provides a secure way to disconnect Instagram accounts from the system by clearing Instagram-related data from the database. Since Instagram doesn't provide an official API to revoke permissions, this approach effectively "disconnects" the account from the user's perspective.

## Implementation Details

### Files Modified
1. **`modules/channels/apps/channels/channel.service.ts`** - Added `disconnectInstagram` method
2. **`modules/channels/apps/channels/channel.controller.ts`** - Added `disconnectInstagram` endpoint
3. **`modules/channels/apps/channels/channel.router.ts`** - Added route for disconnect endpoint
4. **`modules/channels/apps/channels/responses/disconnect-instagram.ts`** - Response schema
5. **`modules/channels/apps/channels/responses/index.ts`** - Export new response schema

### API Endpoint
```
POST /api/v1/channels/instagram/disconnect
Authorization: Bearer <user_token>
```

### Expected Response
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "Instagram account disconnected successfully"
  },
  "message": "با موفقیت انجام شد"
}
```

### Error Responses
- **400 Bad Request**: When Instagram account is not connected
- **404 Not Found**: When user doesn't have a channel
- **500 Internal Server Error**: For unexpected errors

## Database Changes After Disconnect
The following fields in the `channel` table are set to `undefined` (NULL in database):
- `accessToken`
- `accessTokenExpiresAt`
- `platformId`
- `username`

**Note**: The channel record itself is preserved along with user settings like `name`, `description`, `exchangeRateType`, etc.

## Security Features
1. **Validation**: Checks if Instagram is actually connected before attempting disconnect
2. **Error Handling**: Comprehensive error handling with proper logging
3. **Transaction Safety**: Uses database transactions where needed
4. **Authentication**: Requires valid JWT token
5. **Logging**: Detailed logging for audit trail

## Testing Steps
1. **Prerequisites**: User should have a connected Instagram account (channel data not null)
2. **Call Disconnect**: `POST /api/v1/channels/instagram/disconnect` with valid JWT
3. **Verify Response**: Check for successful response
4. **Check Profile API**: Verify that profile API now returns `"channel": null`
5. **Test Reconnection**: User should be able to connect again using existing connect endpoint

## Frontend Integration
The frontend dashboard should:
1. **Show Disconnect Button**: When Instagram is connected (`channel` object exists in profile)
2. **Call Disconnect API**: When user clicks disconnect button
3. **Update UI State**: Show disconnected state after successful response
4. **Show Connect Button**: Allow user to reconnect using existing connect flow

## Benefits of This Approach
1. **User Control**: Users can easily disconnect their Instagram account
2. **Data Preservation**: User settings and channel configuration are preserved
3. **Clean State**: Instagram-specific data is completely cleared
4. **Reconnection Ready**: Users can reconnect anytime using the existing flow
5. **Audit Trail**: All actions are logged for debugging and compliance

## Alternative Approaches Considered
1. **Token Revocation**: Instagram doesn't provide official revocation API
2. **Account Deletion**: Too destructive, would lose user settings
3. **Flag-based Disable**: Less clean, leaves sensitive data in database

## Conclusion
This implementation provides the best balance of user control, data security, and system cleanliness for Instagram account disconnection.
