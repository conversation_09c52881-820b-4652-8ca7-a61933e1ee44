{"compilerOptions": {"target": "es2022", "module": "CommonJS", "declaration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "outDir": "./dist", "typeRoots": ["modules/common/lib/types", "node_modules/@types"]}, "include": ["modules/**/*", "migrations/*", "eslint.config.mjs", "lint-staged.config.mjs", ".prettierrc.js", ".mocharc.js", "jest.config.ts", "jest.e2e.config.ts"]}