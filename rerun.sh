#!/bin/bash

set -e

# Step 1: Get .env path from args
ENV_FILE="$1"
if [ -z "$ENV_FILE" ]; then
  echo "❌ Please provide the full path to the .env file"
  echo "Usage: ./rerun.sh /etc/palette/ir/.env"
  exit 1
fi
if [ ! -f "$ENV_FILE" ]; then
  echo "❌ Environment file does not exist: $ENV_FILE"
  exit 1
fi

# Step 2: Restart with sudo if not root
if [ "$(id -u)" -ne 0 ]; then
  echo "⚠️  Restarting with sudo..."
  exec sudo "$0" "$ENV_FILE"
fi

# Step 3: Load env vars from .env
echo "📦 Loading environment from $ENV_FILE"
set -a
source "$ENV_FILE"
set +a

# Step 4: Extract ENV_NAME from path (e.g. 'ir' or 'us')
ENV_NAME=$(basename "$(dirname "$ENV_FILE")")

# Step 5: Validate required environment variables
MISSING=0
[ -z "$VIRTUAL_HOST" ]   && echo "❌ VIRTUAL_HOST is not set"   && MISSING=1
[ -z "$LETSENCRYPT_HOST" ]&& echo "❌ LETSENCRYPT_HOST is not set" && MISSING=1
[ -z "$VIRTUAL_PORT" ]   && echo "❌ VIRTUAL_PORT is not set"   && MISSING=1
if [ "$MISSING" -eq 1 ]; then
  echo "❌ Missing one or more required environment variables"
  exit 1
fi

echo "🌍 PUBLIC_APP_LANG: $PUBLIC_APP_LANG"
echo "🌍 Environment: $ENV_NAME"
echo "🔗 VIRTUAL_HOST=$VIRTUAL_HOST"
echo "🔒 LETSENCRYPT_HOST=$LETSENCRYPT_HOST"
echo "🌐 VIRTUAL_PORT=$VIRTUAL_PORT"

# Step 6: Ensure external proxy networks exist
for net in nginx-proxy-ir nginx-proxy-us; do
  if ! docker network inspect "$net" &>/dev/null; then
    echo "🌐 Creating external network $net..."
    docker network create "$net"
  else
    echo "🌐 External network $net already exists."
  fi
done

# Step 7: Prepare temp env file
TMP_ENV="/tmp/palette-${ENV_NAME}.env"
cp "$ENV_FILE" "$TMP_ENV"
grep -q "^VIRTUAL_HOST="    "$TMP_ENV" || echo "VIRTUAL_HOST=$VIRTUAL_HOST"    >> "$TMP_ENV"
grep -q "^LETSENCRYPT_HOST=" "$TMP_ENV" || echo "LETSENCRYPT_HOST=$LETSENCRYPT_HOST" >> "$TMP_ENV"
grep -q "^VIRTUAL_PORT="    "$TMP_ENV" || echo "VIRTUAL_PORT=$VIRTUAL_PORT"    >> "$TMP_ENV"
echo "ENV_NAME=$ENV_NAME" >> "$TMP_ENV"

# Step 8: Pick the right compose file
COMPOSE_FILE="docker-compose.${ENV_NAME}.yml"
if [ ! -f "$COMPOSE_FILE" ]; then
  echo "❌ Compose file not found: $COMPOSE_FILE"
  exit 1
fi
echo "📄 Using compose file: $COMPOSE_FILE"

# Step 9: Tear down old stack
echo "🛑 Stopping existing containers for palette-$ENV_NAME..."
docker compose --env-file "$TMP_ENV" -f "$COMPOSE_FILE" -p "palette-$ENV_NAME" down

# Step 10: Build & up
echo "🚀 Building and starting containers..."
docker compose --env-file "$TMP_ENV" -f "$COMPOSE_FILE" -p "palette-$ENV_NAME" up --build -d

# Step 11: Cleanup
rm "$TMP_ENV"

echo "✅ palette-$ENV_NAME is up and running!"
