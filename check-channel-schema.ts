import { DataSource } from "typeorm";
import { config } from "./typeorm.config";

async function checkChannelSchema() {
    const dataSource = new DataSource(config);
    
    try {
        await dataSource.initialize();
        
        // Check if columns exist
        const result = await dataSource.query(`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'channel' 
            AND column_name IN ('chatbotEnabled', 'isDisconnected')
            ORDER BY column_name;
        `);
        
        console.log("Current channel table columns:");
        console.table(result);
        
        // Check all channel columns
        const allColumns = await dataSource.query(`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'channel'
            ORDER BY ordinal_position;
        `);
        
        console.log("\nAll channel table columns:");
        console.table(allColumns);
        
    } catch (error) {
        console.error("Error:", error);
    } finally {
        await dataSource.destroy();
    }
}

checkChannelSchema();
