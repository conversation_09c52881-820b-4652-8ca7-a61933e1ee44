# kind: pipeline
# type: docker
# name: default
# steps:
#     - name: stage
#       image: docker.arvancloud.ir/docker/compose
#       volumes:
#           - name: cache
#             path: /var/run/docker.sock
#       commands:
#           - docker-compose -p palette up -d --build
#       when:
#           branch:
#               - develop
#           event:
#               - push

# volumes:
#     - name: cache
#       host:
#           path: /var/run/docker.sock
