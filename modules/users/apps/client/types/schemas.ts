import { Client } from "./entities";

export type GetClientResponse = Pick<
    Client,
    "id" | "username" | "messagingEnabled"
> & {
    totalOrders: number;
    lastOrderDate?: string;
    lastMessageDate?: string;
    lastMessage?: string;
};

export interface ClientsReportMetadata {
    from: string;
    to: string;
}
export interface ClientsReportData {
    count: number;
    totalCount: number;
}

export interface ClientsReportResponse {
    metadata: ClientsReportMetadata;
    data: ClientsReportData;
}
