import { layers } from "../../../common";
import { Client } from "./types";
import { ClientSchema } from "./client.model";

export default class ClientRepo extends layers.BaseTypeormRepository<Client> {
    relations = [];

    constructor() {
        super(ClientSchema);
    }

    getClientsForAdmin = (userId: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("client")
            .where("client.userId = :userId", { userId })
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    async getClientsForUser(query: Partial<Express.Query>, userId: number) {
        const result = await this.createQueryBuilder("client")
            .where("client.userId = :userId", { userId })
            .leftJoinAndSelect(
                (subQuery) => {
                    return subQuery
                        .from("chat", "chat")
                        .select(`"chat"."clientId" AS "clientId"`)
                        .addSelect(
                            `LAST_VALUE("chat"."text") OVER (PARTITION BY "chat"."clientId") AS "lastMessage"`,
                        )
                        .addSelect(
                            `LAST_VALUE("chat"."createdAt") OVER (PARTITION BY "chat"."clientId") AS "lastMessageDate"`,
                        )
                        .where("chat.userId = :userId", { userId });
                },
                "chat",
                `client.id = "chat"."clientId"`,
            )
            .leftJoinAndSelect(
                (subQuery) => {
                    return subQuery
                        .from("order", "order")
                        .select(
                            `"order"."clientId" as "clientId", COUNT("order"."id")::INTEGER as "totalOrders", MAX("order"."createdAt") as "lastOrderDate"`,
                        )
                        .where("order.userId = :userId", { userId })
                        .groupBy("order.clientId");
                },
                "order",
                `client.id = "order"."clientId"`,
            )
            .filter(query.filter, ...this.relations)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();

        return result;
    }

    async getClientForUser(id: number, userId: number) {
        const query = this.createQueryBuilder("client")
            .where("client.id = :id", { id })
            .andWhere("client.userId = :userId", { userId })
            .leftJoinAndSelect(
                (subQuery) => {
                    return subQuery
                        .from("chat", "chat")
                        .select(`"chat"."clientId" AS "clientId"`)
                        .addSelect(
                            `LAST_VALUE("chat"."text") OVER (PARTITION BY "chat"."clientId") AS "lastMessage"`,
                        )
                        .addSelect(
                            `LAST_VALUE("chat"."createdAt") OVER (PARTITION BY "chat"."clientId") AS "lastMessageDate"`,
                        )
                        .where("chat.userId = :userId", { userId });
                },
                "chat",
                `client.id = "chat"."clientId"`,
            )
            .leftJoinAndSelect(
                (subQuery) => {
                    return subQuery
                        .from("order", "order")
                        .select(
                            `"order"."clientId" as "clientId", COUNT("order"."id")::INTEGER as "totalOrders", MAX("order"."createdAt") as "lastOrderDate"`,
                        )
                        .where("order.clientId = :id", { id })
                        .andWhere("order.userId = :userId", { userId })
                        .groupBy("order.clientId");
                },
                "order",
                `client.id = "order"."clientId"`,
            );

        return query.getRawMany();
    }

    async getClientsCount(userId: number, from: string, to: string) {
        const query = this.createQueryBuilder("client")
            .select(`COUNT(*)::INTEGER AS "totalCount"`)
            .addSelect((subQuery) => {
                return subQuery
                    .from("client", "client")
                    .select("COUNT(*)::INTEGER AS count")
                    .where("client.userId = :userId", {
                        userId,
                    })
                    .andWhere("client.createdAt >= :from", { from })
                    .andWhere("client.createdAt <= :to", { to });
            })
            .where("client.userId = :userId", {
                userId,
            });

        return query.getRawMany();
    }
}
