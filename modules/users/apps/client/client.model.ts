import { EntitySchema } from "typeorm";
import { Client } from "./types";

export const ClientSchema = new EntitySchema<Client>({
    name: "client",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        username: {
            type: String,
        },
        userId: {
            type: Number,
        },
        messagingEnabled: {
            type: Boolean,
            default: true,
        },
        channelId: {
            type: Number,
        },
        platformId: {
            type: String,
            nullable: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    indices: [
        {
            name: "IDX_UNIQUE_CHANNEL_CLIENT",
            unique: true,
            columns: ["channelId", "platformId"],
        },
    ],
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
        },
        channel: {
            type: "many-to-one",
            target: "channel",
        },
    },
});
