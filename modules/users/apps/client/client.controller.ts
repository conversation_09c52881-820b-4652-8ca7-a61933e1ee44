import { injectable } from "tsyringe";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";

import ClientService from "./client.service";
import { EditClientsSchema } from "./schemas";

import {
    GetClientResponseSchema,
    GetClientsReportResponseSchema,
    GetClientsResponseSchema,
} from "./responses";

@injectable()
export default class ClientController {
    constructor(private _service: ClientService) {}

    @OpenAPI(
        "users/clients",
        "/",
        "get",
        undefined,
        undefined,
        GetClientsResponseSchema,
        "bearerAuth",
    )
    getClients = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const result = await this._service.getClients(parsedQuery, user!);

        res.success(result);
    };

    @OpenAPI(
        "users/clients",
        "/{clientId}",
        "get",
        undefined,
        [
            {
                in: "path",
                name: "clientId",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        GetClientResponseSchema,
        "bearerAuth",
    )
    getClient = async (req: Request, res: Response) => {
        const {
            user,
            params: { clientId },
        } = req;

        const result = await this._service.getClient(Number(clientId), user!);

        res.success(result);
    };

    @OpenAPI(
        "users/clients",
        "/{clientId}",
        "patch",
        EditClientsSchema,
        [
            {
                in: "path",
                name: "clientId",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editClient = async (req: Request, res: Response) => {
        const {
            body,
            user,
            params: { clientId },
        } = req;

        await this._service.editClient(Number(clientId), body, user!);

        res.success({});
    };

    @OpenAPI(
        "users/clients",
        "/report",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2000-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
        ],
        GetClientsReportResponseSchema,
        "bearerAuth",
    )
    getClientsCount = async (req: Request, res: Response) => {
        const { parsedQuery, user } = req;

        const clients = await this._service.getClientsCount(parsedQuery, user!);

        res.success(clients);
    };
}
