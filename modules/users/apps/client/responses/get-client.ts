import { JSONSchemaType } from "ajv";
import { GetClientResponse } from "../types";

export const GetClientResponseSchema: JSONSchemaType<GetClientResponse> = {
    type: "object",
    prefix: "client_",
    properties: {
        id: { type: "number" },
        username: { type: "string" },
        messagingEnabled: { type: "boolean" },
        totalOrders: {
            type: "integer",
            prefix: "",
            default: 0,
        },
        lastOrderDate: {
            type: "string",
            format: "date-time",
            prefix: "",
            nullable: true,
            default: null,
        },
        lastMessageDate: {
            type: "string",
            format: "date-time",
            prefix: "",
            nullable: true,
            default: null,
        },
        lastMessage: {
            type: "string",
            prefix: "",
            nullable: true,
            default: null,
        },
    },
    required: [],
};
