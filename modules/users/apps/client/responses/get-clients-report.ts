import { JSONSchemaType } from "ajv";
import { ClientsReportResponse } from "../types";

export const GetClientsReportResponseSchema: JSONSchemaType<ClientsReportResponse> =
    {
        type: "object",
        properties: {
            metadata: {
                type: "object",
                properties: {
                    from: {
                        type: "string",
                    },
                    to: {
                        type: "string",
                    },
                },
                required: [],
            },
            data: {
                type: "object",
                properties: {
                    totalCount: {
                        type: "integer",
                    },
                    count: {
                        type: "integer",
                    },
                },
                required: [],
            },
        },
        required: [],
    };
