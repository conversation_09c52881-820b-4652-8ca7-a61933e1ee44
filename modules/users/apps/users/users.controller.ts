import { injectable } from "tsyringe";
import { Request, Response } from "express";

import UserService from "./users.service";
import { OpenAPI } from "../../../common/lib/decorators";
import { GoogleAuthSchema, LoginSchema, VerifyLoginOtpSchema } from "./schemas";
import {
    GetUserProfileResponseSchema,
    OtpResponseSchema,
    TokenResponseSchema,
} from "./responses";
import { getSuccessMessage } from "../../../common/lib/errors/locales/success-messages";

@injectable()
export default class UserController {
    constructor(private _service: UserService) {}

    @OpenAPI(
        "users",
        "/login",
        "post",
        LoginSchema,
        undefined,
        OtpResponseSchema,
        "bearerAuth",
    )
    login = async (req: Request, res: Response) => {
        const { body } = req;
        const result = await this._service.login(body);
        res.success(result, getSuccessMessage("USER_OTP_SENT_SUCCESSFULLY"));
    };

    @OpenAPI(
        "users",
        "/verify-login-otp",
        "post",
        VerifyLoginOtpSchema,
        undefined,
        TokenResponseSchema,
        "bearerAuth",
    )
    verifyLoginOtp = async (req: Request, res: Response) => {
        const { body } = req;
        const token = await this._service.verifyLoginOtp(body);
        res.success(token, getSuccessMessage("USER_LOGGED_IN_SUCCESSFULLY"));
    };

    @OpenAPI(
        "users",
        "/auth/google",
        "post",
        GoogleAuthSchema,
        [],
        TokenResponseSchema,
        undefined,
    )
    googleAuth = async (req: Request, res: Response) => {

        const { body } = req;
        if (!body.token) {
            console.log("ERROR: No token provided in request");
            throw new Error("No authentication token provided");
        }

        try {
            // Pass the token to the service
            const response = await this._service.googleAuth(body);
            res.success(response);
        } catch (error) {
            throw error; // Let the error middleware handle it
        }
    };

    @OpenAPI(
        "users",
        "/profile",
        "get",
        undefined,
        undefined,
        GetUserProfileResponseSchema,
        "bearerAuth",
    )
    getUserProfile = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;
        const userProfile = await this._service.getUserProfile(
            user!,
            parsedQuery,
        );
        res.success(userProfile);
    };
}
