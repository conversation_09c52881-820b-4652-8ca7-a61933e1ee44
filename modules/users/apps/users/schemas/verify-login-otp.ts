import { JSONSchemaType } from "ajv";
import { VerifyLoginOtpDto } from "../types";

export const VerifyLoginOtpSchema: JSONSchemaType<VerifyLoginOtpDto> = {
    $id: "verify-login-otp",
    type: "object",
    properties: {
        phone: {
            type: "string",
            pattern: "09\\d{9}",
            example: "09199999999",
        },
        otp: {
            type: "string",
        },
    },
    required: ["phone", "otp"],
    additionalProperties: false,
};
