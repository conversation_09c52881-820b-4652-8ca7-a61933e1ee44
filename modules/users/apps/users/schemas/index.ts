import { validators } from "../../../../common";

import { LoginSchema } from "./login";
import { VerifyLoginOtpSchema } from "./verify-login-otp";
import { InstagramWebhookSchema } from "./instagram-webhook";
import { VerifyInstagramWebhookSchema } from "./verify-instagram-webhook";
import { GoogleAuthSchema } from "./google-auth";

const bv = validators.AJVValidator([
    LoginSchema,
    VerifyLoginOtpSchema,
    InstagramWebhookSchema,
    VerifyInstagramWebhookSchema,
    GoogleAuthSchema,
]);

export * from "./login";
export * from "./verify-login-otp";
export * from "./instagram-webhook";
export * from "./verify-instagram-webhook";
export * from "./google-auth";

export const login = bv("login");
export const verifyLoginOtp = bv("verify-login-otp");
export const instagramWebhook = bv("instagram-webhook");
export const verifyInstagramWebhook = bv("verify-instagram-webhook");
export const googleAuth = bv("google-auth");
