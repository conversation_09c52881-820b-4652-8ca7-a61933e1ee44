import { JSONSchemaType } from "ajv";
import { VerifyInstagramWebhookDto } from "../types";

export const VerifyInstagramWebhookSchema: JSONSchemaType<VerifyInstagramWebhookDto> =
    {
        $id: "verify-instagram-webhook",
        type: "object",
        properties: {
            "hub.mode": {
                type: "string",
                enum: ["subscribe"],
                example: "subscribe",
            },
            "hub.verify_token": {
                type: "string",
                example: "your_verify_token",
            },
            "hub.challenge": {
                type: "number",
                example: 1158201444,
            },
        },
        required: ["hub.mode", "hub.verify_token", "hub.challenge"],
    };
