import express from "express";
import { container } from "tsyringe";
import UserController from "./users.controller";

import * as schemas from "./schemas";
import { middlewares } from "../../../common";

const router = express.Router();

const controller = container.resolve(UserController);

router.route("/login").post(schemas.login, controller.login);

router
    .route("/verify-login-otp")
    .post(schemas.verifyLoginOtp, controller.verifyLoginOtp);

router.route("/auth/google").post(controller.googleAuth);

router.route("/profile").get(middlewares.JWT, controller.getUserProfile);

export default router;
