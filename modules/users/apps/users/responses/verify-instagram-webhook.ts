/* eslint-disable camelcase */
import { JSONSchemaType } from "ajv";
import { VerifyInstagramWebhookOutputDto } from "../types";

export const VerifyInstagramWebhookOutputSchema: JSONSchemaType<VerifyInstagramWebhookOutputDto> =
    {
        $id: "verify-instagram-webhook-output",
        type: "object",
        properties: {
            object: {
                type: "string",
                example: "page",
            },
            entry: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        id: { type: "string" },
                        time: { type: "number" },
                        messaging: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    sender: {
                                        type: "object",
                                        properties: {
                                            id: { type: "string" },
                                        },
                                        required: ["id"],
                                    },
                                    recipient: {
                                        type: "object",
                                        properties: {
                                            id: { type: "string" },
                                        },
                                        required: ["id"],
                                    },
                                    timestamp: { type: "number" },
                                    message: {
                                        type: "object",
                                        properties: {
                                            mid: { type: "string" },
                                            text: {
                                                type: "string",
                                                nullable: true,
                                            },
                                            attachments: {
                                                type: "array",
                                                items: {
                                                    type: "object",
                                                    properties: {
                                                        type: {
                                                            type: "string",
                                                        },
                                                        payload: {
                                                            type: "object",
                                                            properties: {
                                                                url: {
                                                                    type: "string",
                                                                },
                                                            },
                                                            required: ["url"],
                                                        },
                                                    },
                                                    required: [
                                                        "type",
                                                        "payload",
                                                    ],
                                                },
                                                nullable: true,
                                            },
                                            is_deleted: {
                                                type: "boolean",
                                                nullable: true,
                                            },
                                            is_echo: {
                                                type: "boolean",
                                                nullable: true,
                                            },
                                            quick_reply: {
                                                type: "object",
                                                properties: {
                                                    payload: { type: "string" },
                                                },
                                                required: ["payload"],
                                                nullable: true,
                                            },
                                            reply_to: {
                                                type: "object",
                                                properties: {
                                                    mid: { type: "string" },
                                                },
                                                required: ["mid"],
                                                nullable: true,
                                            },
                                        },
                                        required: ["mid"],
                                        nullable: true,
                                    },
                                    reaction: {
                                        type: "object",
                                        properties: {
                                            mid: { type: "string" },
                                            action: {
                                                type: "string",
                                                enum: ["react", "unreact"],
                                            },
                                            reaction: {
                                                type: "string",
                                                nullable: true,
                                            },
                                            emoji: {
                                                type: "string",
                                                nullable: true,
                                            },
                                        },
                                        required: ["mid", "action"],
                                        nullable: true,
                                    },
                                    postback: {
                                        type: "object",
                                        properties: {
                                            mid: { type: "string" },
                                            title: { type: "string" },
                                            payload: { type: "string" },
                                        },
                                        required: ["mid", "title", "payload"],
                                        nullable: true,
                                    },
                                    referral: {
                                        type: "object",
                                        properties: {
                                            ref: { type: "string" },
                                            source: { type: "string" },
                                            type: {
                                                type: "string",
                                                enum: ["OPEN_THREAD"],
                                            },
                                        },
                                        required: ["ref", "source", "type"],
                                        nullable: true,
                                    },
                                    read: {
                                        type: "object",
                                        properties: {
                                            mid: { type: "string" },
                                        },
                                        required: ["mid"],
                                        nullable: true,
                                    },
                                },
                                required: ["sender", "recipient", "timestamp"],
                            },
                        },
                    },
                    required: ["id", "time", "messaging"],
                },
            },
        },
        required: ["object", "entry"],
    };
