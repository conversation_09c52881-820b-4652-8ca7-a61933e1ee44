import { JSONSchemaType } from "ajv";
import { GetChannelResponseSchema } from "../../../../channels/apps/channels/responses";
import { GetUserResponse } from "../types";

export const GetUsersProfileResponseSchema: JSONSchemaType<GetUserResponse[]> =
    {
        type: "array",
        items: {
            type: "object",
            prefix: "user_",
            properties: {
                id: {
                    type: "number",
                },
                phone: {
                    type: "string",
                },
                role: {
                    type: "string",
                },
                subscription: {
                    type: "object",
                    prefix: "subscription_",
                    properties: {
                        id: {
                            type: "number",
                        },
                        status: {
                            type: "string",
                        },
                        endDate: {
                            type: "string",
                            format: "date-time",
                        },
                        plan: {
                            type: "object",
                            prefix: "subscription-plan_",
                            properties: {
                                id: {
                                    type: "number",
                                },
                                name: {
                                    type: "string",
                                },
                                price: {
                                    type: "string",
                                },
                                isPopular: {
                                    type: "boolean",
                                },
                                duration: {
                                    type: "integer",
                                },
                            },
                            required: [],
                        },
                    },
                    required: [],
                },
                channel: GetChannelResponseSchema,
            },
            required: [],
        },
    };
