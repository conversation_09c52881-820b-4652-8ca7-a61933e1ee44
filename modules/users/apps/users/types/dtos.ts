import {
    AccessToken,
    InstagramAuthData,
    InstagramWebhookPayload,
    Messaging,
    MessagingEvent,
    VerifyInstagramWebhook,
} from "./entities";

export interface RegisterDto {
    phone: string;
}

export interface LoginDto {
    phone: string;
}

export interface VerifyLoginOtpDto {
    phone: string;
    otp: string;
}

export interface VerifyRegistrationOtpDto {
    phone: string;
    otp: string;
}

export type MessagingDto = Pick<Messaging, "sender" | "recipient">;

export type MessagingEventDto = Pick<
    MessagingEvent,
    "id" | "time" | "messaging"
>;

export type InstagramWebhookPayloadDto = Pick<
    InstagramWebhookPayload,
    "object" | "entry"
>;

export type AccessTokenDto = Pick<AccessToken, "token">;

export type VerifyInstagramWebhookDto = Pick<
    VerifyInstagramWebhook,
    "hub.mode" | "hub.verify_token" | "hub.challenge"
>;

export type VerifyInstagramWebhookOutputDto = Pick<
    InstagramWebhookPayload,
    "object" | "entry"
>;

export type InstagramAuthDataDto = Pick<
    InstagramAuthData,
    "clientId" | "clientSecret" | "grantType"
>;

export type GoogleAuthDto = { token: string };
