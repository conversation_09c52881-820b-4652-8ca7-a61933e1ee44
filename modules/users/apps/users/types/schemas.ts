import { GetChannelResponse } from "../../../../channels/apps/channels/types";
import { GetUserSubscriptionResponse } from "../../../../subscriptions/apps/user-subscriptions/types";
import { User } from "../types";

export interface OtpResponse {
    value: string;
    expirationDate: string;
}

export interface TokenResponse {
    token: string;
}

export type GetUserResponse = Pick<User, "id" | "phone" | "role"> & {
    channel: GetChannelResponse;
    subscription: GetUserSubscriptionResponse | null;
};
