import { layers } from "../../../common";
import { User } from "./types";

import { UserSchema } from "./users.model";

export default class UserRepo extends layers.BaseTypeormRepository<User> {
    relations = [];
    constructor() {
        super(UserSchema);
    }

    getProfileForUser = (id: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("user")
            .where("user.id = :id", { id })
            .filter(query.filter)
            .leftJoinAndSelect("channel", "channel", "channel.userId = user.id")
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawOne();
    };

    getProfileForUsers = (query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("user")
            .filter(query.filter)
            .leftJoinAndSelect("channel", "channel", "channel.userId = user.id")
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawMany();
    };

    async getDailyAndAggregatedUserRegistrations(
        startDate: Date,
        endDate: Date,
    ) {
        const dailyRegistrations = await this._repo
            .createQueryBuilder("user")
            .select("DATE(user.createdAt)", "date")
            .addSelect("COUNT(*)", "count")
            .where("user.createdAt BETWEEN :start AND :end", {
                start: startDate,
                end: endDate,
            })
            .andWhere("user.role = :role", { role: "user" })
            .groupBy("DATE(user.createdAt)")
            .orderBy("date", "ASC")
            .getRawMany();

        const totalRegistrations = dailyRegistrations.reduce(
            (total: number, day: { count: string }) =>
                total + parseInt(day.count, 10),
            0,
        );

        return {
            dailyRegistrations,
            totalRegistrations,
        };
    }
}
