import express from "express";
import DocsRouter from "../docs";
import UserRouter from "../users";
import CategoryRouter from "../categories";
import staticServerRouter from "../static-server";
import InventoryRouter from "../inventories";
import AutomationRouter from "../automation";
import ChatRouter from "../chat";
import WebsiteRouter from "../websites";
import UserWebsiteRouter from "../user-websites";
import ChannelRouter from "../channels";
import OrderRouter from "../orders";
import RatesRouter from "../rates";
import SubscriptionRouter from "../subscriptions";
import FaqRouter from "../faq";
import AdminRouter from "../admin";
import { metricsEndpoint } from "../common/lib/middlewares/metrics.middleware";
import NotificationRouter from "../notifications";
import HealthRouter from "../health";

const router = express.Router();

router.use("/docs", DocsRouter);
router.use("/users", UserRouter);
router.use("/categories", CategoryRouter);
router.use("/inventories", InventoryRouter);
router.use("/automations", AutomationRouter);
router.use("/chats", ChatRouter);
router.use("/websites", WebsiteRouter);
router.use("/user-websites", UserWebsiteRouter);
router.use("/channels", ChannelRouter);
router.use("/orders", OrderRouter);
router.use("/rates", RatesRouter);
router.use("/static-server", staticServerRouter);
router.use("/subscriptions", SubscriptionRouter);
router.use("/faqs", FaqRouter);
router.use("/admins", AdminRouter);
router.use("/notifications", NotificationRouter);
router.use("/health", HealthRouter);
router.get("/metrics", metricsEndpoint);

// Simple test route
router.get("/test", (_req, res) => {
    res.json({ message: "Server is running!" });
});

export default router;
