import { Channel } from "../../../../channels/apps/channels/types";
import { Client } from "../../../../users/apps/client/types";
import { User } from "../../../../users/apps/users/types";

export interface Chat {
    id: number;
    userId: number;
    clientId: number;
    text: string;
    channelId: number;
    createdAt: string;
    updatedAt: string;
    uniqueKey: string;
    type: "user" | "system" | "ai" | "platform" | "generated";
    user: User;
    client: Client;
    channel: Channel;
}
