import { injectable } from "tsyringe";
import Chat<PERSON><PERSON><PERSON> from "./chat.repo";
import { AddChatHistoryDto } from "./types";
import {
    getChatHistorySerializer,
    getClientHistorySerializer,
} from "./responses";
import { EntityManager } from "typeorm";
import Logger from "../../../common/lib/metrics/logger";
import {
    chatAddHistoryLog,
    chatGetClientHistoryLog,
    chatGetHistoryLog,
} from "../../../common/lib/metrics/metrics";

@injectable()
export default class ChatsService {
    constructor(private _repo: ChatRepo) {}

    async getMessagesCount(query: Partial<Express.Query>, userId: number) {
        const chats = await this._repo.getChatsForAdmin(userId, query);

        return getChatHistorySerializer(chats);
    }

    addChatHistory = async (
        args: AddChatHistoryDto,
        manager: EntityManager,
    ) => {
        const chat = await this._repo.create(args, { manager });

        chatAddHistoryLog.inc();
        Logger.info("Chat history successfully added", {
            action: "addChatHistory",
            chatId: chat.id,
        });

        return chat;
    };

    async getChatHistory(query: Partial<Express.Query>, profile: Express.User) {
        const { id: userId } = profile;

        const history = await this._repo.getHistoryForUser(query, userId);

        chatGetHistoryLog.inc();
        Logger.info("Chat history retrieved successfully", {
            action: "getChatHistory",
            userId,
        });

        return getChatHistorySerializer(history);
    }

    async getClientChatHistory(clientId: number) {
        const history = await this._repo.getClientChatHistory(clientId);

        chatGetClientHistoryLog.inc();
        Logger.info("Client chat history retrieved successfully", {
            action: "getClientChatHistory",
            clientId,
        });

        return getClientHistorySerializer(history);
    }
}
