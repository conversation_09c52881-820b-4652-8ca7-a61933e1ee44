import { EntitySchema } from "typeorm";
import { Chat } from "./types";

export const ChatSchema = new EntitySchema<Chat>({
    name: "chat",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        userId: {
            type: Number,
            nullable: true,
        },
        clientId: {
            type: Number,
            nullable: true,
        },
        text: {
            type: String,
        },
        channelId: {
            type: Number,
        },
        uniqueKey: {
            type: String,
            unique: true,
        },
        type: {
            type: String,
            enum: ["user", "system", "ai", "platform", "generated"],
            default: "user",
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
        },
        client: {
            type: "many-to-one",
            target: "client",
        },
        channel: {
            type: "many-to-one",
            target: "channel",
        },
    },
});
