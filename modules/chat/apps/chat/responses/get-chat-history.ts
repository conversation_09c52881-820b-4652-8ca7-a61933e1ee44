import { JSONSchemaType } from "ajv";
import { GetChatHistory } from "../types";
import { ChatClientSchema } from "./chat-client";

export const GetChatHistoryResponseSchema: JSONSchemaType<GetChatHistory[]> = {
    type: "array",
    items: {
        type: "object",
        prefix: "chat_",
        properties: {
            id: { type: "number" },
            clientId: { type: "number" },
            text: { type: "string" },
            type: { type: "string" },
            channelId: { type: "number" },
            createdAt: { type: "string", format: "date-time" },
            updatedAt: { type: "string", format: "date-time" },
            client: ChatClientSchema,
        },
        required: [],
    },
};
