import { JSONSchemaType } from "ajv";
import { ChatHistory } from "../types";

export const GetClientHistoryResponseSchema: JSONSchemaType<ChatHistory[]> = {
    type: "array",
    items: {
        type: "object",
        prefix: "chat_",
        properties: {
            id: { type: "number" },
            clientId: { type: "number" },
            text: { type: "string" },
            type: { type: "string" },
            channelId: { type: "number" },
            createdAt: { type: "string", format: "date-time" },
            updatedAt: { type: "string", format: "date-time" },
        },
        required: [],
    },
};
