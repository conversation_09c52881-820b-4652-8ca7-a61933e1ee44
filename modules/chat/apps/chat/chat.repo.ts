import { layers } from "../../../common";
import { Chat } from "./types";
import { ChatSchema } from "./chat.model";

const CHAT_HISTORY_LIMIT = 50;

export default class ChatRepo extends layers.BaseTypeormRepository<Chat> {
    relations = [];
    constructor() {
        super(ChatSchema);
    }

    getChatsForAdmin = async (
        userId: number,
        query: Partial<Express.Query>,
    ) => {
        return this._repo
            .createQueryBuilder("chat")
            .where("chat.userId = :userId", { userId })
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    async getHistoryForUser(query: Partial<Express.Query>, userId: number) {
        const result = await this.createQueryBuilder("chat")
            .where("chat.userId = :userId", { userId })
            .filter(query.filter, ...this.relations)
            .leftJoinAndSelect("client", "client", "chat.clientId = client.id")
            .search(query.search, query.searchField, ...this.relations)
            .sort({ id: "DESC" })
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();

        return result;
    }

    async getClientChatHistory(clientId: number) {
        return this.createQueryBuilder("chat")
            .where("chat.clientId = :clientId", { clientId })
            .andWhere("chat.type IN ('user', 'ai')")
            .sort({ id: "DESC" })
            .limit(CHAT_HISTORY_LIMIT)
            .getRawManyAndCount();
    }
}
