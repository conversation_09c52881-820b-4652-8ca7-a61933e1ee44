import { injectable } from "tsyringe";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import ChatService from "./chat.service";
import { GetChatHistoryResponseSchema } from "./responses";

@injectable()
export default class ChatController {
    constructor(private _service: ChatService) {}

    @OpenAPI(
        "chat",
        "/",
        "get",
        undefined,
        undefined,
        GetChatHistoryResponseSchema,
        "bearerAuth",
    )
    getChatHistory = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const chatHistory = await this._service.getChatHistory(
            parsedQuery,
            user!,
        );

        res.success(chatHistory);
    };
}
