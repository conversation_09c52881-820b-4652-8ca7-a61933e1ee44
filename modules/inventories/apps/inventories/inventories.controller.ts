import { injectable } from "tsyringe";
import { OpenAPI } from "../../../common/lib/decorators";
import { AddInventorySchema, EditInventorySchema } from "./schemas";
import InventoryService from "./inventories.service";
import { Request, Response } from "express";
import { GetInventoriesResponseSchema } from "./responses/get-inventories";

@injectable()
export default class InventoryController {
    constructor(private _service: InventoryService) {}

    @OpenAPI(
        "inventories",
        "/",
        "post",
        AddInventorySchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addInventory = async (req: Request, res: Response) => {
        const { user, body } = req;
        await this._service.addInventory(body, user!);
        res.success({});
    };

    @OpenAPI(
        "inventories",
        "/",
        "get",
        undefined,
        undefined,
        GetInventoriesResponseSchema,
        "bearerAuth",
    )
    getInventories = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;
        const inventories = await this._service.getInventories(
            user!,
            parsedQuery,
        );
        res.success(inventories);
    };

    @OpenAPI(
        "inventories",
        "/{id}",
        "patch",
        EditInventorySchema,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editInventory = async (req: Request, res: Response) => {
        const {
            params: { id },
            user,
            body,
        } = req;
        await this._service.editInventory(body, user!, parseInt(id));
        res.success({});
    };

    @OpenAPI(
        "inventories",
        "/{id}",
        "delete",
        undefined,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    deleteInventory = async (req: Request, res: Response) => {
        const {
            user,
            params: { id },
        } = req;
        await this._service.deleteInventory(user!, parseInt(id));
        res.success({});
    };

    @OpenAPI(
        "inventories",
        "/best-selling",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2000-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "limit",
                schema: {
                    type: "string",
                    example: "10",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    getBestSellingProducts = async (req: Request, res: Response) => {
        const { parsedQuery } = req;

        const clients = await this._service.getBestSellingProducts(parsedQuery);

        res.success(clients);
    };

    @OpenAPI(
        "inventories",
        "/sales-profit",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2000-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    getSalesAndProfit = async (req: Request, res: Response) => {
        const { parsedQuery, user } = req;

        const clients = await this._service.getSalesAndProfit(
            parsedQuery,
            user!,
        );

        res.success(clients);
    };

}
