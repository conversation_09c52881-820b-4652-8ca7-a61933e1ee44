import { layers } from "../../../common";
import { InventorySchema } from "./inventories.model";
import { <PERSON><PERSON>ty<PERSON>anager, In } from "typeorm";
import { EditStockDto, Inventory, SearchInventoryDto } from "./types";

export default class InventoryRepo extends layers.BaseTypeormRepository<Inventory> {
    relations = [];

    constructor() {
        super(InventorySchema);
    }

    async getBestSellingProducts(
        startDate: Date,
        endDate: Date,
        pageSize: number = 10,
    ) {
        return this._repo
            .createQueryBuilder("inventory")
            .select("inventory.id", "id")
            .addSelect("inventory.name", "name")
            .addSelect("SUM(orderItems.amount)", "totalSales")
            .leftJoin(
                "order-items",
                "orderItems",
                "orderItems.inventoryId = inventory.id",
            )
            .leftJoin("order", "order", "order.id = orderItems.orderId")
            .where("order.createdAt BETWEEN :startDate AND :endDate", {
                startDate,
                endDate,
            })
            .andWhere("order.status = :status", { status: "PAID" })
            .groupBy("inventory.id")
            .orderBy("SUM(orderItems.amount)", "DESC")
            .limit(pageSize)
            .getRawMany();
    }

    async getSalesAndProfitData(
        startDate: Date,
        endDate: Date,
        userId: number,
    ) {
        return this._repo
            .createQueryBuilder("inventory")
            .select("inventory.id", "id")
            .addSelect("inventory.name", "name")
            .addSelect(
                "SUM(orderItems.amount * orderItems.price)",
                "totalSales",
            )
            .addSelect(
                "SUM(orderItems.amount * (orderItems.price - orderItems.cost))",
                "totalProfit",
            )
            .leftJoin(
                "order-items",
                "orderItems",
                "orderItems.inventoryId = inventory.id",
            )
            .leftJoin("order", "order", "order.id = orderItems.orderId")
            .where("order.createdAt BETWEEN :startDate AND :endDate", {
                startDate,
                endDate,
            })
            .andWhere("order.status = :status", { status: "PAID" })
            .andWhere("inventory.userId = :userId", { userId })
            .groupBy("inventory.id")
            .orderBy(`"totalSales"`, "DESC")
            .getRawMany();
    }

    getInventoriesForUser = (
        userId: number,
        websiteIds: number[],
        query: Partial<Express.Query>,
    ) => {
        const queryBuilder = this._repo
            .createQueryBuilder("inventory")
            .where("inventory.userId = :userId", { userId });

        if (websiteIds.length > 0) {
            queryBuilder.orWhere("inventory.websiteId IN (:...websiteIds)", {
                websiteIds,
            });
        }

        return queryBuilder
            .filter(query.filter)
            .leftJoinAndSelect(
                "category",
                "category",
                "inventory.categoryId = category.id",
            )
            .leftJoinAndSelect(
                "channel",
                "channel",
                "channel.id = inventory.channelId",
            )
            .leftJoinAndSelect(
                "attribute",
                "attribute",
                "attribute.inventoryId = inventory.id",
            )
            .limit(1000)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    async getInventoriesOfUserByIds(
        userId: number,
        ids: number[],
    ): Promise<Inventory[]> {
        return this.findByQuery({ userId, id: In(ids) });
    }

    getInventoriesCountOfUser = async (userId: number) => {
        const count = await this._repo
            .createQueryBuilder("inventory")
            .where("inventory.userId = :userId", { userId })
            .getCount();

        return count;
    };

    editStock = async (inventories: EditStockDto[], manager: EntityManager) => {
        return this.createQueryBuilder("inventory", { manager })
            .insert()
            .into(InventorySchema, [
                "id",
                "name",
                "cost",
                "price",
                "total",
                "reserved",
            ])
            .values(inventories)
            .orUpdate(["total", "reserved"], ["id"])
            .execute();
    };

    searchInventory = async (userId: number, args: SearchInventoryDto) => {
        const minSimilarity = args.infoFa.reduce((acc, { value }) => {
            return acc + value.split(" ").length;
        }, 0);

        const tsQuery = `to_tsquery('simple', '(${args.infoFa
            .map(({ value }) => {
                return value.replaceAll(" ", " & ");
            })
            .join(" & ")}) | (${args.infoEn
            .map(({ value }) => {
                return value.replaceAll(" ", " & ");
            })
            .join(" & ")})')`;

        const query = this._repo
            .createQueryBuilder()
            .select("*")
            .from(
                (qb) =>
                    qb
                        .select("inventory.*")
                        .addSelect(
                            `ts_rank("attributesVector", ${tsQuery}) as rank`,
                        )
                        .from("inventory", "inventory")
                        .leftJoinAndSelect(
                            "attribute",
                            "attribute",
                            "attribute.inventoryId = inventory.id",
                        )
                        .where("inventory.userId = :userId", { userId }),
                "inv",
            )
            .where(`"inv"."rank" > ${minSimilarity / 100}`)
            .orderBy({ "inv.rank": "DESC" });

        return query.getRawMany();
    };
}
