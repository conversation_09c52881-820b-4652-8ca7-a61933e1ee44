export interface GetInventoriesResponse {
    id: number;
    name: string;
    image: string[];
    total: number;
    reserved: number;
    price: string;
    cost: string;
    category: {
        id: number;
        name: string;
    };
    channel: {
        id: number;
        name: string;
    };
    attributes: {
        key: string;
        value: string;
    }[];
    isActive: boolean;
    isAutomated: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface GetMatchingInventoriesResponse {
    id: number;
    name: string;
    price: string;
    attributes: {
        key: string;
        value: string;
    }[];
    image: string[];
}
