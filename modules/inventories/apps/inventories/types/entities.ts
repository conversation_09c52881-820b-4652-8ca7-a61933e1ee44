import { Category } from "../../../../categories/apps/categories/types";
import { Channel } from "../../../../channels/apps/channels/types";
import { User } from "../../../../users/apps/users/types";
import { Website } from "../../../../websites/apps/websites/types";
import { Attribute } from "../../attributes/types/entities";

export interface Inventory {
    id: number;
    referenceId: number;
    categoryId: number;
    category: Category;
    state: "AVAILABLE" | "LOW_STOCK" | "NO_STOCK";
    sku: string;
    userId: number;
    user: User;
    name: string;
    price: string;
    cost: string;
    isActive: boolean;
    isAutomated: boolean;
    reserved: number;
    total: number;
    image: string[];
    url: string;
    attributes: Attribute[];
    channel: Channel;
    channelId: number;
    website: Website;
    websiteId: number;
    attributesVector: string; //Depreciated
    createdAt: Date;
    updatedAt: Date;
}

export interface InventoryEmbedding {
    userId: number;
    description: string;
    geminiEmbedding?: number[];
    openaiEmbedding?: number[]; // We're using this for now
}
