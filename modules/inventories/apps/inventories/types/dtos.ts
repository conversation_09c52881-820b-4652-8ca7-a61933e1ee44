import { AttributeDto } from "../../attributes/types/dtos";
import { Inventory } from "./entities";

export type InventoryItemsDto = Pick<
    Inventory,
    | "categoryId"
    | "name"
    | "price"
    | "total"
    | "isActive"
    | "reserved"
    | "cost"
    | "image"
> & {
    attributes?: AttributeDto[];
};

export type AutomaticInventoryDto = Pick<
    Inventory,
    | "websiteId"
    | "referenceId"
    | "name"
    | "image"
    | "url"
    | "sku"
    | "state"
    | "cost"
> & {
    attributes?: AttributeDto[];
};

export type EditStockDto = Pick<
    Inventory,
    "id" | "name" | "cost" | "price" | "total" | "reserved"
>;

export type SearchInventoryDto = {
    infoFa: AttributeDto[];
    infoEn: AttributeDto[];
};
