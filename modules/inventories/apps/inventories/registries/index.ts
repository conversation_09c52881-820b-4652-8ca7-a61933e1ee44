
import { vectorStoreFactory } from "../../../../common/lib/chatbot/vector-stores/vector-stores-factory";
import {
    EMBEDDING_MODEL,
    VECTOR_INDEX_NAME,
    VECTOR_STORE_PROVIDER,
    VECTOR_TOKEN_NAME,
} from "../../../../common/base/types/typing";
import { embeddingFactory } from "../../../../common/lib/chatbot/llm-factory";

export const registries = [
    {
        token: VECTOR_TOKEN_NAME.INVENTORY_VECTOR_STORE,
        useValue: vectorStoreFactory(
            VECTOR_STORE_PROVIDER.ELASTIC_SEARCH,
            embeddingFactory(process.env.EMBEDDING as EMBEDDING_MODEL),
            {
                index: VECTOR_INDEX_NAME.INVENTORIES,
            },
        ),
    },
];
