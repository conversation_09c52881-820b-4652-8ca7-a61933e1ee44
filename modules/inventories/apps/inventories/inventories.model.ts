import { EntitySchema } from "typeorm";
import { Inventory } from "./types";

export const InventorySchema = new EntitySchema<Inventory>({
    name: "inventory",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        referenceId: {
            type: Number,
            nullable: true,
        },
        sku: { type: String, nullable: true },
        state: {
            type: String,
            enum: ["AVAILABLE", "LOW_STOCK", "NO_STOCK"],
            default: "AVAILABLE",
        },
        name: {
            type: String,
            nullable: false,
        },
        userId: {
            type: Number,
            nullable: true,
        },
        categoryId: {
            type: Number,
            nullable: true,
        },
        price: {
            type: "numeric",
            nullable: true,
        },
        cost: {
            type: "numeric",
            nullable: true,
        },
        image: {
            type: String,
            nullable: true,
            array: true,
        },
        url: {
            type: String,
            nullable: true,
        },
        reserved: {
            type: Number,
            nullable: true,
        },
        total: {
            type: Number,
            nullable: true,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isAutomated: {
            type: Boolean,
            default: false,
        },
        channelId: {
            type: Number,
            nullable: true,
        },
        websiteId: {
            type: Number,
            nullable: true,
        },
        attributesVector: {
            type: "tsvector",
            nullable: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    indices: [
        {
            name: "IDX_UNIQUE_WEBSITE_INVENTORY_SKU",
            unique: true,
            columns: ["websiteId", "referenceId", "sku"],
        },
    ],
    relations: {
        category: {
            type: "many-to-one",
            target: "category",
            nullable: true,
        },
        user: {
            type: "many-to-one",
            target: "user",
            nullable: true,
        },
        channel: {
            type: "many-to-one",
            target: "channel",
            nullable: true,
        },
        website: {
            type: "many-to-one",
            target: "website",
            nullable: true,
        },
        attributes: {
            type: "one-to-many",
            target: "attribute",
            inverseSide: "inventory",
        },
    },
});
