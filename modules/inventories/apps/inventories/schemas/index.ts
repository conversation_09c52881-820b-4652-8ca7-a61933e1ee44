import { validators } from "../../../../common";
import { AddInventorySchema } from "./add-inventory";
import { EditInventorySchema } from "./edit-inventory";

const bv = validators.AJVValidator([AddInventorySchema, EditInventorySchema]);

export * from "./add-inventory";
export * from "./edit-inventory";

export const addInventory = bv("add-inventory");
export const editInventory = bv("edit-inventory");
