import { JSONSchemaType } from "ajv";
import { InventoryItemsDto } from "../types";
import { AttributesSchema } from "../../attributes/schemas";

export const InventorySchema: JSONSchemaType<InventoryItemsDto> = {
    $id: "inventory",
    type: "object",
    properties: {
        categoryId: {
            type: "number",
            example: 1,
        },
        image: {
            type: "array",
            items: {
                type: "string",
            },
            example: ["google.com"],
        },
        name: {
            type: "string",
            example: "bag",
        },
        isActive: {
            type: "boolean",
            example: true,
        },
        reserved: {
            type: "number",
            example: 1,
        },
        total: {
            type: "number",
            example: 10,
        },
        price: {
            type: "string",
            example: "200000",
        },
        cost: {
            type: "string",
            example: "150000",
        },
        attributes: {
            ...AttributesSchema,
            nullable: true,
            example: [
                {
                    key: "color",
                    value: "red",
                },
            ],
        },
    },
    required: [],
    additionalProperties: false,
};
