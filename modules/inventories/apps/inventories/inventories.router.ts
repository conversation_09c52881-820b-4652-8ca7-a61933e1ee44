import express from "express";
import { container } from "tsyringe";
import { middlewares } from "../../../common";
import InventoryController from "./inventories.controller";
import * as schemas from "./schemas";
import { checkRole } from "../../../common/lib/middlewares";

const { JWT } = middlewares;

const router = express.Router();

const controller = container.resolve(InventoryController);

router
    .route("/")
    .post(JWT, schemas.addInventory, controller.addInventory)
    .get(JWT, controller.getInventories);

router
    .route("/:id")
    .patch(JWT, schemas.editInventory, controller.editInventory)
    .delete(JWT, controller.deleteInventory);

router
    .route("/sales-profit")
    .get(middlewares.JWT, checkRole(["user"]), controller.getSalesAndProfit);

router
    .route("/best-selling")
    .get(
        middlewares.JWT,
        checkRole(["user"]),
        controller.getBestSellingProducts,
    );

export default router;
