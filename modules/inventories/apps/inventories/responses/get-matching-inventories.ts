import { JSONSchemaType } from "ajv";
import { GetMatchingInventoriesResponse } from "../types";

export const GetMatchingInventoriesResponseSchema: JSONSchemaType<
    GetMatchingInventoriesResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "",
        properties: {
            id: {
                type: "number",
            },
            name: {
                type: "string",
            },
            price: {
                type: "string",
            },
            attributes: {
                type: "array",
                items: {
                    type: "object",
                    prefix: "attribute_",
                    properties: {
                        key: {
                            type: "string",
                        },
                        value: {
                            type: "string",
                        },
                    },
                    required: [],
                },
            },
            image: {
                type: "array",
                items: {
                    type: "string",
                },
                ignore: true,
            },
        },
        required: [],
    },
};
