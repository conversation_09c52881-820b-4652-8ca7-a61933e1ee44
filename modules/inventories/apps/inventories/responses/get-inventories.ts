import { JSONSchemaType } from "ajv";
import { GetInventoriesResponse } from "../types";

export const GetInventoriesResponseSchema: JSONSchemaType<
    GetInventoriesResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "inventory_",
        properties: {
            id: {
                type: "number",
            },
            name: {
                type: "string",
            },
            reserved: {
                type: "number",
            },
            total: {
                type: "number",
            },
            image: {
                type: "array",
                items: {
                    type: "string",
                },
                ignore: true,
            },
            isActive: {
                type: "boolean",
            },
            isAutomated: {
                type: "boolean",
            },
            price: {
                type: "string",
            },
            cost: {
                type: "string",
            },
            attributes: {
                type: "array",
                items: {
                    type: "object",
                    prefix: "attribute_",
                    properties: {
                        key: {
                            type: "string",
                        },
                        value: {
                            type: "string",
                        },
                    },
                    required: [],
                },
            },
            category: {
                type: "object",
                prefix: "category_",
                properties: {
                    id: {
                        type: "number",
                    },
                    name: {
                        type: "string",
                    },
                },
                required: [],
            },
            channel: {
                type: "object",
                prefix: "channel_",
                properties: {
                    id: {
                        type: "number",
                    },
                    name: {
                        type: "string",
                    },
                },
                required: [],
            },
            createdAt: {
                type: "string",
            },
            updatedAt: {
                type: "string",
            },
        },
        required: [],
    },
};
