import { EntitySchema } from "typeorm";
import { Attribute } from "./types";

export const AttributeSchema = new EntitySchema<Attribute>({
    name: "attribute",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        inventoryId: {
            type: Number,
        },
        key: {
            type: String,
        },
        value: {
            type: String,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        inventory: {
            type: "many-to-one",
            target: "inventory",
            inverseSide: "attributes",
            joinColumn: {
                name: "inventoryId",
                referencedColumnName: "id"
            }
        },
    },
});
