import { JSONSchemaType } from "ajv";
import { AttributeDto } from "../types";

export const AttributesSchema: JSONSchemaType<AttributeDto[]> = {
    $id: "attribute",
    type: "array",
    items: {
        type: "object",
        properties: {
            key: {
                type: "string",
            },
            value: {
                type: "string",
            },
        },
        required: [],
        additionalProperties: false,
    },
    minItems: 1,
};
