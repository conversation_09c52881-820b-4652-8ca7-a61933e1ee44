import { injectable } from "tsyringe";
import AttributeRepo from "./attribute.repo";
import { AttributeDto } from "./types";
import { EntityManager } from "typeorm";

@injectable()
export default class AttributeService {
    constructor(private _repo: AttributeRepo) {}

    resetAttributes = async (
        inventoryId: number,
        attributes: AttributeDto[],
        manager: EntityManager,
    ) => {
        await this._repo.deleteByQuery({ inventoryId }, { manager });
        await this._repo.bulkCreate(
            attributes.map((attribute) => ({
                inventoryId,
                ...attribute,
            })),
            { manager },
        );
    };

    removeAttributes = async (inventoryId: number, manager: EntityManager) => {
        await this._repo.deleteByQuery({ inventoryId }, { manager });
    };
}
