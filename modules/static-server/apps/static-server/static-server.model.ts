import { EntitySchema } from "typeorm";
import { File } from "./types";

export const FileSchema = new EntitySchema<File>({
    name: "file",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        path: {
            type: String,
        },
        userId: {
            type: Number,
            nullable: true,
        },
        isGlobal: {
            type: Boolean,
            default: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
        },
    },
});
