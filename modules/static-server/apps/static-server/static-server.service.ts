import { injectable } from "tsyringe";
import mime from "mime";
import { Response } from "express";
import sharp from "sharp";
import stream, { Readable } from "stream";

import { errors, utils } from "../../../common";
import { FileUploaderFactory, ImageStrategy } from "./strategies";
import StaticServerRepo from "./static-server.repo";
import { AddFileDto } from "./types";

@injectable()
export default class StaticServerService {
    constructor(private _repo: StaticServerRepo) {}

    upload = async (
        args: AddFileDto,
        file: Express.Multer.File,
        profile: Express.User,
    ) => {
        const userId = profile?.id;

        const strategy = FileUploaderFactory.createUploader(file.mimetype);
        if (!strategy?.getStrategy()) {
            throw new errors.BadRequestError();
        }

        const path = await strategy.upload(file);

        const storedFile = await this._repo.create({
            userId,
            path,
            isGlobal: args.isGlobal === "true",
        });

        return storedFile.path;
    };

    async getFileWithoutRes(
        path: string,
        filter: { width?: number; height?: number },
        profile?: Express.User,
    ): Promise<Readable> {
        const fileConfig = await this._repo.findOneByQuery({
            path: `${path}`,
        });

        if (utils.isNil(fileConfig)) {
            throw new errors.NotFoundError();
        }

        if (!fileConfig.isGlobal && fileConfig.userId !== profile?.id) {
            throw new errors.NotFoundError();
        }

        const { response, fileType } = await this._requestFile(path);
        if (!response.ok) {
            throw new errors.NotFoundError();
        }

        const readable = Readable.from(response.body!);

        if (!new ImageStrategy().getMimeTypes().includes(fileType)) {
            return readable;
        }

        const { width, height } = filter;
        const transform = sharp();

        if (utils.isNotNil(width) || utils.isNotNil(height)) {
            transform.resize({
                width,
                height,
                withoutEnlargement: true,
            });
        }

        return readable.pipe(transform);
    }

    getFile = async (
        path: string,
        filter: { width?: number; height?: number },
        res: Response,
        profile?: Express.User,
    ) => {
        const fileConfig = await this._repo.findOneByQuery({
            path: `/${path}`,
        });
        if (utils.isNil(fileConfig)) {
            throw new errors.NotFoundError();
        }

        if (!fileConfig.isGlobal) {
            if (fileConfig.userId !== profile?.id) {
                throw new errors.NotFoundError();
            }
        }

        const { response, fileType } = await this._requestFile(path);

        const { width, height } = filter;

        const contentType = response.headers.get("content-type");
        res.setHeader("content-type", contentType!);
        if (!response.ok) {
            throw new errors.NotFoundError();
        }

        const readable = stream.Readable.from(response.body!);
        if (!new ImageStrategy().getMimeTypes().includes(fileType)) {
            return readable.pipe(res);
        }
        const transform = sharp();
        if (utils.isNotNil(width) || utils.isNotNil(height)) {
            transform.resize({
                width,
                height,
                withoutEnlargement: true,
            });
        }
        return readable.pipe(transform).pipe(res);
    };

    private _requestFile = async (path: string) => {
        const fileUrl = `${process.env.FILER_URL}/${path}`;
        const fileType = mime.lookup(path.split("/").pop()!);

        try {
            const response = await fetch(fileUrl);
            return { response, fileType };
        } catch (e) {
            throw new errors.NotFoundError("File");
        }
    };
}
