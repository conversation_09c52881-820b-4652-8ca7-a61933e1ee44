import { Request, Response } from "express";
import { injectable } from "tsyringe";

import StaticServerService from "./static-server.service";
import { OpenAPI } from "../../../common/lib/decorators";
import { AddFileSchema } from "./schemas";

@injectable()
export default class StaticServerController {
    constructor(private _service: StaticServerService) {}

    @OpenAPI(
        "static-server",
        "/",
        "post",
        AddFileSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addFile = async (req: Request, res: Response) => {
        const { user, file, body } = req;

        const path = await this._service.upload(body, file!, user!);

        res.success({ path });
    };

    @OpenAPI(
        "static-server",
        "/{path}",
        "get",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    getFile = async (req: Request, res: Response) => {
        const { user, params, parsedQuery } = req;

        const path = params["0"];

        await this._service.getFile(
            path,
            parsedQuery.filter as { width?: number; height?: number },
            res,
            user,
        );
    };
}
