import express from "express";
import { container } from "tsyringe";

import StaticServerController from "./static-server.controller";
import multer from "multer";
import { middlewares } from "../../../common";

import * as schemas from "./schemas";

const storage = multer.memoryStorage();
const upload = multer({ storage });

const router = express.Router();

const controller = container.resolve(StaticServerController);

router
    .route("/")
    .post(
        middlewares.JWT,
        upload.single("file"),
        schemas.addFile,
        controller.addFile,
    );

router.route("/*").get(middlewares.JWTExists, controller.getFile);

export default router;
