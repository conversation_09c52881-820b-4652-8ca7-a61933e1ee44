import { JSONSchemaType } from "ajv";
import { AddFileDto } from "../types";

export const AddFileSchema: JSONSchemaType<AddFileDto> = {
    $id: "add-file",
    type: "object",
    properties: {
        isGlobal: {
            type: "string",
            enum: ["false", "true"],
        },
        file: {
            type: "string",
            format: "binary",
        },
    },
    required: ["isGlobal"],
    additionalProperties: false,
};
