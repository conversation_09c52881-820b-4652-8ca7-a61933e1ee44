import axios from "axios";
import FormData from "form-data";
import { v4 as uuidv4 } from "uuid";
import { errors } from "../../../../common";

export default class Uploader {
    private _maxSize: number;
    private _mimeTypes: string[];
    private _folderName: string;

    constructor(args: {
        maxSize: number;
        mimeTypes: string[];
        folderName: string;
    }) {
        this._maxSize = args.maxSize;
        this._mimeTypes = args.mimeTypes;
        this._folderName = args.folderName;
    }

    getStrategy() {
        return this;
    }

    getMaxSize() {
        return this._maxSize;
    }

    getMimeTypes() {
        return this._mimeTypes;
    }

    getFolderName() {
        return this._folderName;
    }

    isValidSize(file: Express.Multer.File) {
        return file.size <= this._maxSize;
    }

    isValidType(file: Express.Multer.File) {
        return this.getMimeTypes().includes(file.mimetype);
    }

    async upload(file: Express.Multer.File) {
        if (!this.isValidType(file)) {
            throw new errors.BadRequestError();
        }

        if (!this.isValidSize(file)) {
            throw new errors.BadRequestError();
        }
        const form = new FormData();
        form.append("file", file.buffer);
        const folderName = this.getFolderName();
        const uploadFolder = `${folderName}/${new Date().toISOString().split("T")[0].replaceAll("-", "/")}`;
        const fileName =
            file.fieldname +
            "-" +
            uuidv4() +
            file.originalname.slice(file.originalname.lastIndexOf("."));
        await axios.post(
            `${process.env.FILER_URL}/${uploadFolder}/${fileName}`,
            form,
            {
                headers: form.getHeaders(),
            },
        );

        return `/${uploadFolder}/${fileName}`;
    }
}

module.exports = Uploader;
