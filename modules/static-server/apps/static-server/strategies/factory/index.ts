import { ImageStrategy, PDFStrategy } from "../strategies";

export class FileUploaderFactory {
    static createUploader(fileType: string) {
        let strategy = null;

        if (new ImageStrategy().getMimeTypes().includes(fileType)) {
            strategy = new ImageStrategy();
        }
        if (new PDFStrategy().getMimeTypes().includes(fileType)) {
            strategy = new PDFStrategy();
        }

        return strategy;
    }
}
