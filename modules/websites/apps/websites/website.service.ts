import { injectable } from "tsyringe";
import WebsiteRepo from "./website.repo";
import { AddWebsiteDto } from "./types";
import { getWebsitesSerializer } from "./responses";
import { isNil } from "lodash";
import { errors, utils } from "../../../common";

@injectable()
export default class WebsiteService {
    constructor(private _repo: WebsiteRepo) {}

    addWebsite = async (body: AddWebsiteDto) => {
        await this._repo.create(body);
    };

    getWebsites = async (
        profile: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const websites = await this._repo.getWebsites(profile.id, parsedQuery);
        return getWebsitesSerializer(websites);
    };

    getWebsite = async (id: number) => {
        const website = await this._repo.findOneByQuery({ id });

        if (isNil(website)) {
            throw new errors.NotFoundError("website");
        }
    };

    getWebsiteByName = async (name: string) => {
        const website = await this._repo.findOneByQuery({ name });

        if (isNil(website)) {
            throw new errors.NotFoundError("website");
        }

        return website;
    };

    getWebsitesByIds = async (ids: number[]) => {
        const websites = await this._repo.getWebsitesByIds(ids);
        if (websites.length !== ids.length) {
            throw new errors.NotFoundError("Website");
        }

        return websites;
    };

    getWebsitesById = async (id: number) => {
        const website = await this._repo.findById(id);
        if (utils.isNil(website)) {
            throw new errors.NotFoundError("Website");
        }

        return website;
    };
}
