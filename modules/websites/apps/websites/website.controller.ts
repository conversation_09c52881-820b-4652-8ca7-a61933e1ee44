import { injectable } from "tsyringe";
import WebsiteService from "./website.service";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import { AddWebsiteSchema } from "./schemas";
import { GetWebsitesResponseSchema } from "./responses";

@injectable()
export default class WebsiteController {
    constructor(private _service: WebsiteService) {}

    @OpenAPI(
        "websites",
        "/",
        "post",
        AddWebsiteSchema,
        undefined,
        undefined,
        undefined,
    )
    addWebsite = async (req: Request, res: Response) => {
        const { body } = req;

        await this._service.addWebsite(body);

        res.success({});
    };

    @OpenAPI(
        "websites",
        "/",
        "get",
        undefined,
        undefined,
        GetWebsitesResponseSchema,
        "bearerAuth",
    )
    getWebsites = async (req: Request, res: Response) => {
        const { parsedQuery, user } = req;

        const websites = await this._service.getWebsites(user!, parsedQuery);

        res.success(websites);
    };
}
