import { In } from "typeorm";
import { layers } from "../../../common";
import { Website } from "./types";
import { WebsiteSchema } from "./website.model";

export default class WebsiteRepo extends layers.BaseTypeormRepository<Website> {
    relations = [];
    constructor() {
        super(WebsiteSchema);
    }

    getWebsites = (userId: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("website")
            .addSelect(
                `CASE WHEN "user-website"."id" IS NOT NULL THEN true ELSE false END as "isEnabled"`,
            )
            .leftJoinAndSelect(
                "user-website",
                "user-website",
                "user-website.websiteId = website.id AND user-website.userId = :userId",
                { userId },
            )
            .filter(query.filter)
            .search(query.search, query.searchField)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getWebsitesByIds = (ids: number[]) => {
        return this.findByQuery({
            id: In(ids),
        });
    };
}
