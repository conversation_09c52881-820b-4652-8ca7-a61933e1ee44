import { JSONSchemaType } from "ajv";
import { GetWebsiteResponse } from "../types/schemas";

export const GetWebsitesResponseSchema: JSONSchemaType<GetWebsiteResponse[]> = {
    type: "array",
    items: {
        type: "object",
        prefix: "website_",
        properties: {
            id: {
                type: "number",
            },
            name: {
                type: "string",
            },
            link: {
                type: "string",
            },
            isActive: {
                type: "boolean",
            },
            isEnabled: {
                type: "boolean",
                prefix: "",
            },
            createdAt: {
                type: "string",
            },
            updatedAt: {
                type: "string",
            },
        },
        required: [],
    },
};
