import { EntitySchema } from "typeorm";
import { Website } from "./types";

export const WebsiteSchema = new EntitySchema<Website>({
    name: "website",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        name: {
            type: String,
        },
        link: {
            type: String,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {},
});
