import express from "express";
import { container } from "tsyringe";
import * as schemas from "./schemas";
import WebsiteController from "./website.controller";
import { middlewares } from "../../../common/lib";

const router = express.Router();

const controller = container.resolve(WebsiteController);

router
    .route("/")
    .post(schemas.addWebsite, controller.addWebsite)
    .get(middlewares.JWT, controller.getWebsites);

export default router;
