import { JSONSchemaType } from "ajv";
import { AddWebsiteDto } from "../types";

export const AddWebsiteSchema: JSONSchemaType<AddWebsiteDto> = {
    $id: "add-website",
    type: "object",
    properties: {
        name: {
            type: "string",
            example: "zara",
        },
        link: {
            type: "string",
            example: "https://www.zara.com/de/en/",
        },
        isActive: {
            type: "boolean",
            default: true,
        },
    },
    required: ["name", "link"],
    additionalProperties: false,
};
