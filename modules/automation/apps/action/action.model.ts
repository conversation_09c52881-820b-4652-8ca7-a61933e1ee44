import { EntitySchema } from "typeorm";
import { Action } from "./types";

export const ActionSchema = new EntitySchema<Action>({
    name: "action",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        automationId: {
            type: Number,
        },
        type: {
            type: String,
            enum: [
                "text",
                "enhanced-text",
                "file",
                "button",
                "form",
                "showcase",
            ],
        },
        text: {
            type: String,
            nullable: true,
        },
        file: {
            type: String,
            nullable: true,
        },
        url: {
            type: String,
            nullable: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        automation: {
            type: "many-to-one",
            target: "automation",
        },
    },
});
