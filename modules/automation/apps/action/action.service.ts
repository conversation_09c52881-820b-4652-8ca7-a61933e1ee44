import { injectable } from "tsyringe";
import { errors, utils } from "../../../common";
import ActionRepo from "./action.repo";
import { ActionDto } from "./types";
import { EntityManager } from "typeorm";
import {
    actionDeleteLog,
    actionResetLog,
    actionValidateLog,
    actionValidationErrorLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";
import Logger from "../../../common/lib/metrics/logger";

@injectable()
export default class ActionsService {
    constructor(private _repo: ActionRepo) {}

    validateActions = (actions: ActionDto[]) => {
        for (const action of actions) {
            const { type } = action;
            if (type === "text" || type === "enhanced-text") {
                if (utils.isNil(action.text)) {
                    Logger.error("Action validation failed", {
                        actionType: type,
                        action: "validateActions",
                    });
                    actionValidationErrorLog.inc();
                    recordErrorValue("ValidationError", "Action not found");

                    throw new errors.BadRequestError();
                }
            } else if (type === "file") {
                if (utils.isNil(action.file)) {
                    Logger.error("Action validation failed", {
                        actionType: type,
                        action: "validateActions",
                    });
                    actionValidationErrorLog.inc();
                    recordErrorValue("ValidationError", "Action not found");

                    throw new errors.BadRequestError();
                }
            } else if (type === "button") {
                if (utils.isNil(action.text)) {
                    actionValidationErrorLog.inc();
                    Logger.error("Action validation failed", {
                        actionType: type,
                        action: "validateActions",
                    });
                    recordErrorValue("ValidationError", "Action not found");

                    throw new errors.BadRequestError();
                }
            }
            // TODO: Implement
            // else if (type === "form") {
            //     if (utils.isNil(action.file)) {
            //         throw new errors.BadRequestError();
            //     }
            // } else if (type === "showcase") {
            //     if (utils.isNil(action.file)) {
            //         throw new errors.BadRequestError();
            //     }
            // }
        }

        actionValidateLog.inc();
        Logger.info("Actions validated successfully", {
            action: "validateActions",
        });
    };

    resetAction = async (
        actions: ActionDto[],
        automationId: number,
        manager: EntityManager,
    ) => {
        await this._repo.deleteByQuery(
            {
                automationId,
            },
            { manager },
        );

        await this._repo.bulkCreate(
            actions.map((action) => ({
                ...action,
                automationId,
            })),
            { manager },
        );

        actionResetLog.inc();
        Logger.info("Actions reset successfully", {
            automationId,
            action: "resetAction",
        });
    };

    removeActions = async (automationId: number, manager: EntityManager) => {
        await this._repo.deleteByQuery(
            {
                automationId,
            },
            { manager },
        );

        actionDeleteLog.inc();
        Logger.info("Actions deleted successfully", {
            automationId,
            action: "removeActions",
        });
    };

    processAction = (action: ActionDto) => {
        Logger.info("Processing action", { actionType: action.type });

        if (action.type === "enhanced-text") {
            return action.text!;
        } else if (action.type === "text") {
            return action.text!;
        }
        // TODO: Implement
        // else if (type === "file") {
        // } else if (type === "button") {
        // }
        // else if (type === "form") {
        //     if (utils.isNil(action.file)) {
        //         throw new errors.BadRequestError();
        //     }
        // } else if (type === "showcase") {
        //     if (utils.isNil(action.file)) {
        //         throw new errors.BadRequestError();
        //     }
        // }
    };
}
