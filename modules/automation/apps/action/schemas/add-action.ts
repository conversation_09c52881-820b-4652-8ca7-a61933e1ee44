import { JSONSchemaType } from "ajv";
import { ActionDto } from "../types";

export const AddActionsSchema: JSONSchemaType<ActionDto[]> = {
    $id: "add-action",
    type: "array",
    items: {
        type: "object",
        properties: {
            type: {
                type: "string",
                enum: [
                    "text",
                    "enhanced-text",
                    "file",
                    "button",
                    "form",
                    "showcase",
                ],
            },
            text: {
                type: "string",
                example: "action response 1",
                nullable: true,
            },
            file: {
                type: "string",
                example: "path/to/your/file",
                nullable: true,
            },
            url: {
                type: "string",
                nullable: true,
            },
        },
        required: ["type"],
    },
};
