import { JSONSchemaType } from "ajv";
import { ActionResponse } from "../types";

export const ActionsResponseSchema: JSONSchemaType<ActionResponse[]> = {
    type: "array",
    uniqueId: "id",
    items: {
        type: "object",
        prefix: "action_",
        properties: {
            id: { type: "number" },
            type: {
                type: "string",
            },
            text: {
                type: "string",
                nullable: true,
            },
            file: {
                type: "string",
                nullable: true,
            },
            url: {
                type: "string",
                nullable: true,
            },
        },
        required: [],
    },
};
