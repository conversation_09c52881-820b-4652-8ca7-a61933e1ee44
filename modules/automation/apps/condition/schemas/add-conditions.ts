import { JSONSchemaType } from "ajv";
import { ConditionDto } from "../types";

export const AddConditionSchema: JSONSchemaType<ConditionDto[]> = {
    $id: "add-condition",
    type: "array",
    items: {
        type: "object",
        properties: {
            keyword: {
                type: "string",
                example: "bag",
                nullable: true,
            },
            type: {
                type: "string",
                enum: ["contain", "exactly_equal", "followed"],
            },
        },
        required: ["type"],
    },
};
