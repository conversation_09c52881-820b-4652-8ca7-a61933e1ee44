import { EntitySchema } from "typeorm";
import { Condition } from "./types";

export const ConditionSchema = new EntitySchema<Condition>({
    name: "condition",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        keyword: {
            type: String,
            nullable: true,
        },
        automationId: {
            type: Number,
        },
        type: {
            type: String,
            enum: ["followed", "exactly_equal", "contain"],
        },
    },
    relations: {
        automation: {
            type: "many-to-one",
            target: "automation",
        },
    },
});
