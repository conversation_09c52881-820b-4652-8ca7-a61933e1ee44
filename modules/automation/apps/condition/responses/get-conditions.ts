import { JSONSchemaType } from "ajv";
import { ConditionsResponse } from "../types";

export const ConditionsResponseSchema: JSONSchemaType<ConditionsResponse[]> = {
    type: "array",
    uniqueId: "id",
    items: {
        type: "object",
        prefix: "condition_",
        properties: {
            id: { type: "number" },
            keyword: {
                type: "string",
                nullable: true,
            },
            type: {
                type: "string",
            },
        },
        required: [],
    },
};
