import { JSONSchemaType } from "ajv";
import { AutomationDto } from "../types";
import { AddConditionSchema } from "../../condition/schemas";
import { AddActionsSchema } from "../../action/schemas";

export const AutomationSchema: JSONSchemaType<AutomationDto> = {
    $id: "automation",
    type: "object",
    properties: {
        isActive: {
            type: "boolean",
            example: true,
        },
        conditions: AddConditionSchema,
        actions: AddActionsSchema,
    },
    required: [],
    additionalProperties: false,
};
