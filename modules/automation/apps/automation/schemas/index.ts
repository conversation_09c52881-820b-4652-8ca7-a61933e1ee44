import { validators } from "../../../../common";

import { AddAutomationSchema } from "./add-automation";
import { EditAutomationSchema } from "./edit-automation";

const bv = validators.AJVValidator([AddAutomationSchema, EditAutomationSchema]);

export * from "./add-automation";
export * from "./edit-automation";

export const addAutomation = bv("add-automation");
export const editAutomation = bv("edit-automation");
