import { paginatedSerializer, serializer } from "../../../../common/lib/utils";
import { GetAutomationResponseSchema } from "./get-automation";
import { GetAutomationsResponseSchema } from "./get-automations";

export const getAutomationSerializer = serializer(GetAutomationResponseSchema);

export const getAutomationsSerializer = paginatedSerializer(
    GetAutomationsResponseSchema,
);

export const getUserAutomationsSerializer = serializer(
    GetAutomationsResponseSchema,
);

export * from "./get-automation";
export * from "./get-automations";
