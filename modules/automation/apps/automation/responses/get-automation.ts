import { JSONSchemaType } from "ajv";
import { GetAutomationResponse } from "../types";
import { ConditionsResponseSchema } from "../../condition/responses";
import { ActionsResponseSchema } from "../../action/responses";

export const GetAutomationResponseSchema: JSONSchemaType<GetAutomationResponse> =
    {
        type: "object",
        prefix: "automation_",
        properties: {
            id: {
                type: "number",
            },
            userId: {
                type: "number",
            },
            isActive: {
                type: "boolean",
            },
            createdAt: {
                type: "string",
            },
            updatedAt: {
                type: "string",
            },
            conditions: ConditionsResponseSchema,
            actions: ActionsResponseSchema,
        },
        required: [],
    };
