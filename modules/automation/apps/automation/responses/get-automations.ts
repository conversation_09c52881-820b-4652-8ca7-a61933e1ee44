import { JSONSchemaType } from "ajv";
import { ConditionsResponseSchema } from "../../condition/responses";
import { GetAutomationResponse } from "../types";
import { ActionsResponseSchema } from "../../action/responses";

export const GetAutomationsResponseSchema: JSONSchemaType<
    GetAutomationResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "automation_",
        properties: {
            id: {
                type: "number",
            },
            userId: {
                type: "number",
            },
            isActive: {
                type: "boolean",
            },
            createdAt: {
                type: "string",
            },
            updatedAt: {
                type: "string",
            },
            conditions: ConditionsResponseSchema,
            actions: ActionsResponseSchema,
        },
        required: [],
    },
};
