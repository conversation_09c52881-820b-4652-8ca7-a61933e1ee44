import express from "express";
import { container } from "tsyringe";
import AutomationController from "./automation.controller";
import * as schemas from "./schemas";
import { middlewares } from "../../../common";

const router = express.Router();

const controller = container.resolve(AutomationController);

router
    .route("/")
    .get(middlewares.JWT, controller.getAutomations)
    .post(middlewares.JWT, schemas.addAutomation, controller.addAutomation);

router
    .route("/:id")
    .get(middlewares.JWT, controller.getAutomation)
    .put(middlewares.JWT, schemas.editAutomation, controller.editAutomation)
    .delete(middlewares.JWT, controller.deleteAutomation);

export default router;
