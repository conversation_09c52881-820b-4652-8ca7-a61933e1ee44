import { injectable } from "tsyringe";
import { OpenAPI } from "../../../common/lib/decorators";
import { Request, Response } from "express";

import AutomationService from "./automation.service";
import { AddAutomationSchema, EditAutomationSchema } from "./schemas";
import {
    GetAutomationResponseSchema,
    GetAutomationsResponseSchema,
} from "./responses";

@injectable()
export default class AutomationController {
    constructor(private _service: AutomationService) {}

    @OpenAPI(
        "automations",
        "/",
        "post",
        AddAutomationSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addAutomation = async (req: Request, res: Response) => {
        const { body, user } = req;

        await this._service.addAutomation(body, user!);

        res.success({});
    };

    @OpenAPI(
        "automations",
        "/",
        "get",
        undefined,
        undefined,
        GetAutomationsResponseSchema,
        "bearerAuth",
    )
    getAutomations = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const automation = await this._service.getAutomations(
            user!,
            parsedQuery,
        );

        res.success(automation);
    };

    @OpenAPI(
        "automations",
        "/{id}",
        "get",
        undefined,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        GetAutomationResponseSchema,
        "bearerAuth",
    )
    getAutomation = async (req: Request, res: Response) => {
        const {
            params: { id },
            user,
        } = req;

        const automation = await this._service.getAutomation(Number(id), user!);

        res.success(automation);
    };

    @OpenAPI(
        "automations",
        "/{id}",
        "put",
        EditAutomationSchema,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editAutomation = async (req: Request, res: Response) => {
        const {
            params: { id },
            body,
            user,
        } = req;

        await this._service.editAutomation(body, parseInt(id), user!);

        res.success({});
    };

    @OpenAPI(
        "automations",
        "/{id}",
        "delete",
        undefined,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    deleteAutomation = async (req: Request, res: Response) => {
        const {
            params: { id },
            user,
        } = req;

        await this._service.deleteAutomation(parseInt(id), user!);

        res.success({});
    };
}
