import { injectable } from "tsyringe";

import AutomationRepo from "./automation.repo";
import ConditionsService from "../condition/condition.service";
import ActionService from "../action/action.service";

import { errors, utils } from "../../../common";
import { <PERSON><PERSON>tyManager } from "typeorm";
import {
    AutomationDto,
    GetAutomationResponse,
    ProcessAutomationDto,
} from "./types";
import {
    getAutomationSerializer,
    getAutomationsSerializer,
    getUserAutomationsSerializer,
} from "./responses";

@injectable()
export default class AutomationsService {
    constructor(
        private _repo: AutomationRepo,
        private _conditionService: ConditionsService,
        private _actionService: ActionService,
    ) {}

    private _findMatchingAutomation = (
        automations: GetAutomationResponse[],
        text: string,
    ) => {
        for (const automation of automations) {
            const { conditions } = automation;
            const results = conditions.map((condition) =>
                this._conditionService.processCondition(condition, text),
            );

            const isMatch = results.every((result) => result);
            if (isMatch) {
                return automation;
            }
        }
    };

    async processAutomation(body: ProcessAutomationDto) {
        const { userId, text } = body;

        const rawAutomations = await this._repo.getUserAutomations(userId);
        const automations = getUserAutomationsSerializer(rawAutomations);

        const automation = this._findMatchingAutomation(automations, text);

        if (utils.isNil(automation)) {
            return [];
        }

        return automation.actions.map(this._actionService.processAction);
    }

    addAutomation = async (args: AutomationDto, profile: Express.User) => {
        const { id: userId } = profile;
        const { conditions, actions } = args;

        await Promise.all([
            this._conditionService.validateConditions(conditions),
            this._actionService.validateActions(actions),
        ]);

        await this._repo.runTransaction(async (manager: EntityManager) => {
            const automation = await this._repo.create({ userId }, { manager });

            await Promise.all([
                this._conditionService.resetConditions(
                    conditions,
                    automation.id,
                    manager,
                ),
                this._actionService.resetAction(
                    actions,
                    automation.id,
                    manager,
                ),
            ]);
        });
    };

    getAutomations = async (
        profile: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = profile;

        const automations = await this._repo.getAutomations(
            userId,
            parsedQuery,
        );

        return getAutomationsSerializer(automations);
    };

    getAutomation = async (id: number, profile: Express.User) => {
        const { id: userId } = profile;

        const automation = await this._repo.getAutomation(id, userId);
        if (
            utils.isNil(automation) ||
            (Array.isArray(automation) && automation.length === 0)
        ) {
            throw new errors.NotFoundError("Automation");
        }

        return getAutomationSerializer(automation);
    };

    editAutomation = async (
        args: AutomationDto,
        id: number,
        profile: Express.User,
    ) => {
        const { id: userId } = profile;
        const { conditions, actions } = args;

        const automation = await this.getRawAutomationOfUser(id, userId);

        await Promise.all([
            this._conditionService.validateConditions(conditions),
            this._actionService.validateActions(actions),
        ]);

        await this._repo.runTransaction(async (manager: EntityManager) => {
            await Promise.all([
                this._conditionService.resetConditions(
                    conditions,
                    automation.id,
                    manager,
                ),
                this._actionService.resetAction(
                    actions,
                    automation.id,
                    manager,
                ),
            ]);
        });
    };

    deleteAutomation = async (id: number, profile: Express.User) => {
        const { id: userId } = profile;

        const automation = await this.getRawAutomationOfUser(id, userId);

        await this._repo.runTransaction(async (manager: EntityManager) => {
            await Promise.all([
                this._conditionService.removeConditions(automation.id, manager),
                this._actionService.removeActions(automation.id, manager),
            ]);

            await this._repo.deleteById(automation.id, { manager });
        });
    };

    getRawAutomationOfUser = async (id: number, userId: number) => {
        const automation = await this._repo.findOneByQuery({ id, userId });
        if (utils.isNil(automation)) {
            throw new errors.NotFoundError("Automation");
        }

        return automation;
    };
}
