import express from "express";
import { container } from "tsyringe";
import { middlewares } from "../../../common";
import UserWebsiteRouter from "./user-website.controller";
import * as schemas from "./schemas";

const { JWT } = middlewares;

const router = express.Router();

const controller = container.resolve(UserWebsiteRouter);

router
    .route("/")
    .post(JWT, schemas.addUserWebsite, controller.toggleUserWebsite);

export default router;
