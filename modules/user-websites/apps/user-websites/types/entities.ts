import { Channel } from "../../../../channels/apps/channels/types";
import { User } from "../../../../users/apps/users/types";
import { Website } from "../../../../websites/apps/websites/types";

export interface UserWebsite {
    id: number;
    userId: number;
    user: User;
    websiteId: number;
    website: Website;
    channel: Channel;
    channelId: number;
    createdAt: Date;
    updatedAt: Date;
}
