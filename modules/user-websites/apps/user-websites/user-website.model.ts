import { EntitySchema } from "typeorm";
import { UserWebsite } from "./types";

export const UserWebsiteSchema = new EntitySchema<UserWebsite>({
    name: "user-website",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        userId: {
            type: Number,
        },
        websiteId: {
            type: Number,
        },
        channelId: {
            type: Number,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    indices: [
        {
            name: "IDX_UNIQUE_WEBSITE_NAME",
            unique: true,
            columns: ["userId", "websiteId", "channelId"],
        },
    ],
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
        },
        website: {
            type: "many-to-one",
            target: "website",
        },
        channel: {
            type: "many-to-one",
            target: "channel",
        },
    },
});
