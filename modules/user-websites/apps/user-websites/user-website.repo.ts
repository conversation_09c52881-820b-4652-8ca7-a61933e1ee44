import { layers } from "../../../common";
import { UserWebsite } from "./types";
import { UserWebsiteSchema } from "./user-website.model";

export default class UserWebsiteRepo extends layers.BaseTypeormRepository<UserWebsite> {
    relations = [];
    constructor() {
        super(UserWebsiteSchema);
    }

    getUserWebsites = (userId: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("user-website")
            .where("user-website.userId = :userId", { userId })
            .filter(query.filter)
            .leftJoinAndSelect(
                "website",
                "website",
                "user-website.websiteId = website.id",
            )
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };
}
