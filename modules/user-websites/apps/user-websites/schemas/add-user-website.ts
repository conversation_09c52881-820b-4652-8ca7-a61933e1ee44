import { JSONSchemaType } from "ajv";
import { AddUserWebsiteDto } from "../types/dtos";

export const AddUserWebsiteSchema: JSONSchemaType<AddUserWebsiteDto> = {
    $id: "add-user-website",
    type: "object",
    properties: {
        websiteId: {
            type: "integer",
            example: 1,
        },
        isEnabled: {
            type: "boolean",
            example: true,
        },
    },
    required: ["websiteId", "isEnabled"],
    additionalProperties: false,
};
