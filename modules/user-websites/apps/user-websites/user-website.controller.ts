import { injectable } from "tsyringe";
import { Request, Response } from "express";
import UserWebsiteService from "./user-website.service";
import { AddUserWebsiteSchema } from "./schemas";
import { OpenAPI } from "../../../common/lib/decorators";

@injectable()
export default class UserWebsiteController {
    constructor(private _service: UserWebsiteService) {}

    @OpenAPI(
        "user-websites",
        "/",
        "post",
        AddUserWebsiteSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    toggleUserWebsite = async (req: Request, res: Response) => {
        const { user, body } = req;
        await this._service.toggleUserWebsite(body, user!);
        res.success({});
    };
}
