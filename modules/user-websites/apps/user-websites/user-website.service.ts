import { injectable } from "tsyringe";
import UserWebsiteRepo from "./user-website.repo";
import { AddUserWebsiteDto } from "./types";
// import { errors } from "../../../common";
import WebsiteService from "../../../websites/apps/websites/website.service";
import ChannelService from "../../../channels/apps/channels/channel.service";
import { utils } from "../../../common";

@injectable()
export default class UserWebsiteService {
    constructor(
        private _repo: UserWebsiteRepo,
        private _websiteService: WebsiteService,
        private _channelService: ChannelService,
    ) {}

    toggleUserWebsite = async (
        args: AddUserWebsiteDto,
        profile: Express.User,
    ) => {
        const { id: userId } = profile;
        const { websiteId, isEnabled } = args;

        const channel =
            await this._channelService.getChannelOfUserByUserId(userId);

        await this._websiteService.getWebsitesById(websiteId);

        if (isEnabled) {
            const existingWebsite = await this._repo.findOneByQuery({
                userId,
                websiteId,
            });
            if (utils.isNotNil(existingWebsite)) {
                return;
            }

            await this._repo.create({
                userId,
                websiteId,
                channelId: channel.id,
            });
        } else {
            await this._repo.deleteOneByQuery({ userId, websiteId });
        }
    };

    getSelectedWebsitesOfUser = async (userId: number) => {
        const selectedWebsites = await this._repo.findByQuery({ userId });
        return selectedWebsites;
    };
}
