import { JSONSchemaType } from "ajv";
import { GetSubscriptionPlansResponse } from "../types";

export const GetSubscriptionPlansResponseSchema: JSONSchemaType<
    GetSubscriptionPlansResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "plan_",
        properties: {
            id: {
                type: "number",
            },
            name: {
                type: "string",
            },
            price: {
                type: "string",
            },
            duration: {
                type: "integer",
                minimum: 1,
            },
            isPopular: {
                type: "boolean",
            },
            isBase: {
                type: "boolean",
            },
            inventoryLimit: {
                type: "integer",
                minimum: 0,
            },
            botLimit: {
                type: "integer",
                minimum: 0,
            },
            reportLimit: {
                type: "integer",
                minimum: 0,
            },
        },
        required: [],
    },
};
