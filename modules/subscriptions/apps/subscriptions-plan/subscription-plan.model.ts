import { EntitySchema } from "typeorm";
import { SubscriptionPlan } from "./types";

export const SubscriptionPlanSchema = new EntitySchema<SubscriptionPlan>({
    name: "subscription-plan",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        name: {
            type: String,
            unique: true,
        },
        price: {
            type: "numeric",
        },
        duration: {
            type: Number,
        },
        isPopular: {
            type: Boolean,
            default: false,
        },
        isBase: {
            type: Boolean,
            default: false,
        },
        inventoryLimit: {
            type: Number,
        },
        botLimit: {
            type: Number,
        },
        reportLimit: {
            type: Number,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
});
