import { JSONSchemaType } from "ajv";
import { AddSubscriptionPlanDto } from "../types";

export const AddSubscriptionPlanSchema: JSONSchemaType<AddSubscriptionPlanDto> =
    {
        $id: "add-subscription-plan",
        type: "object",
        properties: {
            name: {
                type: "string",
                example: "yellow",
            },
            price: {
                type: "string",
                example: "20000",
            },
            duration: {
                type: "integer",
                minimum: 1,
                example: 1,
            },
            isPopular: {
                type: "boolean",
                example: false,
            },
            isBase: {
                type: "boolean",
                example: false,
            },
            inventoryLimit: {
                type: "integer",
                minimum: 0,
                example: 100,
            },
            botLimit: {
                type: "integer",
                minimum: 0,
                example: 50,
            },
            reportLimit: {
                type: "integer",
                minimum: 0,
                example: 50,
            },
            isActive: {
                type: "boolean",
                example: true,
            },
        },
        required: [
            "name",
            "price",
            "duration",
            "inventoryLimit",
            "botLimit",
            "reportLimit",
        ],
    };
