import { injectable } from "tsyringe";
import SubscriptionPlanRepo from "./subscription-plan.repo";
import { AddSubscriptionPlanDto } from "./types";
import { getSubscriptionPlansSerializer } from "./responses";

@injectable()
export default class SubscriptionPlanService {
    constructor(private _repo: SubscriptionPlanRepo) {}

    addSubscriptionPlan = async (args: AddSubscriptionPlanDto) => {
        await this._repo.create(args);
    };

    getSubscriptionPlans = async (parsedQuery: Partial<Express.Query>) => {
        const subscriptionPlans =
            await this._repo.getSubscriptionPlans(parsedQuery);

        return getSubscriptionPlansSerializer(subscriptionPlans);
    };

    getSubscriptionPlan = async (planId: number) => {
        return await this._repo.findOneByQuery({
            id: planId,
        });
    };
}
