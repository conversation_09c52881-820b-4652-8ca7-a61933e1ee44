import express from "express";
import { container } from "tsyringe";
import SubscriptionPlanController from "./subscription-plan.controller";

import * as schemas from "./schemas";
import { JWT } from "../../../common/lib/middlewares";

const router = express.Router();

const controller = container.resolve(SubscriptionPlanController);

router
    .route("/")
    .post(schemas.addSubscriptionPlan, controller.addSubscriptionPlan)
    .get(JW<PERSON>, controller.getSubscriptionPlans);

export default router;
