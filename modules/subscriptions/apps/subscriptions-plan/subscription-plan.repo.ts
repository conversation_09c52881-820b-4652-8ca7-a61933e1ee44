import { layers } from "../../../common";
import { SubscriptionPlanSchema } from "./subscription-plan.model";
import { SubscriptionPlan } from "./types";

export default class SubscriptionPlanRepo extends layers.BaseTypeormRepository<SubscriptionPlan> {
    relations = [];
    constructor() {
        super(SubscriptionPlanSchema);
    }

    getSubscriptionPlans = (query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("plan")
            .where("plan.isActive = true")
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };
}
