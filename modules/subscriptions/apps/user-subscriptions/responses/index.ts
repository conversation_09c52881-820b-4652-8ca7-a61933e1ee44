import { paginatedSerializer, serializer } from "../../../../common/lib/utils";
import { GetUserSubscriptionsResponseSchema } from "./get-user-subscriptions";
import { GetUserSubscriptionResponseSchema } from "./get-user-subscription";

export const getUserSubscriptionsSerializer = paginatedSerializer(
    GetUserSubscriptionsResponseSchema,
);

export const getUserSubscriptionSerializer = serializer(
    GetUserSubscriptionResponseSchema,
);

export * from "./get-user-subscriptions";
export * from "./get-user-subscription";
export * from "./create-payment";
