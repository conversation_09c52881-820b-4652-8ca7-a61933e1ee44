import { inject, injectable, registry } from "tsyringe";
import UserSubscriptionRepo from "./user-subscription.repo";
import {
    AddUserSubscriptionDto,
    InquiryDepositJob,
    UserSubscription,
    VerifyDepositJob,
    ZibalCallBackQueryDto,
} from "./types";
import SubscriptionPlanService from "../subscriptions-plan/subscription-plan.service";
import { errors, utils } from "../../../common";
import {
    getUserSubscriptionSerializer,
    getUserSubscriptionsSerializer,
} from "./responses";
import { ZibalService } from "../../../common/lib/payment/zibal";
import { RedisConnection } from "../../../common/lib/redis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { Job, Processor } from "bullmq";
import { registries } from "./registries";

type RejectReason = UserSubscription["rejectionReason"];

@registry(registries)
@injectable()
export default class UserSubscriptionService {
    private _verifyDepositJobName: string = "verify-deposit";
    private _inquiryDepositJobName: string = "inquiry-deposit";
    private _redisConnection: RedisConnection;
    private _paymentService: ZibalService;
    private _verifyDepositQueue: BullMQModule<VerifyDepositJob>;
    private _inquiryDepositQueue: BullMQModule<InquiryDepositJob>;
    private _redirectUrl = process.env.WEB_APP_URL;

    constructor(
        private _repo: UserSubscriptionRepo,
        private _subscriptionPlanService: SubscriptionPlanService,
        @inject("payment")
        createPaymentService: () => ZibalService,
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();

        this._paymentService = createPaymentService();

        const VerifyDepositProcessor: Processor<VerifyDepositJob> =
            createJobProcessor(this._verifyDeposit.bind(this));
        this._verifyDepositQueue = createQueueModule(
            this._verifyDepositJobName,
            VerifyDepositProcessor,
            this._redisConnection,
        );

        const InquiryDepositProcessor: Processor<InquiryDepositJob> =
            createJobProcessor(this._inquiryDeposit.bind(this));
        this._inquiryDepositQueue = createQueueModule(
            this._inquiryDepositJobName,
            InquiryDepositProcessor,
            this._redisConnection,
        );
    }

    private _generateStartDate = (now: Date, lastDate: Date | null) => {
        let startDate;
        if (utils.isNil(lastDate)) {
            startDate = now;
        } else {
            startDate = now > lastDate ? now : lastDate;
        }

        return startDate;
    };

    private _generateEndDate = (date: Date, durationInMonths: number) => {
        const endDate = new Date(date);
        endDate.setMonth(endDate.getMonth() + durationInMonths);

        return endDate;
    };

    private _verifyDeposit = async (job: Job<VerifyDepositJob>) => {
        const { id, trackId } = job.data;

        const verifyResponse =
            await this._paymentService.verifyDeposit(trackId);

        if (utils.isNotNil(verifyResponse.error)) {
            return this._repo.updateById(id, {
                rejectionReason: verifyResponse.error as RejectReason,
                status: "REJECTED",
            });
        } else if (verifyResponse.state === "PAYED_ACCEPTED") {
            await this._repo.updateById(id, {
                status: "PAYED_ACCEPTED",
            });

            await this._inquiryDepositQueue.addJob(
                this._inquiryDepositJobName,
                {
                    id,
                    trackId,
                },
            );
        }
    };

    private _inquiryDeposit = async (job: Job<InquiryDepositJob>) => {
        const { id, trackId } = job.data;

        const inquiryResponse =
            await this._paymentService.inquiryDeposit(trackId);

        if (utils.isNotNil(inquiryResponse.error)) {
            return this._repo.updateById(id, {
                rejectionReason: inquiryResponse.error as RejectReason,
                status: "REJECTED",
            });
        }

        await this._repo.runTransaction(async (manager) => {
            await this._repo.updateById(
                id,
                {
                    description: inquiryResponse.description,
                },
                { manager },
            );

            if (inquiryResponse.state === "PAYED_ACCEPTED") {
                await this._repo.updateById(
                    id,
                    {
                        status: "EXECUTED",
                    },
                    { manager },
                );
            }
        });
    };

    addUserSubscription = async (
        args: AddUserSubscriptionDto,
        profile: Express.User,
    ) => {
        const { planId } = args;
        const { id: userId } = profile;

        const now = new Date();

        const subscriptionPlan =
            await this._subscriptionPlanService.getSubscriptionPlan(planId);
        if (utils.isNil(subscriptionPlan)) {
            throw new errors.NotFoundError("Subscription Plan");
        }
        if (subscriptionPlan.isBase) {
            throw new errors.BadRequestError();
        }

        if (!subscriptionPlan.isActive) {
            throw new errors.BadRequestError();
        }

        const latestSubscription =
            await this.getLatestSubscriptionOfUser(userId);
        const startDate = this._generateStartDate(
            now,
            latestSubscription?.endDate ?? null,
        );
        const endDate = this._generateEndDate(
            startDate,
            subscriptionPlan.duration,
        );

        const { trackId, payLink, error } =
            await this._paymentService.createRequest(
                Number(subscriptionPlan.price),
            );

        if (utils.isNotNil(error)) {
            throw new errors.BadRequestError();
        }

        await this._repo.create({
            planId,
            endDate,
            trackId,
            userId,
        });

        return {
            trackId,
            payLink,
        };
    };

    verifyPayment = async (args: ZibalCallBackQueryDto) => {
        const payment = await this._repo.findOneByQuery({
            trackId: args.trackId,
        });

        if (utils.isNil(payment)) {
            return this._redirectUrl;
        }

        const { error, state } = this._paymentService.callbackStatusHandler(
            args.status,
        );

        if (utils.isNotNil(error)) {
            await this._repo.updateById(payment.id, {
                rejectionReason: error as RejectReason,
                status: "REJECTED",
            });
            return this._redirectUrl;
        }

        if (state === "PAYED_ACCEPTED") {
            await this._repo.updateById(payment.id, {
                status: "PAYED_ACCEPTED",
            });

            await this._inquiryDepositQueue.addJob(
                this._inquiryDepositJobName,
                {
                    id: payment.id,
                    trackId: payment.trackId,
                },
            );
        } else if (state === "PAYED_NOT_ACCEPTED") {
            await this._repo.updateById(payment.id, {
                rejectionReason: error as RejectReason,
                status: "PAYED_NOT_ACCEPTED",
            });

            await this._verifyDepositQueue.addJob(this._verifyDepositJobName, {
                id: payment.id,
                trackId: payment.trackId,
            });
        }

        return this._redirectUrl;
    };

    getSubscriptionsOfUser = async (
        user: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = user;

        const userSubscriptions = await this._repo.getUserSubscriptionHistory(
            userId,
            parsedQuery,
        );

        return getUserSubscriptionsSerializer(userSubscriptions);
    };

    getLatestSubscriptionOfUser = async (userId: number) => {
        return this._repo.getLatestSubscriptionOfUser(userId);
    };

    getCurrentSubscriptionOfUser = async (userId: number) => {
        const now = new Date();

        const rawSubscription =
            await this._repo.getNearestSubscriptionOfUser(userId);

        if (utils.isNil(rawSubscription)) {
            return null;
        }

        const subscription = getUserSubscriptionSerializer(rawSubscription);

        const endDate = new Date(subscription.endDate);

        const startDate = new Date(endDate);
        startDate.setMonth(endDate.getMonth() - subscription.plan.duration);

        if (now < startDate) {
            return null;
        }

        return subscription;
    };
}
