import { layers } from "../../../common";
import { UserSubscriptionSchema } from "./user-subscription.model";
import { UserSubscription } from "./types";
// import { LessThanOrEqual } from "typeorm";

export default class UserSubscriptionRepo extends layers.BaseTypeormRepository<UserSubscription> {
    relations = [];
    constructor() {
        super(UserSubscriptionSchema);
    }

    getUserSubscriptionHistory = async (
        userId: number,
        query: Partial<Express.Query>,
    ) => {
        return this._repo
            .createQueryBuilder("subscription")
            .where("subscription.userId = :userId", { userId })
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getLatestSubscriptionOfUser = async (userId: number) => {
        return this._repo
            .createQueryBuilder("subscription")
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription.status = 'EXECUTED'")
            .orderBy("subscription.endDate", "DESC")
            .addOrderBy("subscription.id", "DESC")
            .getOne();
    };

    getNearestSubscriptionOfUser = async (userId: number) => {
        return this._repo
            .createQueryBuilder("subscription")
            .leftJoinAndSelect(
                "subscription-plan",
                "subscription-plan",
                "subscription-plan.id = subscription.planId",
            )
            .where("subscription.userId = :userId", { userId })
            .andWhere("subscription.status = 'EXECUTED'")
            .andWhere("subscription.endDate > NOW()")
            .orderBy("subscription.endDate", "ASC")
            .getRawOne();
    };

    getDepositByTrackId = async (trackId: string) => {
        return this.findOneByQuery({ trackId });
    };
}
