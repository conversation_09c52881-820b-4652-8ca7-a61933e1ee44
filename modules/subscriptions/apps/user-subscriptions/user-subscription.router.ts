import express from "express";
import { container } from "tsyringe";
import UserSubscriptionController from "./user-subscription.controller";
import { JWT } from "../../../common/lib/middlewares";
import * as schemas from "./schemas";

const router = express.Router();

const controller = container.resolve(UserSubscriptionController);

router
    .route("/")
    .post(JWT, schemas.addUserSubscription, controller.addUserSubscription)
    .get(JWT, controller.getSubscriptionsOfUser);

router.route("/callback").get(controller.verifyPaymentWebhook);

export default router;
