import { UserSubscription } from "./entities";

export type AddUserSubscriptionDto = Pick<UserSubscription, "planId">;

export interface ZibalCallBackQueryDto {
    trackId: string;
    success: "0" | "1";
    status:
        | "-1"
        | "-2"
        | "1"
        | "2"
        | "3"
        | "4"
        | "5"
        | "6"
        | "7"
        | "8"
        | "9"
        | "10"
        | "11"
        | "12";
}
