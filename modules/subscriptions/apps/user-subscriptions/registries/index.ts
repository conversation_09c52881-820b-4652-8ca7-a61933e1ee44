import { createBullMQModule } from "../../../../common/lib/bullmq";
import { createPaymentService } from "../../../../common/lib/payment";
import { createRedisConnection } from "../../../../common/lib/redis";

export const registries = [
    { token: "redis", useFactory: () => createRedisConnection },
    { token: "queue", useFactory: () => createBullMQModule },
    { token: "payment", useFactory: () => createPaymentService },
];
