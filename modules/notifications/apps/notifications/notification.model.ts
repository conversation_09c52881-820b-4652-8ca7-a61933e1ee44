import { EntitySchema } from "typeorm";
import { Notification } from "./types/entities";

export const NotificationSchema = new EntitySchema<Notification>({
    name: "notification",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        userId: {
            type: Number,
            nullable: true,
        },
        type: {
            type: String,
            enum: ["inventory", "order"],
        },
        sourceId: {
            type: Number,
            nullable: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
            nullable: true,
        },
    },
});
