import { injectable } from "tsyringe";
import { Request, Response } from "express";
import NotificationService from "./notification.service";
import { OpenAPI } from "../../../common/lib/decorators";

@injectable()
export default class NotificationController {
    constructor(private _service: NotificationService) {}

    @OpenAPI(
        "notification",
        "/",
        "get",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    getNotifications = async (req: Request, res: Response) => {
        const { user } = req;

        await this._service.getNotifications(user!);

        res.success({});
    };
}
