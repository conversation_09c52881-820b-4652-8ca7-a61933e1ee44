import { EntitySchema } from "typeorm";
import { NotificationConfig } from "./types/entities";

export const NotificationConfigSchema = new EntitySchema<NotificationConfig>({
    name: "notification-config",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        userId: {
            type: Number,
            nullable: true,
        },
        token: {
            type: String,
        },
        type: {
            type: String,
            enum: ["FCM"],
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
            nullable: true,
        },
    },
});
