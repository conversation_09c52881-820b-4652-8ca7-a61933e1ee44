import { injectable } from "tsyringe";
import { Request, Response } from "express";
import NotificationConfigService from "./notifications-config.service";
import { AddTokenSchema } from "./schemas";
import { OpenAPI } from "../../../common/lib/decorators";

@injectable()
export default class NotificationConfigController {
    constructor(private _service: NotificationConfigService) {}

    @OpenAPI(
        "notification/config",
        "/",
        "post",
        AddTokenSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addToken = async (req: Request, res: Response) => {
        const { user, body } = req;

        await this._service.addToken(body, user!);

        res.success({});
    };
}
