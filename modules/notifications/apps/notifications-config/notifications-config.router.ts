import express from "express";
import { middlewares } from "../../../common";
import { container } from "tsyringe";
import notificationConfigController from "./notifications-config.controller";
import * as schemas from "./schemas";

const { JWT } = middlewares;

const router = express.Router();

const controller = container.resolve(notificationConfigController);

router.route("/").post(JWT, schemas.addToken, controller.addToken);

export default router;
