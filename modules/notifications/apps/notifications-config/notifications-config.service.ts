import { injectable } from "tsyringe";
import { addTokenDto } from "./types/dtos";
import NotificationConfigRepo from "./notifications-config.repo";

@injectable()
export default class NotificationConfigService {
    constructor(private _repo: NotificationConfigRepo) {}

    addToken = async (args: addTokenDto, profile: Express.User) => {
        const { id: userId } = profile;
        await this._repo.create({
            ...args,
            userId,
            type: "FCM",
        });
    };

    getUserTokens = async (userId: number) => {
        return this._repo.findByQuery({ userId });
    };
}
