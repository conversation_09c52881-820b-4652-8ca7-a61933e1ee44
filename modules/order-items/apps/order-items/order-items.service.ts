import { injectable } from "tsyringe";
import OrderItemsRepo from "./order-items.repo";
import { EntityManager } from "typeorm";
import { ResetOrderItemsDto } from "./types";

@injectable()
export default class OrderItemsService {
    constructor(private _repo: OrderItemsRepo) {}

    getOrderItems = (orderId: number) => {
        return this._repo.findByQuery({ orderId });
    };

    resetOrderItems = async (
        inventories: ResetOrderItemsDto[],
        orderId: number,
        manager: EntityManager,
    ) => {
        await this._repo.deleteByQuery({ orderId }, { manager });
        await this._repo.bulkCreate(
            inventories.map((inventory) => ({
                orderId,
                ...inventory,
            })),
            { manager },
        );
    };
}
