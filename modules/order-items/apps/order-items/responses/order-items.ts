import { JSONSchemaType } from "ajv";
import { OrderItemSchema } from "../types";

export const OrderItemResponseSchema: JSONSchemaType<OrderItemSchema[]> = {
    type: "array",
    items: {
        type: "object",
        prefix: "order-items_",
        properties: {
            inventoryId: {
                type: "number",
            },
            name: {
                type: "string",
                prefix: "inventory_",
            },
            amount: {
                type: "number",
            },
            cost: {
                type: "string",
            },
            price: {
                type: "string",
            },
        },
        required: [],
    },
};
