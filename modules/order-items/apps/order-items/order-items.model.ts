import { EntitySchema } from "typeorm";
import { OrderItem } from "./types/entities";

export const OrderItemsSchema = new EntitySchema<OrderItem>({
    name: "order-items",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        orderId: {
            type: Number,
        },
        inventoryId: {
            type: Number,
        },
        amount: {
            type: Number,
        },
        cost: {
            type: "numeric",
        },
        price: {
            type: "numeric",
        },
    },
    relations: {
        order: {
            type: "many-to-one",
            target: "order",
        },
        inventory: {
            type: "many-to-one",
            target: "inventory",
        },
    },
});
