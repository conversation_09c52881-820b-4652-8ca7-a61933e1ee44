/**
 * Trigger phrases that customers can send to disable the chatbot and request human assistance
 */
export const CHATBOT_DISABLE_TRIGGERS = {
    ENGLISH: [
        "quick reply",
        "admin reply",
        "human help",
        "speak to admin",
        "talk to human",
        "customer service",
        "live chat"
    ],
    PERSIAN: [
        "ادمین پاسخ دهد",
        "پاسخ سریع",
        "صحبت با ادمین",
        "کمک انسانی",
        "پشتیبانی",
        "خدمات مشتریان"
    ]
};

/**
 * Confirmation messages sent when chatbot is disabled due to trigger phrases
 */
export const CHATBOT_DISABLE_CONFIRMATIONS = {
    ENGLISH: "Your request for admin assistance has been received. You will get a response soon.",
    PERSIAN: "درخواست شما برای صحبت با ادمین ثبت شد. به زودی پاسخ خواهید گرفت."
};

/**
 * Get all trigger phrases for the current language
 */
export function getTriggerPhrases(language: 'fa' | 'en' = 'fa'): string[] {
    if (language === 'en') {
        return [...CHATBOT_DISABLE_TRIGGERS.ENGLISH, ...CHATBOT_DISABLE_TRIGGERS.PERSIAN];
    }
    return [...CHATBOT_DISABLE_TRIGGERS.PERSIAN, ...CHATBOT_DISABLE_TRIGGERS.ENGLISH];
}

/**
 * Get confirmation message for the current language
 */
export function getConfirmationMessage(language: 'fa' | 'en' = 'fa'): string {
    return language === 'en' 
        ? CHATBOT_DISABLE_CONFIRMATIONS.ENGLISH 
        : CHATBOT_DISABLE_CONFIRMATIONS.PERSIAN;
}
