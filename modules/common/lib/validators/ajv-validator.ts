import Ajv, { AnySchema, DefinedError } from "ajv/dist/2019";
import { NextFunction, Request, Response } from "express";
import addFormats from "ajv-formats";

import { ValidationError } from "../errors";

// const addFormats = require("ajv-formats");
// const addErrors = require("ajv-errors");

export function AJVValidator(schemas: AnySchema[]) {
    const ajv = new Ajv({
        allErrors: true,
        strict: "log",
        schemas,
        keywords: ["example"],
    });

    addFormats(ajv);
    // addErrors(ajv);

    return (schemaId: string) => {
        const validate = ajv.getSchema(schemaId);
        return async (req: Request, res: Response, next: NextFunction) => {
            await validate!(req.body);
            if (!validate?.errors) {
                return next();
            }

            next(new ValidationError(validate?.errors as DefinedError[]));
        };
    };
}
