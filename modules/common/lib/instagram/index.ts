import { injectable } from "tsyringe";
import { errors, utils } from "../../../common";

export interface Message {
    id: string;
    created_time: string;
    text: string;
}

export interface MessagesData {
    data: Message[];
}

export interface Conversation {
    id: string;
    updated_time: string;
    messages?: MessagesData;
}

export interface ConversationResponse {
    data: Conversation[];
}

export interface InstagramTokenResponse {
    access_token: string;
    user_id: number;
}

export interface InstagramUserProfile {
    id: string;
    username: string;
}

export interface User {
    name: string;
    instagramUsername: string;
}

export interface UserRepo {
    create(user: { name: string; instagramUsername: string }): Promise<User>;
}

export interface InstagramUser {
    instagramUsername: string;
}

export interface ClientRepo {
    create(user: { instagramUsername: string }): Promise<InstagramUser | null>;
}

interface Following {
    id: string;
}

interface ApiResponse {
    data: Following[];
}

@injectable()
export class InstagramService {
    // TODO: this value should assigned dynamically
    private pageAccessToken = process.env.PAGE_ACCESS_TOKEN;
    private PAGE_ID = process.env.PAGE_ID;

    public async getUserDetails(
        userId: string,
    ): Promise<{ locale: string; name: string }> {
        const url = `https://graph.facebook.com/${userId}?access_token=${this.pageAccessToken}&fields=name,locale`;

        const response = await fetch(url, {
            method: "GET",
        });

        if (utils.isNil(response)) {
            throw new errors.NotFoundError("response");
        }

        const data = await response.json();

        return data as { locale: string; name: string };
    }

    async processInstagramSSO(
        clientId: string,
        clientSecret: string,
        grantType: string,
    ): Promise<string> {
        const response = await fetch(
            `https://graph.facebook.com/oauth/access_token?client_id=${clientId}&client_secret=${clientSecret}&grant_type=${grantType}`,
            {
                method: "GET",
            },
        );

        if (utils.isNil(response)) {
            throw new errors.NotFoundError("response");
        }

        const data: InstagramTokenResponse = await response.json();

        return data.access_token;
    }

    // Get a list of conversations
    async getConversations(
        pageId: string,
        platform: string,
        accessToken: string,
    ): Promise<unknown> {
        const response = await fetch(
            `https://graph.facebook.com/v20.0/${pageId}/conversations?platform=${platform}&access_token=${accessToken}`,
            {
                method: "GET",
            },
        );

        if (utils.isNil(response)) {
            throw new errors.NotFoundError("response");
        }

        const data: unknown = await response.json();

        return data;
    }

    // Find a conversation with a specific user
    async getConversationWithUser(
        pageId: string,
        platform: string,
        userId: string,
        accessToken: string,
    ): Promise<ConversationResponse> {
        const response = await fetch(
            `https://graph.facebook.com/v20.0/${pageId}/conversations?platform=${platform}&user_id=${userId}&access_token=${accessToken}`,
            {
                method: "GET",
            },
        );

        if (utils.isNil(response)) {
            throw new errors.NotFoundError("response");
        }

        const data = await response.json();

        return data as ConversationResponse;
    }

    // Get a list of messages in a conversation
    async getMessagesInConversation(
        conversationId: string,
        accessToken: string,
    ): Promise<MessagesData> {
        const response = await fetch(
            `https://graph.facebook.com/v20.0/${conversationId}?fields=messages&access_token=${accessToken}`,
            {
                method: "GET",
            },
        );

        if (utils.isNil(response)) {
            throw new errors.NotFoundError("response");
        }

        const data = await response.json();

        return data as MessagesData;
    }

    // Get information about a message
    async getMessageInfo(
        messageId: string,
        accessToken: string,
    ): Promise<unknown> {
        const url = `https://graph.facebook.com/v20.0/${messageId}?fields=id,created_time,from,to,message&access_token=${accessToken}`;

        const response = await fetch(url, {
            method: "GET",
        });

        if (utils.isNil(response)) {
            throw new errors.NotFoundError("response");
        }

        const data: unknown = await response.json();

        return data;
    }

    // TODO: this is just practice, actual api need to be implemented
    isClientFollowedByUser = async (
        userId: string,
        clientId: string,
    ): Promise<boolean> => {
        const accessToken = "ACCESS_TOKEN";
        const url = `https://graph.instagram.com/${userId}/following?access_token=${accessToken}`;

        const response = await fetch(url);
        // if (!response.ok) {
        //     throw new errors.BadRequestError("Network response was not ok");
        // }

        const data: ApiResponse = await response.json();
        const followingList: Following[] = data.data;

        return followingList.some(
            (following: Following) => following.id === clientId,
        );
    };

    sendMessageToInstagramUser = async (
        IGSID: string,
        message: string,
    ): Promise<void> => {
        const url = `https://graph.facebook.com/v20.0/${this.PAGE_ID}/messages?access_token=${this.pageAccessToken}`;

        const payload = {
            recipient: { id: IGSID },
            message: { text: message },
        };

        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error("Error sending message:", errorData);
                throw new Error("Network response was not ok");
            }

            const data = await response.json();
            console.log("Message sent:", data);
        } catch (error) {
            console.error("Error sending message:", error);
        }
    };

    //https://developers.facebook.com/docs/messenger-platform/send-messages/template/button
    // sendButtonTemplate = async (
    //     recipientId: string,
    //     buttonResponse: ButtonResponse,
    //     pageAccessToken: string,
    // ) => {
    //     const payload = {
    //         recipient: {
    //             id: recipientId,
    //         },
    //         message: {
    //             attachment: {
    //                 type: "template",
    //                 payload: {
    //                     // eslint-disable-next-line camelcase
    //                     template_type: "button",
    //                     text: buttonResponse.text,
    //                     buttons: buttonResponse.buttons,
    //                 },
    //             },
    //         },
    //     };

    //     const response = await fetch(
    //         `https://graph.facebook.com/v20.0/me/messages?access_token=${pageAccessToken}`,
    //         {
    //             method: "POST",
    //             headers: {
    //                 "Content-Type": "application/json",
    //             },
    //             body: JSON.stringify(payload),
    //         },
    //     );

    //     const data = await response.json();
    //     if (!response.ok) {
    //         // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    //         throw new Error(data.error.message);
    //     }

    //     // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    //     return data;
    // };

    // async  getInstagramUserInsights(
    //     igUserId: string,
    //     accessToken: string,
    //     metric: string,
    //     period: string,
    //     since?: string,
    //     until?: string,
    //     breakdown?: string
    // ): Promise<unknown> {
    //     const apiVersion = "v20.0"; // You can update this to the latest version if needed.
    //     let url = `https://graph.facebook.com/${apiVersion}/${igUserId}/insights?metric=${metric}&period=${period}&access_token=${accessToken}`;

    //     if (since) {
    //         url += `&since=${since}`;
    //     }

    //     if (until) {
    //         url += `&until=${until}`;
    //     }

    //     if (breakdown) {
    //         url += `&breakdown=${breakdown}`;
    //     }

    //     const response = await fetch(url, {
    //         method: "GET",
    //     });

    //     if (!response.ok) {
    //         throw new Error(`Error fetching insights: ${response.statusText}`);
    //     }

    //     const data: unknown = await response.json();

    //     return data;
    // }

    // // Example usage:
    // (async () => {
    //     try {
    //         const insights = await getInstagramUserInsights(
    //             "your-ig-user-id",
    //             "your-access-token",
    //             "follower_count", // Metric you want to retrieve
    //             "day",            // Period (e.g., day, week, month, lifetime)
    //             "2023-07-01",     // Optional since date
    //             "2023-07-31",     // Optional until date
    //             "FOLLOWER"        // Optional breakdown (e.g., FOLLOWER, NON_FOLLOWER)
    //         );
    //         console.log(insights);
    //     } catch (error) {
    //         console.error("Error fetching insights:", error);
    //     }
    // })();
}

export * from "./insta";
export * from "./insta-account";
