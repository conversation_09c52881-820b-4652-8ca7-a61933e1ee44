import { singleton } from "tsyringe";
import { errors } from "..";
import Logger from "../metrics/logger";
import {
    getAccessToken,
    getAccessTokenFailure,
    getLongLivedToken,
    getLongLivedTokenFailure,
    recordErrorValue,
} from "../metrics/metrics";
import {
    INSTA_BASIC_API,
    INSTA_GRAPH_API,
    INSTAGRAM_BASIC_DISPLAY_OAUTH_API,
    InstagramAccessTokenResponse,
    InstagramChallengeDto,
    InstagramLongLivedTokenResponse,
    InstaGramScopes,
} from "../../base/types/instagram.type";

const {
    INSTAGRAM_CLIENT_ID,
    INSTAGRAM_CLIENT_SECRET,
    INSTAGRAM_CALLBACK_URL,
    INSTAGRAM_WEBHOOK_TOKEN,
} = process.env;

@singleton()
export class AuthService {
    private _clientId: string = INSTAGRAM_CLIENT_ID;
    private _clientSecret: string = INSTAGRAM_CLIENT_SECRET;
    private _callbackUrl: string = INSTAGRAM_CALLBACK_URL;
    private _scope: string[] = InstaGramScopes;

    constructor() {}

    createLoginUrl = (state: string) => {
        Logger.debug("Creating Instagram login URL", {
            action: "create_login_url",
            state,
            clientId: this._clientId,
            callbackUrl: this._callbackUrl,
            scope: this._scope,
        });

        //Instagram Basic Display OAuth endpoint
        const url = `${INSTAGRAM_BASIC_DISPLAY_OAUTH_API}?client_id=${this._clientId}&redirect_uri=${this._callbackUrl}&response_type=code&scope=${this._scope.join(",")}&state=${state}`;

        Logger.debug("Instagram login URL created", {
            action: "create_login_url",
            url,
        });

        return url;
    };

    getAccessToken = async (code: string) => {
        Logger.debug("Starting getAccessToken", {
            action: "get_access_token",
            code: code.substring(0, 5) + "...", // Log only part of the code for security
        });

        const form = new URLSearchParams();
        form.append("client_id", this._clientId);
        form.append("client_secret", this._clientSecret);
        form.append("grant_type", "authorization_code");
        form.append("redirect_uri", this._callbackUrl);
        form.append("code", code);

        Logger.debug("Prepared form data for access token request", {
            action: "get_access_token",
            endpoint: `${INSTA_BASIC_API}/oauth/access_token`,
            formData: {
                client_id: this._clientId,
                grant_type: "authorization_code",
                redirect_uri: this._callbackUrl,
                // Not logging client_secret and code for security
            },
        });

        try {
            Logger.debug("Sending access token request", {
                action: "get_access_token",
                url: `${INSTA_BASIC_API}/oauth/access_token`,
            });

            const response = await fetch(
                `${INSTA_BASIC_API}/oauth/access_token`,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                    body: form,
                },
            );

            Logger.debug("Received response from access token request", {
                action: "get_access_token",
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
            });

            if (!response.ok) {
                const errorText = await response.text();
                Logger.error("Access token request failed with non-OK status", {
                    action: "get_access_token",
                    status: response.status,
                    statusText: response.statusText,
                    responseBody: errorText,
                });
                throw new Error(
                    `HTTP error! Status: ${response.status}, Response: ${errorText}`,
                );
            }

            const data =
                (await response.json()) as InstagramAccessTokenResponse;

            getAccessToken.inc();
            Logger.info("Successfully obtained access token", {
                action: "get_access_token",
                userId: data.user_id,
                permissions: data.permissions,
                tokenLength: data.access_token ? data.access_token.length : 0,
            });

            return data;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;

            console.error(error);

            Logger.error("Failed to get access token", {
                action: "get_access_token",
                error: "badRequestError",
                errorMessage,
                errorStack,
                code: code.substring(0, 5) + "...", // Log only part of the code for security
            });

            getAccessTokenFailure.inc();
            recordErrorValue("badRequestError", errorMessage);
            throw new errors.BadRequestError();
        }
    };

    getLongLivedToken = async (token: string) => {
        Logger.debug("Starting getLongLivedToken", {
            action: "get_long_lived_token",
            tokenLength: token ? token.length : 0,
            tokenPrefix: token ? token.substring(0, 5) + "..." : "null", // Log only part of the token for security
        });

        try {
            const url = `${INSTA_GRAPH_API}/access_token?grant_type=ig_exchange_token&client_secret=${this._clientSecret}&access_token=${token}`;

            Logger.debug("Sending long-lived token request", {
                action: "get_long_lived_token",
                endpoint: INSTA_GRAPH_API + "/access_token",
                params: {
                    grant_type: "ig_exchange_token",
                    // Not logging client_secret and access_token for security
                },
            });

            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            Logger.debug("Received response from long-lived token request", {
                action: "get_long_lived_token",
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
            });

            if (!response.ok) {
                const errorText = await response.text();
                Logger.error(
                    "Long-lived token request failed with non-OK status",
                    {
                        action: "get_long_lived_token",
                        status: response.status,
                        statusText: response.statusText,
                        responseBody: errorText,
                    },
                );
                throw new Error(
                    `HTTP error! Status: ${response.status}, Response: ${errorText}`,
                );
            }

            const data =
                (await response.json()) as InstagramLongLivedTokenResponse;

            getLongLivedToken.inc();
            Logger.info("Successfully obtained long-lived token", {
                action: "get_long_lived_token",
                tokenLength: data.access_token ? data.access_token.length : 0,
                expiresIn: data.expires_in,
            });

            return data;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;

            console.error(error);

            Logger.error("Failed to get long-lived token", {
                action: "get_long_lived_token",
                error: "badRequestError",
                errorMessage,
                errorStack,
                tokenLength: token ? token.length : 0,
            });

            getLongLivedTokenFailure.inc();
            recordErrorValue("badRequestError", errorMessage);
            throw new errors.BadRequestError();
        }
    };

    refreshLongLivedToken = async (token: string) => {
        Logger.debug("Starting refreshLongLivedToken", {
            action: "refresh_long_lived_token",
            tokenLength: token ? token.length : 0,
            tokenPrefix: token ? token.substring(0, 5) + "..." : "null", // Log only part of the token for security
        });

        try {
            const url = `${INSTA_GRAPH_API}/refresh_access_token?grant_type=ig_refresh_token&access_token=${token}`;

            Logger.debug("Sending refresh token request", {
                action: "refresh_long_lived_token",
                endpoint: INSTA_GRAPH_API + "/refresh_access_token",
                params: {
                    grant_type: "ig_refresh_token",
                    // Not logging access_token for security
                },
            });

            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            Logger.debug("Received response from refresh token request", {
                action: "refresh_long_lived_token",
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
            });

            if (!response.ok) {
                const errorText = await response.text();
                Logger.error(
                    "Refresh token request failed with non-OK status",
                    {
                        action: "refresh_long_lived_token",
                        status: response.status,
                        statusText: response.statusText,
                        responseBody: errorText,
                    },
                );
                throw new Error(
                    `HTTP error! Status: ${response.status}, Response: ${errorText}`,
                );
            }

            const data =
                (await response.json()) as InstagramLongLivedTokenResponse;

            getLongLivedToken.inc();
            Logger.info("Successfully refreshed long-lived token", {
                action: "refresh_long_lived_token",
                tokenLength: data.access_token ? data.access_token.length : 0,
                expiresIn: data.expires_in,
            });

            return data;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;

            console.error(error);

            Logger.error("Failed to refresh long-lived token", {
                action: "refresh_long_lived_token",
                error: "badRequestError",
                errorMessage,
                errorStack,
                tokenLength: token ? token.length : 0,
            });

            getLongLivedTokenFailure.inc();
            recordErrorValue("badRequestError", errorMessage);
            throw new errors.BadRequestError();
        }
    };

    verifyWebhook = (challenge: InstagramChallengeDto) => {
        Logger.debug("Starting verifyWebhook", {
            action: "verify_webhook",
            challenge: {
                mode: challenge["hub.mode"],
                hasChallenge: !!challenge["hub.challenge"],
                hasVerifyToken: !!challenge["hub.verify_token"],
            },
        });

        try {
            if (challenge["hub.mode"] !== "subscribe") {
                Logger.error("Webhook verification failed: invalid mode", {
                    action: "verify_webhook",
                    expectedMode: "subscribe",
                    receivedMode: challenge["hub.mode"],
                });
                throw new errors.BadRequestError();
            }

            Logger.debug("Webhook mode verified", {
                action: "verify_webhook",
                mode: challenge["hub.mode"],
            });

            if (challenge["hub.verify_token"] !== INSTAGRAM_WEBHOOK_TOKEN) {
                Logger.error("Webhook verification failed: invalid token", {
                    action: "verify_webhook",
                    tokenMatch: false,
                    receivedTokenLength:
                        challenge["hub.verify_token"]?.length || 0,
                });
                throw new errors.BadRequestError();
            }

            Logger.debug("Webhook token verified", {
                action: "verify_webhook",
                tokenMatch: true,
            });

            Logger.info("Webhook verification successful", {
                action: "verify_webhook",
                challengeLength: challenge["hub.challenge"]?.length || 0,
            });

            return challenge["hub.challenge"];
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;

            Logger.error("Webhook verification failed with exception", {
                action: "verify_webhook",
                errorMessage,
                errorStack,
            });

            throw error; // Re-throw the error to maintain the original behavior
        }
    };
}
