import { errors } from "..";
import {
    INSTA_GRAPH_API,
    InstagramProfileResponse,
    InstagramSendInventoryDto,
    InstagramSuccessResponse,
} from "../../base/types/instagram.type";
import Logger from "../metrics/logger";
import {
    getProfileFailedToFetch,
    getProfileTotal,
    recordErrorValue,
    subscriptionToEventsFailure,
} from "../metrics/metrics";

export class AccountService {
    private _accessToken: string;
    private _profileData = ["user_id", "username", "name"];

    constructor(accessToken: string) {
        this._accessToken = accessToken;
    }

    getProfile = async () => {
        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me?fields=${this._profileData.join(",")}&access_token=${this._accessToken}`;
            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = (await response.json()) as InstagramProfileResponse;

            getProfileTotal.inc();
            Logger.info("get instagram profile of user", {
                action: "get profile",
                message: "successfully return profile of instagram user",
            });
            return data;
        } catch (error) {
            console.error(error);
            Logger.error("getting profile of instagram user is failed", {
                action: "getProfile of instagram user",
                error: "badRequestError",
            });

            getProfileFailedToFetch.inc();
            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
    };

    subscribeToEvents = async () => {
        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/subscribed_apps?subscribed_fields=messages&access_token=${this._accessToken}`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = (await response.json()) as InstagramSuccessResponse;

            Logger.info("subscribe to events successfully done", {
                action: "subscribe to events",
                message: "successfully subscribe to events",
            });
            return data;
        } catch (error) {
            console.error(error);
            Logger.error("subscribeToEvents failed", {
                action: "subscribe to events",
                error: "badRequestError",
            });

            subscriptionToEventsFailure.inc();
            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
    };

    getUserProfile = async (platformId: string) => {
        try {
            const url = `${INSTA_GRAPH_API}/v20.0/${platformId}?fields=username&access_token=${this._accessToken}`;
            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = (await response.json()) as InstagramProfileResponse;
            return data;
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendMessage = async (to: string, text: string, useHumanAgentTag: boolean = false) => {
        const payload: any = {
            recipient: {
                id: to,
            },
            message: {
                text,
            },
        };

        // Only include the tag if useHumanAgentTag is true
        if (useHumanAgentTag) {
            payload.tag = "HUMAN_AGENT"; // Include this tag when responding outside the 24-hour window
        }

        const response = await fetch(`${INSTA_GRAPH_API}/v20.0/me/messages`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${this._accessToken}`,
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            console.error(await response.text());
            throw new errors.BadRequestError();
        }
    };

    sendInventory = async (
        to: string,
        inventory: InstagramSendInventoryDto,
    ) => {
        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: "image",
                            payload: {
                                url: inventory.image,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };

    sendImage = async (to: string, image: string) => {
        try {
            const url = `${INSTA_GRAPH_API}/v20.0/me/messages`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${this._accessToken}`,
                },
                body: JSON.stringify({
                    recipient: {
                        id: to,
                    },
                    message: {
                        attachment: {
                            type: "image",
                            payload: {
                                url: image,
                            },
                        },
                    },
                }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
        } catch (error) {
            console.error(error);
            throw new errors.BadRequestError();
        }
    };
}
