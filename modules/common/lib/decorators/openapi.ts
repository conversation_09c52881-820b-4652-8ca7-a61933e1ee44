/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { AnySchema } from "ajv";

export type SecurityType = "bearerAuth";

export type HTTPMethods = "get" | "post" | "patch" | "put" | "delete";

export interface Parameter {
    in: "header" | "path" | "query";
    name: string;
    schema: {
        type: string;
        example?: string;
        enum?: string[];
    };
}

export interface Route {
    path: string;
    method: HTTPMethods;
    operationId: string;
    tag: string;
    parameters?: Parameter[];
    schema?: AnySchema;
    response?: AnySchema;
    security?: SecurityType;
}

export function OpenAPI(
    tag: string,
    path: string,
    method: HTTPMethods,
    schema?: AnySchema,
    parameters?: Parameter[],
    response?: AnySchema,
    security?: SecurityType,
) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return function (target: any, operationId: string) {
        if (!Reflect.hasMetadata("routes", target.constructor)) {
            Reflect.defineMetadata(
                "routes",
                [] as Array<Route>,
                target.constructor,
            );
        }
        Reflect.defineMetadata("__controller__", true, target.constructor);

        const routes = Reflect.getMetadata(
            "routes",
            target.constructor,
        ) as Array<Route>;

        routes.push({
            path,
            method,
            operationId,
            tag,
            parameters,
            schema,
            response,
            security,
        });

        Reflect.defineMetadata("routes", routes, target.constructor);
    };
}
