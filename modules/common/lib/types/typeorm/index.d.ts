export declare module "typeorm/query-builder/SelectQueryBuilder" {
    interface SelectQueryBuilder<Entity> {
        paginate(
            this: SelectQueryBuilder<Entity>,
            pageNumber?: number,
            pageSize?: number,
        ): SelectQueryBuilder<Entity>;

        sort(
            this: SelectQueryBuilder<Entity>,
            sort?: Record<string, SortOrder>,
            ...aliases: string[]
        ): SelectQueryBuilder<Entity>;

        search(
            this: SelectQueryBuilder<Entity>,
            search?: string,
            searchField?: string[] | string,
            ...aliases: string[]
        ): SelectQueryBuilder<Entity>;

        filter(
            this: SelectQueryBuilder<Entity>,
            filter?: Partial<Record<string, unknown>>,
            ...aliases: string[]
        ): SelectQueryBuilder<Entity>;

        filterByJoined(
            this: SelectQueryBuilder<Entity>,
            alias: string,
            filter: Partial<Record<string, unknown>>,
        ): SelectQueryBuilder<Entity>;

        filterByEntity(
            this: SelectQueryBuilder<Entity>,
            entity: string,
            field: string,
            value: unknown,
        ): SelectQueryBuilder<Entity>;

        getRawManyAndCount(this: SelectQueryBuilder<Entity>): Promise<Entity[]>;
    }
}
