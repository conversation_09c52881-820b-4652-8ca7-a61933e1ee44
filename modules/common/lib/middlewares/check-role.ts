import { NextFunction, Request, Response } from "express";
import { ForbiddenError } from "../errors";

export function checkRole(allowedRoles: string[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        const userRole = req.user?.role;

        if (userRole === undefined || !allowedRoles.includes(userRole)) {
            return next(new ForbiddenError());
        }

        return next();
    };
}
