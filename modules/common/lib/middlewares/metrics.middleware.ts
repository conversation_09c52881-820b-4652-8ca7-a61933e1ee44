import { NextFunction, Request, Response } from "express";
import client from "prom-client";
import Logger from "../metrics/logger";

// Collect default metrics (CPU/memory usage, etc.)
client.collectDefaultMetrics();

// Counter for HTTP errors
const errorCounter = new client.Counter({
    name: "http_errors_total",
    help: "Total number of HTTP errors",
    labelNames: ["method", "statusCode", "route"],
});

// Define a histogram for HTTP request duration
const httpRequestDurationSeconds = new client.Histogram({
    name: "http_request_duration_seconds",
    help: "Duration of HTTP requests in seconds",
    labelNames: ["method", "route", "statusCode"],
    buckets: [0.1, 0.2, 0.5, 1, 2, 5, 10],
});

// Extend the Request type to include the route property
interface CustomRequest extends Request {
    route: {
        path: string;
    };
}

// Middleware to track request durations
export function metricsMiddleware(
    req: CustomRequest,
    res: Response,
    next: NextFunction,
) {
    const start = process.hrtime();

    res.on("finish", () => {
        const diff = process.hrtime(start);
        const durationInSeconds = diff[0] + diff[1] / 1e9;

        const routePath =
            req.route !== undefined && req.route.path !== undefined
                ? req.route.path
                : req.path;

        Logger.info("Request processed", {
            method: req.method,
            route: routePath,
            statusCode: res.statusCode,
            duration: durationInSeconds,
        });

        httpRequestDurationSeconds
            .labels(req.method, routePath, res.statusCode.toString())
            .observe(durationInSeconds);
    });

    next();
}

export async function metricsEndpoint(req: Request, res: Response) {
    try {
        // const allowedIps = ["127.0.0.1"]; // TODO: add server IP
        // const requestIp = req.ip;
        // console.log(requestIp);

        // if (!allowedIps.includes(requestIp!)) {
        //     return res.status(403).send("Forbidden");
        // }

        res.set("Content-Type", client.register.contentType);
        const metrics = await client.register.metrics();
        res.end(metrics);
    } catch (err) {
        res.status(500).end(err);
    }
}

export { errorCounter };
