/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { NextFunction, Request, Response } from "express";
import { isNil } from "lodash";

import { utils } from "..";
export function queryParser(
    req: Request,
    res: Response,
    next: NextFunction,
): void {
    const queryParams: Partial<Express.Query> = {};

    extractAndParseSearch(req, queryParams);
    extractAndParsePagination(req, queryParams);
    extractAndParseSortParams(req, queryParams);
    extractAndParseFilter(req, queryParams);
    req.parsedQuery = queryParams;

    next();
}

function extractAndParseSortParams(
    req: Request,
    parsedQuery: Partial<Express.Query>,
) {
    const { sort } = req.query;
    const sortObj: Record<string, Express.SortOrder> = {};
    if (Array.isArray(sort)) {
        sort.forEach((param) => {
            extractAndParseSortParam(sortObj, param);
        });
    } else {
        extractAndParseSortParam(sortObj, sort);
    }

    if (Object.keys(sortObj).length > 0) {
        parsedQuery.sort = sortObj;
    }
}

function extractAndParseSortParam(
    sortObj: Record<string, Express.SortOrder>,
    sortParam: unknown,
) {
    if (typeof sortParam !== "string") return;
    const trimmedParam = sortParam.trim();
    const sortOrder = trimmedParam.startsWith("-") ? "DESC" : "ASC";
    const fieldName =
        trimmedParam.startsWith("-") || trimmedParam.startsWith("+")
            ? trimmedParam.substring(1)
            : trimmedParam;
    if (utils.isNil(fieldName)) {
        return;
    }
    sortObj[fieldName] = sortOrder;
}

function extractAndParsePagination(
    req: Request,
    parsedQuery: Partial<Express.Query>,
) {
    const { page, pageSize } = req.query;
    if (!isNaN(parseInt(page?.toString() ?? ""))) {
        parsedQuery.page = parseInt(String(page));
    }

    if (!isNaN(parseInt(pageSize?.toString() ?? ""))) {
        parsedQuery.pageSize = parseInt(String(pageSize));
    }
}

function extractAndParseSearch(
    req: Request,
    parsedQuery: Partial<Express.Query>,
) {
    const { search, searchField } = req.query as {
        search: string;
        searchField: string | string[];
    };
    if (utils.isNil(search) || isNil(searchField)) return;
    parsedQuery.search = search;
    parsedQuery.searchField = searchField;
}

function extractAndParseFilter(
    req: Request,
    parsedQuery: Partial<Express.Query>,
) {
    const filterObj = utils.exclude(
        req.query,
        "sort",
        "search",
        "searchField",
        "page",
        "pageSize",
    );
    if (utils.isNil(filterObj)) return;
    parsedQuery.filter = {};
    for (const filterKey in filterObj) {
        if (Array.isArray(filterObj[filterKey])) {
            parsedQuery.filter[filterKey] = filterObj[filterKey];
        }

        if (typeof filterObj[filterKey] === "string") {
            if (
                !filterKey.startsWith("start_") &&
                !filterKey.startsWith("end_")
            ) {
                parsedQuery.filter[filterKey] = filterObj[filterKey];
            } else {
                const newFilterKey = filterKey
                    .replace("start_", "")
                    .replace("end_", "");

                parsedQuery.filter[newFilterKey] = utils.isNotNil(
                    parsedQuery.filter[newFilterKey],
                )
                    ? parsedQuery.filter[newFilterKey]
                    : {};

                if (filterKey.startsWith("start_")) {
                    parsedQuery.filter[newFilterKey]["start"] =
                        filterObj[filterKey];
                } else {
                    parsedQuery.filter[newFilterKey]["end"] =
                        filterObj[filterKey];
                }
            }
        }
    }
}
