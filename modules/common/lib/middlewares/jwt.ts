import { NextFunction, Request, Response } from "express";
import passport from "passport";

import { UnauthorizedError } from "../errors";
import { isNil, isNotNil } from "../utils";

export function JWT(req: Request, res: Response, next: NextFunction) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    passport.authenticate(
        "jwt",
        (err: unknown, user: Express.User | undefined) => {
            if (isNotNil(err)) {
                return next(err);
            }
            if (!user || isNil(user)) {
                next(new UnauthorizedError());
            }
            req.user = user;
            return next();
        },
    )(req, res, next);
}
