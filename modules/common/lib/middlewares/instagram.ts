import { NextFunction, Request, Response } from "express";
import passport from "passport";

import { UnauthorizedError } from "../errors";
import { utils } from "..";

export function instagram(req: Request, res: Response, next: NextFunction) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    passport.authenticate(
        "instagram",
        (err: unknown, user: Express.Profile | undefined) => {
            if (utils.isNotNil(err)) {
                return next(err);
            }
            if (utils.isNil(user)) {
                return next(new UnauthorizedError());
            }
            req.profile = user;
            return next();
        },
    )(req, res, next);
}
