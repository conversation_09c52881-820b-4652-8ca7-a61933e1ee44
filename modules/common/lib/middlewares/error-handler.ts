/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { NextFunction, Request, Response } from "express";
import { TypeORMError } from "typeorm";

import { APIError, PGError } from "../errors";
import { errors } from "..";
import { errorCounter } from "./metrics.middleware";
import Logger from "../metrics/logger";
import { unauthorizedErrorLog } from "../metrics/metrics";

interface CustomRequest extends Request {
    route: {
        path: string;
    };
}

export function errorHandler(
    err: unknown,
    req: CustomRequest,
    res: Response,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    next: NextFunction,
) {
    console.error("Caught error:", err);

    const routePath =
        req.route !== undefined && req.route.path !== undefined
            ? req.route.path
            : req.path;

    try {
        if (err instanceof APIError) {
            if (err instanceof errors.UnauthorizedError) {
                Logger.error("Unauthorize attempt accrued", {
                    action: "UnauthorizedError",
                });
                unauthorizedErrorLog.inc();
            }
            errorCounter.inc({
                method: req.method,
                statusCode: err.statusCode.toString(),
                route: routePath,
            });
            return res
                .status(err.statusCode)
                .send({ errors: err.serializeErrors() });
        } else if (err instanceof TypeORMError) {
            const pgError = new PGError(err);
            errorCounter.inc({
                method: req.method,
                statusCode: pgError.statusCode.toString(),
                route: routePath,
            });
            return res
                .status(pgError.statusCode)
                .send({ errors: pgError.serializeErrors() });
        } else {
            errorCounter.inc({
                method: req.method,
                statusCode: "500",
                route: routePath,
            });
            // Instead of throwing a generic error, return a localized InternalError
            const internalError = new errors.InternalError();
            return res.status(internalError.statusCode).send({
                errors: internalError.serializeErrors(),
            });
        }
    } catch {
        const internalError = new errors.InternalError();
        return res.status(internalError.statusCode).send({
            errors: internalError.serializeErrors(),
        });
    }
}
