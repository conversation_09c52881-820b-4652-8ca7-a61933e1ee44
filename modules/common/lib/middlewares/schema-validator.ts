import Ajv, { AnySchema, DefinedError } from "ajv";
import { NextFunction, Request, Response } from "express";
import addFormats from "ajv-formats";
// const addErrors = require("ajv-errors");

import { ValidationError } from "../errors";

const ajv = new Ajv({
    allErrors: true,
    strict: "log",
    keywords: ["example"],
});

addFormats(ajv);
// addErrors(ajv);

export default (schema: AnySchema) => {
    const validate = ajv.compile(schema);
    return async (req: Request, res: Response, next: NextFunction) => {
        await validate(req.body);
        if (!validate?.errors) {
            return next();
        }

        next(new ValidationError(validate?.errors as DefinedError[]));
    };
};
