import { NextFunction, Request, Response } from "express";
import { utils } from "../..";
import { ForbiddenError } from "../errors";

export function verifyInstagramWebhook(
    req: Request,
    res: Response,
    next: NextFunction,
) {
    const mode = req.query["hub.mode"];
    const token = req.query["hub.verify_token"];
    const challenge = req.query["hub.challenge"];

    // TODO: get token from passport-instagram
    if (utils.isNotNil(mode) && utils.isNotNil(token)) {
        if (
            mode === "subscribe" &&
            token === process.env.INSTAGRAM_WEBHOOK_TOKEN
        ) {
            res.success(challenge);
        } else {
            return next(new ForbiddenError());
        }
    } else {
        return next();
    }
}
