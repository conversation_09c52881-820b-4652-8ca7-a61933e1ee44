import { NextFunction, Request, Response } from "express";
import passport from "passport";

import { isNil, isNotNil } from "../utils";

export function JWTExists(req: Request, res: Response, next: NextFunction) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    passport.authenticate(
        "jwt-exists",
        (err: unknown, user: Express.User | undefined) => {
            if (isNotNil(err)) {
                return next(err);
            }
            if (isNil(user)) {
                return next();
            }
            req.user = user;
            return next();
        },
    )(req, res, next);
}
