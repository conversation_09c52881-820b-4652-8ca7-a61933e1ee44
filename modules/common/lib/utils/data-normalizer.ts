/**
 * Utility functions for normalizing data
 * Handles normalization of text, numbers, and special characters
 * Particularly useful for handling Persian/Arabic data
 */

/**
 * Normalizes Persian/Arabic numbers to English numbers
 * @param input The string containing Persian/Arabic numbers
 * @returns The string with Persian/Arabic numbers converted to English numbers
 */
export function normalizeNumbers(input: string | number | null | undefined): string {
    if (input === null || input === undefined) {
        return '';
    }

    const str = String(input);
    
    // Persian/Arabic number characters mapped to English equivalents
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    
    let result = str;
    
    // Replace Persian digits with English equivalents
    for (let i = 0; i < 10; i++) {
        const persianDigitRegex = new RegExp(persianDigits[i], 'g');
        const arabicDigitRegex = new RegExp(arabicDigits[i], 'g');
        
        result = result.replace(persianDigitRegex, i.toString());
        result = result.replace(arabicDigitRegex, i.toString());
    }
    
    return result;
}

/**
 * A general-purpose data normalization function that can normalize any field based on its type
 * 
 * @param data The data to normalize (can be any type)
 * @param options Optional configuration for normalization behavior
 * @returns The normalized data
 * 
 * @example
 * // Normalize a string with Persian numbers
 * normalizeData("۱۲۳") // returns "123"
 * 
 * @example
 * // Normalize a price value
 * normalizeData("۱۲,۳۴۵.۶۷", { type: "price" }) // returns "12345.67"
 * 
 * @example
 * // Normalize a URL
 * normalizeData("example.com", { type: "url" }) // returns "https://example.com"
 * 
 * @example
 * // Normalize an object with various fields
 * normalizeData({
 *   name: "Product  Name ",
 *   price: "۱۲,۳۴۵",
 *   quantity: "۱۰",
 *   url: "example.com"
 * }, {
 *   fieldOptions: {
 *     price: { type: "price" },
 *     quantity: { type: "number" },
 *     url: { type: "url" }
 *   }
 * })
 */
export function normalizeData<T>(
    data: T,
    options?: {
        type?: "text" | "number" | "price" | "url" | "boolean" | "array" | "object";
        fieldOptions?: Record<string, { type: "text" | "number" | "price" | "url" | "boolean" | "array" | "object" }>;
        defaultEmptyValue?: any;
    }
): T {
    // Handle null or undefined
    if (data === null || data === undefined) {
        return (options?.defaultEmptyValue ?? '') as T;
    }

    // Determine the type if not specified
    const type = options?.type || typeof data;

    // Normalize based on type
    switch (type) {
        case "text":
            // Normalize text: trim and normalize spaces
            if (typeof data === 'string') {
                return data.trim().replace(/\s+/g, ' ') as unknown as T;
            }
            return String(data).trim().replace(/\s+/g, ' ') as unknown as T;

        case "number":
            // Normalize to number: convert Persian/Arabic digits and parse as number
            if (typeof data === 'number') {
                return data as T;
            }
            const normalizedNumber = normalizeNumbers(data as string | number);
            const parsedNumber = parseFloat(normalizedNumber);
            return (isNaN(parsedNumber) ? 0 : parsedNumber) as unknown as T;

        case "price":
            // Normalize price: convert Persian/Arabic digits, remove non-numeric chars except decimal
            const normalizedPrice = normalizeNumbers(data as string | number);
            // Remove commas and other non-numeric characters except decimal point
            const cleanedPrice = normalizedPrice.replace(/[^\d.]/g, '');
            // Ensure there's only one decimal point
            const parts = cleanedPrice.split('.');
            const finalPrice = parts.length > 2 
                ? parts[0] + '.' + parts.slice(1).join('') 
                : cleanedPrice;
            return finalPrice as unknown as T;

        case "url":
            // Normalize URL: ensure it has a protocol
            if (typeof data !== 'string' || data.trim() === '') {
                return '' as unknown as T;
            }
            const trimmedUrl = data.trim();
            const normalizedUrl = !/^https?:\/\//i.test(trimmedUrl) 
                ? 'https://' + trimmedUrl 
                : trimmedUrl;
            return normalizedUrl as unknown as T;

        case "boolean":
            // Normalize boolean: convert truthy/falsy values
            if (typeof data === 'boolean') {
                return data as T;
            }
            if (typeof data === 'string') {
                const lowercased = data.toLowerCase().trim();
                return (lowercased === 'true' || lowercased === 'yes' || lowercased === '1') as unknown as T;
            }
            return Boolean(data) as unknown as T;

        case "array":
            // Normalize array: apply normalization to each element
            if (!Array.isArray(data)) {
                return ([] as unknown) as T;
            }
            return data.map(item => normalizeData(item)) as unknown as T;

        case "object":
            // Normalize object: apply normalization to each property
            if (typeof data !== 'object' || data === null) {
                return ({} as unknown) as T;
            }
            
            const result = { ...data as object } as Record<string, any>;
            
            // Apply normalization to each field based on fieldOptions
            Object.keys(result).forEach(key => {
                const fieldType = options?.fieldOptions?.[key]?.type;
                if (fieldType) {
                    result[key] = normalizeData(result[key], { type: fieldType });
                } else if (typeof result[key] === 'string') {
                    // Default string normalization
                    result[key] = normalizeData(result[key], { type: 'text' });
                } else if (typeof result[key] === 'object' && result[key] !== null) {
                    // Recursively normalize nested objects and arrays
                    if (Array.isArray(result[key])) {
                        result[key] = normalizeData(result[key], { type: 'array' });
                    } else {
                        result[key] = normalizeData(result[key], { type: 'object' });
                    }
                }
            });
            
            return result as unknown as T;

        default:
            // For other types, return as is
            return data;
    }
}
