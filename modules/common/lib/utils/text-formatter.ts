/**
 * Text formatting utilities
 * Contains functions for formatting text for display
 */

import { LANGUAGE } from "../../base/types/typing";
import { getCurrentLanguage } from "./language";

/**
 * Format a price based on the current language
 * @param price The price to format (string or number)
 * @param language Optional language override (if not provided, uses the current language from environment)
 * @returns The formatted price with appropriate currency
 */
export function formatPrice(price: string | number, language?: LANGUAGE): string {
  // If language is not provided, get it from the environment
  const currentLanguage = language || getCurrentLanguage();
  
  // Parse the price to a number
  let numericPrice: number;
  if (typeof price === 'string') {
    // Remove any non-numeric characters except decimal point
    const cleanedPrice = price.replace(/[^\d.]/g, '');
    numericPrice = parseFloat(cleanedPrice);
  } else {
    numericPrice = price;
  }

  // Check if the price is a valid number
  if (isNaN(numericPrice)) {
    return currentLanguage === LANGUAGE.PERSIAN ? 'قیمت نامشخص' : 'Price unknown';
  }

  // Format based on language
  if (currentLanguage === LANGUAGE.PERSIAN) {
    // Format for Persian: use Toman
    return `${numericPrice.toLocaleString()} تومان`;
  } else {
    // Format for English: use Dollar
    return `$${numericPrice.toFixed(2)}`;
  }
}
