export function addHours(date: Date, hours: number): Date {
    const resultDate = new Date(date.getTime());

    resultDate.setHours(date.getHours() + hours);

    return resultDate;
}

export function getHourDifference(date1: Date, date2: Date): number {
    const differenceInMilliSeconds = date2.getTime() - date1.getTime();

    const hoursDifference = Math.abs(
        differenceInMilliSeconds / (1000 * 60 * 60),
    );

    return Math.round(hoursDifference);
}

/**
 * Detects the time period (day, week, month, year) based on the date range
 * @param from Start date
 * @param to End date
 * @returns The detected time period or null if no specific period is detected
 */
export function detectTimePeriod(from: Date, to: Date): 'day' | 'week' | 'month' | 'year' | null {
    const diffMs = to.getTime() - from.getTime();
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    // Check if it's approximately a day (allow for small time differences)
    if (diffDays >= 0.9 && diffDays <= 1.1) {
        return 'day';
    }

    // Check if it's approximately a week (7 days with some tolerance)
    if (diffDays >= 6.9 && diffDays <= 7.1) {
        return 'week';
    }

    // Check if it's approximately a month (28-31 days with some tolerance)
    if (diffDays >= 27 && diffDays <= 32) {
        return 'month';
    }

    // Check if it's approximately a year (365-366 days with some tolerance)
    if (diffDays >= 364 && diffDays <= 367) {
        return 'year';
    }

    return null;
}

/**
 * Generates a date range for a specific time period based on the end date (today)
 * @param endDate The reference end date (typically today)
 * @param period The time period (day, week, month, year)
 * @returns An object with from and to dates
 */
export function generateDateRange(
    endDate: Date,
    period: 'day' | 'week' | 'month' | 'year'
): { from: Date, to: Date } {
    // Create a copy of the end date to avoid modifying the original
    const today = new Date(endDate);

    // For all periods, end date is today at 23:59:59.999
    const to = new Date(today);
    to.setHours(23, 59, 59, 999);

    // Initialize from date
    let from = new Date(today);

    switch (period) {
        case 'day':
            // Day: Today from 00:00:00 to 23:59:59
            from.setHours(0, 0, 0, 0);
            break;

        case 'week':
            // Week: Today and the previous 6 days
            from.setDate(today.getDate() - 6);
            from.setHours(0, 0, 0, 0);
            break;

        case 'month':
            // Month: Today and the previous 29/30/31 days
            from.setDate(today.getDate() - 30);
            from.setHours(0, 0, 0, 0);
            break;

        case 'year':
            // Year: Today and the previous 364/365 days
            from.setFullYear(today.getFullYear() - 1);
            from.setHours(0, 0, 0, 0);
            break;
    }

    return { from, to };
}
