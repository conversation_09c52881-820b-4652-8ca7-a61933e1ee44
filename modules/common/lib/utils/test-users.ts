import { TestUser } from "../../base/types/typing";
import Logger from "../metrics/logger";


export class TestUsersService {
    private static _testUsers: TestUser[] | null = null;

    /**
     * Parse test users from environment variable
     * Format: phone1:password1,phone2:password2,...
     */
    private static _parseTestUsers(): TestUser[] {
        if (this._testUsers !== null) {
            return this._testUsers;
        }

        const testUsersEnv = process.env.TEST_USERS;
        if (!testUsersEnv || testUsersEnv.trim() === '') {
            Logger.info('[TestUsersService] No TEST_USERS environment variable found');
            this._testUsers = [];
            return this._testUsers;
        }

        try {
            this._testUsers = testUsersEnv
                .split(',')
                .map(userStr => userStr.trim())
                .filter(userStr => userStr.length > 0)
                .map(userStr => {
                    const [phone, password] = userStr.split(':');
                    if (!phone || !password) {
                        throw new Error(`Invalid test user format: ${userStr}`);
                    }
                    return { phone: phone.trim(), password: password.trim() };
                });

            Logger.info('[TestUsersService] Parsed test users', {
                count: this._testUsers.length,
                phones: this._testUsers.map(u => u.phone)
            });

            return this._testUsers;
        } catch (error: any) {
            Logger.error('[TestUsersService] Failed to parse TEST_USERS', {
                testUsersEnv,
                error: error?.message || 'Unknown error'
            });
            this._testUsers = [];
            return this._testUsers;
        }
    }

    /**
     * Check if a phone number is a test user
     */
    static isTestUser(phone: string): boolean {
        const testUsers = this._parseTestUsers();
        const isTest = testUsers.some(user => user.phone === phone);
        
        // Also check legacy FIX_USER for backward compatibility
        const isLegacyTest = phone === process.env.FIX_USER;
        
        return isTest || isLegacyTest;
    }

    /**
     * Get the password for a test user
     */
    static getTestUserPassword(phone: string): string | null {
        const testUsers = this._parseTestUsers();
        const testUser = testUsers.find(user => user.phone === phone);
        
        if (testUser) {
            return testUser.password;
        }

        // Check legacy FIX_USER for backward compatibility
        if (phone === process.env.FIX_USER && process.env.FIX_PASS) {
            return process.env.FIX_PASS;
        }

        return null;
    }

    /**
     * Get all test users
     */
    static getAllTestUsers(): TestUser[] {
        const testUsers = this._parseTestUsers();
        
        // Add legacy user if it exists and is not already in the list
        if (process.env.FIX_USER && process.env.FIX_PASS) {
            const legacyUserExists = testUsers.some(user => user.phone === process.env.FIX_USER);
            if (!legacyUserExists) {
                testUsers.push({
                    phone: process.env.FIX_USER,
                    password: process.env.FIX_PASS
                });
            }
        }

        return testUsers;
    }
}
