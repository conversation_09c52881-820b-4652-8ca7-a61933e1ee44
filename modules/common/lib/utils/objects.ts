import { isEqual as lIsEqual } from "lodash";

export const pick = <T extends object>(obj: T, ...props: (keyof T)[]) => {
    return props.reduce((result, prop) => {
        if (prop in obj) {
            result[prop] = obj[prop];
        }
        return result;
    }, {} as T);
};

export const exclude = <T extends object>(obj: T, ...props: (keyof T)[]) => {
    const deletableObj = { ...obj };
    return props.reduce((result, prop) => {
        delete deletableObj[prop];
        return deletableObj;
    }, {} as T);
};

export const isEqual = (obj1: object, obj2: object) => {
    return lIsEqual(obj1, obj2);
};
