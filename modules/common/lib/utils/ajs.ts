/* eslint-disable @typescript-eslint/strict-boolean-expressions */
/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { JSONSchemaType } from "ajv";

import { utils } from "..";

const NULLISH_KEYS = ["_undefined", "undefined", "_null", "null"];

function extractValues(
    d: any,
    ref: any,
    property: any,
    prefix: string,
    key: string,
) {
    const value = d[`${property?.prefix ?? prefix}${property?.from ?? key}`];
    if (value !== null) {
        ref[key] = value;
    } else if (property.default !== undefined) {
        ref[key] = property.default;
    }
    return value;
}

export interface Metadata {
    pagination: {
        pageNumber: number;
        pageSize: number;
        totalRecords: number;
    };
}

const serialize = <T>(
    schema: JSONSchemaType<T>,
    data: any[],
    dependency?: string,
    dependencyValue?: number,
) => {
    const obj: any = {};

    const thisSchema = schema?.items ?? schema;
    const { prefix, properties, uniqueId } = thisSchema;

    for (const d of data) {
        if (utils.isNotNil(dependency)) {
            if (d[dependency] !== dependencyValue) {
                continue;
            }
        }
        const idKey = `${prefix}${uniqueId ?? "id"}`;
        const id = d[idKey];
        if (id === null) {
            continue;
        }

        const refId = `_${id}`;
        if (utils.isNil(obj[refId])) {
            obj[refId] = {} as T;
        }
        const ref = obj[refId];

        for (const k in properties) {
            const key = k;
            const property = properties[key];
            if (property.type === "array") {
                if (property.ignore === true) {
                    extractValues(d, ref, property, prefix, key);
                } else {
                    ref[key] = serialize(property, data, idKey, id);
                }
            } else if (property.type === "object") {
                ref[key] = serialize(property, data, idKey, id);
            } else {
                if (utils.isNotNil(property.oneOf)) {
                    for (const definition of property.oneOf) {
                        const value = extractValues(
                            d,
                            ref,
                            definition,
                            prefix,
                            key,
                        );
                        if (utils.isNotNil(value)) {
                            break;
                        }
                    }
                } else {
                    extractValues(d, ref, property, prefix, key);
                }
            }
        }
    }

    if (schema.type === "array") {
        if (Object.keys(obj).every((elem) => NULLISH_KEYS.includes(elem))) {
            return [];
        }
        return Object.values(obj);
    } else {
        if (Object.keys(obj).every((elem) => NULLISH_KEYS.includes(elem))) {
            return null;
        }
        return Object.values(obj)[0];
    }
};

export const serializer = <T>(schema: JSONSchemaType<T>) => {
    return (data: any | any[]): T => {
        if (!data?.[0]) {
            data = [data];
        }

        const serialized = serialize<T>(schema, data);

        return serialized as T;
    };
};

export const paginatedSerializer = <T extends any[]>(
    schema: JSONSchemaType<T>,
) => {
    return (data: any | any[]): { metadata: Metadata; data: T } => {
        if (!data?.[0]) {
            data = [data];
        }

        const serialized = serialize<T>(schema, data);

        const pageNumber = data?.[0]?.page_number ?? 1;
        const pageSize = data?.[0]?.page_size ?? 30;
        const totalRecords = data?.[0]?.total_records ?? 0;

        const from = (pageNumber - 1) * pageSize;
        const to = from + pageSize;

        const serializedData = (serialized as T).slice(from, to) as T;
        return {
            data: serializedData,
            metadata: {
                pagination: {
                    pageNumber,
                    pageSize,
                    totalRecords,
                },
            },
        };
    };
};
