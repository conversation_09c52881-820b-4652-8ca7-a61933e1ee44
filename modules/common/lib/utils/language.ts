/**
 * Language utility functions
 * Provides helpers for language detection and handling
 */

import { LANGUAGE } from "../../base/types/typing";


export function getCurrentLanguage(): LANGUAGE {
  // Get language from environment variable
  const appLang = process.env.PUBLIC_APP_LANG;

  // Log the raw value for debugging
  console.log(`[language:getCurrentLanguage] Raw PUBLIC_APP_LANG value: ${appLang || 'not set'}`);

  // Validate and return the language (only "en" is considered English, everything else is Persian)
  if (appLang === LANGUAGE.ENGLISH) {
    console.log(`[language:getCurrentLanguage] Using English language`);
    return LANGUAGE.ENGLISH;
  } else {
    console.log(`[language:getCurrentLanguage] Using Persian language`);
    return LANGUAGE.PERSIAN;
  }
}

/**
 * Validate and normalize a language code
 * @param lang The language code to validate
 * @returns A valid LANGUAGE (ENGLISH or PERSIAN)
 * @note This function no longer throws errors for null/undefined values.
 *       Error checking is now handled in getSuccessMessage function.
 */
export function validateLanguage(lang: string | null | undefined): LANGUAGE {
  return lang === LANGUAGE.ENGLISH ? LANGUAGE.ENGLISH : LANGUAGE.PERSIAN;
}