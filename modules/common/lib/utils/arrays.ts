/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { groupBy, keyBy, mergeWith, pick } from "lodash";

import { utils } from "..";

export function mergeArraysByKey<
    T extends Record<string, any>,
    U extends Record<string, any>,
>(
    array1: T[],
    array2: U[],
    sharedKey: keyof T & keyof U,
    outPutKeys: (keyof T | keyof U)[],
): Array<Partial<T & U>> {
    const mergedObject = mergeWith(
        {},
        keyBy(array1, sharedKey),
        keyBy(array2, sharedKey),
    );

    const mergedArray = Object.values(mergedObject);
    return mergedArray.map((obj) => pick(obj, outPutKeys));
}

export function mergeArraysByDistinctKey<
    T extends Record<string, any>,
    U extends Record<string, any>,
>(
    array1: T[],
    array2: U[],
    key1: keyof T,
    key2: keyof U,
): Array<Partial<T & U>> {
    const mergedObject = mergeWith(
        {},
        keyBy(array1, key1),
        keyBy(array2, key2),
    );
    return Object.values(mergedObject);
}

export function aggregateArraysByKey<T extends Record<string, any>>(
    array1: T[],
    array2: T[],
    key: keyof T,
    sumKey: keyof T,
): Array<T> {
    const groupedObject = groupBy([...array1, ...array2], (obj) => obj[key]);
    return Object.values(groupedObject).map((items) => {
        return items.reduce((sum, item) => {
            return {
                ...sum,
                ...item,
                [sumKey]: (sum?.[sumKey] ?? 0) + item[sumKey],
            };
        }, {} as T);
    });
}

export function arrayToObject<T extends Record<string, any>>(
    array: T[],
    key: keyof T,
): Record<keyof T, T> {
    return mergeWith({}, keyBy(array, key));
}

export function splitArrayIntoChunks<T extends Record<string, any>>(
    array: T[],
    chunkSize: number,
): T[][] {
    return Array.from(
        { length: Math.ceil(array.length / chunkSize) },
        (_, index) =>
            array.slice(index * chunkSize, index * chunkSize + chunkSize),
    );
}

export function groupByKey<T extends Record<string, any>>(
    list: T[],
    key: keyof T,
): T[][] {
    const hash: Record<string, T[]> = {};

    for (const item of list) {
        const value = item[key] as unknown as string;
        if (utils.isNil(hash[value])) {
            hash[value] = [];
        }
        hash[value].push(item);
    }

    return Object.values(hash);
}

export function removeDuplicates<T extends Record<string, any>>(
    array: T[],
    keys: (keyof T)[],
): T[] {
    const uniqueItems = new Set<string>();
    return array.filter((item) => {
        const key = keys.map((k) => item[k]).join("|");
        if (uniqueItems.has(key)) {
            return false;
        } else {
            uniqueItems.add(key);
            return true;
        }
    });
}
