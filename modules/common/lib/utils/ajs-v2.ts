/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
/* eslint-disable @typescript-eslint/strict-boolean-expressions */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { JSONSchemaType } from "ajv";

import { utils } from "..";

// const NULLISH_KEYS = ["_undefined", "undefined", "_null", "null"];

interface Metadata {
    pagination: {
        pageNumber: number;
        pageSize: number;
        totalRecords: number;
    };
}

function extractValues(
    data: any,
    ref: any,
    property: any,
    prefix: string,
    key: string,
) {
    const value = data[`${property?.prefix ?? prefix}${property?.from ?? key}`];
    if (value !== null) {
        ref[key] = value;
    } else if (property.default !== undefined) {
        ref[key] = property.default;
    }
    return value;
}

const divide = <T>(
    obj: any,
    schema: JSONSchemaType<T>,
    data: any,
    from: string,
) => {
    const thisSchema = schema?.items ?? schema;
    const { prefix, properties, uniqueId } = thisSchema;

    const idKey = `${prefix}${uniqueId ?? "id"}`;
    const id = data[idKey];
    if (id === null) {
        return;
    }

    if (utils.isNil(obj[from])) {
        obj[from] = {};
    }
    const refId = `_${id}`;
    if (utils.isNil(obj[from][refId])) {
        obj[from][refId] = {} as T;
    }
    const ref = obj[from][refId];

    for (const k in properties) {
        const key = k;
        const property = properties[key];
        if (property.type === "array") {
            if (property.ignore === true) {
                extractValues(data, ref, property, prefix, key);
            } else {
                divide(ref, property, data, key);
            }
        } else if (property.type === "object") {
            divide(ref, property, data, key);
        } else {
            if (utils.isNotNil(property.oneOf)) {
                for (const definition of property.oneOf) {
                    const value = extractValues(
                        data,
                        ref,
                        definition,
                        prefix,
                        key,
                    );
                    if (utils.isNotNil(value)) {
                        break;
                    }
                }
            } else {
                extractValues(data, ref, property, prefix, key);
            }
        }
    }
};

const mergeArray = <T>(obj: any, schema: JSONSchemaType<T>) => {
    const thisSchema = schema?.items ?? schema;
    const { properties } = thisSchema;

    for (const key in properties) {
        const property = properties[key];
        if (property.type === "array" || property.type === "object") {
            merge(obj, property, key);
        }
    }
};

const merge = <T>(obj: any, schema: JSONSchemaType<T>, from: string) => {
    if (schema.type === "array") {
        obj[from] = Object.values(obj?.[from] ?? {});
        obj[from].forEach((inner: any) => {
            mergeArray(inner, schema.items);
        });
        return;
    } else if (schema.type === "object") {
        if (utils.isNotNil(obj[from])) {
            obj[from] = Object.values(obj[from])[0];
        } else {
            obj[from] = null;
        }
    }

    const thisSchema = schema?.items ?? schema;
    const { properties } = thisSchema;

    for (const key in properties) {
        const property = properties[key];
        if (property.type === "array") {
            if (utils.isNotNil(obj[from])) {
                merge(obj[from], property, key);
            } else {
                obj[from] = [];
            }
        } else if (property.type === "object") {
            if (utils.isNotNil(obj[from])) {
                merge(obj[from], property, key);
            } else {
                obj[from] = null;
            }
        }
    }
};

const serialize = <T>(schema: JSONSchemaType<T>, data: any[]) => {
    const thisSchema = schema?.items ?? schema;

    const obj: any = {};
    for (const d of data) {
        divide(obj, thisSchema, d, "root");
    }

    merge(obj, schema, "root");

    return obj["root"];
};

export const serializerV2 = <T>(schema: JSONSchemaType<T>) => {
    return (data: any | any[]): T => {
        if (!data?.[0]) {
            data = [data];
        }

        const serialized = serialize<T>(schema, data);

        return serialized as T;
    };
};

export const paginatedSerializerV2 = <T extends any[]>(
    schema: JSONSchemaType<T>,
) => {
    return (data: any | any[]): { metadata: Metadata; data: T } => {
        if (!data?.[0]) {
            data = [data];
        }

        const serialized = serialize<T>(schema, data);

        const pageNumber = data?.[0]?.page_number ?? 1;
        const pageSize = data?.[0]?.page_size ?? 30;
        const totalRecords = data?.[0]?.total_records ?? 0;

        const from = (pageNumber - 1) * pageSize;
        const to = from + pageSize;

        const serializedData = (serialized as T).slice(from, to) as T;
        return {
            data: serializedData,
            metadata: {
                pagination: {
                    pageNumber,
                    pageSize,
                    totalRecords,
                },
            },
        };
    };
};
