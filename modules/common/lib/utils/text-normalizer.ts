/**
 * Utility functions for normalizing text responses
 * Removes Markdown formatting and ensures consistent formatting for Instagram
 */

/**
 * Removes Markdown formatting from text
 * @param text The text to normalize
 * @returns The normalized text without Markdown formatting
 */
export function removeMarkdownFormatting(text: string): string {
    if (!text) return text;

    // Replace bold formatting (**text**)
    text = text.replace(/\*\*(.*?)\*\*/g, '$1');

    // Replace italic formatting (*text* or _text_)
    text = text.replace(/\*(.*?)\*/g, '$1');
    text = text.replace(/_(.*?)_/g, '$1');

    // Replace headers (# Header)
    text = text.replace(/^#+\s+(.*?)$/gm, '$1');

    // Replace code blocks (```code```)
    text = text.replace(/```([\s\S]*?)```/g, '$1');

    // Replace inline code (`code`)
    text = text.replace(/`(.*?)`/g, '$1');

    // Replace numbered list items with plain text
    text = text.replace(/^\s*\d+\.\s+\*\*(.*?)\*\*/gm, '$1:');

    // Replace bullet points with plain text
    text = text.replace(/^\s*-\s+\*\*(.*?)\*\*/gm, '$1:');

    return text;
}

/**
 * Formats cart response for Instagram
 * @param text The cart response text
 * @returns The formatted cart response
 */
export function normalizeCartResponse(text: string): string {
    // Ensure consistent formatting for cart sections
    let formatted = text;

    // Format section headers
    formatted = formatted.replace(/سبد خرید شما:/g, 'سبد خرید شما:');
    formatted = formatted.replace(/ایتم‌ها:/g, 'ایتم‌ها:');
    formatted = formatted.replace(/قیمت کل:/g, 'قیمت کل:');

    // Format item details
    formatted = formatted.replace(/تعداد:/g, 'تعداد:');
    formatted = formatted.replace(/قیمت:/g, 'قیمت:');
    formatted = formatted.replace(/وضعیت:/g, 'وضعیت:');

    return formatted;
}

/**
 * Formats order response for Instagram
 * @param text The order response text
 * @returns The formatted order response
 */
export function normalizeOrderResponse(text: string): string {
    // Ensure consistent formatting for order sections
    let formatted = text;

    // Format section headers
    formatted = formatted.replace(/سفارش شماره/g, 'سفارش شماره');
    formatted = formatted.replace(/سفارش شما:/g, 'سفارش شما:');
    formatted = formatted.replace(/وضعیت:/g, 'وضعیت:');
    formatted = formatted.replace(/آیتم‌ها:/g, 'آیتم‌ها:');
    formatted = formatted.replace(/قیمت کل:/g, 'قیمت کل:');
    formatted = formatted.replace(/جزئیات محصولات:/g, 'جزئیات محصولات:');

    // Format item details
    formatted = formatted.replace(/تعداد:/g, 'تعداد:');
    formatted = formatted.replace(/قیمت:/g, 'قیمت:');

    return formatted;
}
