import Redis from "ioredis";
import { injectable, singleton } from "tsyringe";

const { REDIS_PORT, REDIS_HOST } = process.env;

@singleton()
@injectable()
export class RedisConnection {
    public redis: Redis;

    constructor() {
        this.redis = new Redis({
            port: Number(REDIS_PORT),
            host: REDIS_HOST,
            maxRetriesPerRequest: null,
        });
    }

    public getConnection(): Redis {
        return this.redis;
    }
}

export function createRedisConnection(): RedisConnection {
    return new RedisConnection();
}
