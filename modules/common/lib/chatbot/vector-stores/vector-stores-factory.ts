import { type Embeddings } from "@langchain/core/embeddings";
import { isNil } from "../../utils";
import {
    ElasticVectorStore,
    ElasticVectorStoreArgs,
} from "./elastic-vector-store";
import { VECTOR_STORE_PROVIDER } from "../../../base/types/typing";

export type VectorStoreArgs = ElasticVectorStoreArgs;

export function vectorStoreFactory(
    model: VECTOR_STORE_PROVIDER.ELASTIC_SEARCH,
    embeddings: Embeddings,
    args?: VectorStoreArgs,
) {
    if (model === VECTOR_STORE_PROVIDER.ELASTIC_SEARCH) {
        if (isNil(args?.index)) {
            throw new Error("Invalid arguments");
        }

        return new ElasticVectorStore(embeddings, args);
    }

    throw new Error("Unsupported model name");
}
