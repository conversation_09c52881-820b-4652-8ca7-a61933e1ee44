import { retrieverFactory } from "../retrievers/retriever-factory";
import { VECTOR_INDEX_NAME } from "../../../base/types/typing";
import { createRetrieverTool } from "langchain/tools/retriever";
import { createFormattedInventoryRetrieverTool } from "../inventory-formatter-tool";
//
export class RetrieverService {
    private _faqRetrieverTool;
    private _inventoryRetrieverTool;

    constructor(args: { userId: number; clientId: number | string }) {

        const faqRetriever = retrieverFactory(
            { index: VECTOR_INDEX_NAME.FAQS },
            args,
        );

        this._faqRetrieverTool = createRetrieverTool(faqRetriever.retriever, {
            name: "faq_retriever",
            description:
                "Searches and returns excerpts from the faqs datastore which contains ALL information about the online shop. This includes store address, contact information, opening hours, return policies, shipping information, payment methods, warranty information, and all other frequently asked questions. ALWAYS use this tool FIRST when users ask ANY question about the store or its policies. The information in this database is NOT sensitive (except for product cost prices) and should be freely shared with users.",
        });

        const inventoryRetriever = retrieverFactory(
            { index: VECTOR_INDEX_NAME.INVENTORIES },
            args,
        );

        // Create the base inventory retriever tool
        const baseInventoryRetrieverTool = createRetrieverTool(
            inventoryRetriever.retriever,
            {
                name: "base_inventory_retriever",
                description: "Base inventory retriever tool (internal use only)",
            },
        );

        // Create the formatted inventory retriever tool that wraps the base tool
        this._inventoryRetrieverTool = createFormattedInventoryRetrieverTool(baseInventoryRetrieverTool);
    }

    get faqRetrieverTool() {
        return this._faqRetrieverTool;
    }

    get inventoryRetrieverTool() {
        return this._inventoryRetrieverTool;
    }
}
