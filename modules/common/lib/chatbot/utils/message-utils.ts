import {
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
  } from "@langchain/core/messages";
  import { CHATBOT_MESSAGE_TYPES} from "../../../base/types/typing";
  
  const isDev = process.env.NODE_ENV !== "production";
  
  export class MessageUtils {
    static createMessage(
      type: CHATBOT_MESSAGE_TYPES,
      text: string
    ): BaseMessage | undefined {
      if (type === CHATBOT_MESSAGE_TYPES.SYSTEM) {
        return new SystemMessage(text);
      } else if (type === CHATBOT_MESSAGE_TYPES.USER) {
        return new HumanMessage(text);
      } else if (type === CHATBOT_MESSAGE_TYPES.AI) {
        return new AIMessage(text);
      }
      return undefined;
    }

  }
  