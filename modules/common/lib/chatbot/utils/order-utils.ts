import { ClientOrderResponse } from "../../../../orders/apps/orders/types/schemas";
import { normalizeOrderResponse } from "../../utils/text-normalizer";
import { formatPrice } from "../../utils/text-formatter";
import { LANGUAGE } from "../../../base/types/typing";
import { getCurrentLanguage } from "../../utils/language";

/**
 * Utility functions for handling orders in the chatbot
 */
export class OrderUtils {
    /**
     * Translates order status to Persian text
     */
    static getOrderStatusText(status: string): string {
        // Get current language
        const currentLanguage = getCurrentLanguage();

        if (currentLanguage === LANGUAGE.ENGLISH) {
            // English status text
            switch (status) {
                case "PENDING_SELLER_REVIEW":
                    return "Waiting for seller review";
                case "CONFIRMED_WAITING_PAYMENT":
                    return "Confirmed, waiting for payment";
                case "PAYMENT_CONFIRMED_PROCESSING":
                    return "Payment confirmed, processing";
                case "OUT_FOR_DELIVERY":
                    return "Out for delivery";
                case "DELIVERED":
                    return "Delivered";
                default:
                    return "Unknown";
            }
        } else {
            // Persian status text
            switch (status) {
                case "PENDING_SELLER_REVIEW":
                    return "در انتظار بررسی فروشنده";
                case "CONFIRMED_WAITING_PAYMENT":
                    return "تایید شده و در انتظار پرداخت";
                case "PAYMENT_CONFIRMED_PROCESSING":
                    return "پرداخت شده و در حال پردازش";
                case "OUT_FOR_DELIVERY":
                    return "مرسوله در حال تحویل";
                case "DELIVERED":
                    return "تحویل نهایی";
                default:
                    return "نامشخص";
            }
        }
    }

    /**
     * Formats a list of orders into a readable text based on current language
     * @param orders List of client orders to format
     * @param totalCount Optional total count of orders (used when displaying a limited subset)
     * @returns Formatted order text with Markdown formatting removed
     */
    static formatOrdersList(
        orders: ClientOrderResponse[],
        totalCount?: number,
    ): string {
        // Get current language
        const currentLanguage = getCurrentLanguage();

        if (orders.length === 0) {
            return currentLanguage === LANGUAGE.ENGLISH
                ? "You don't have any orders."
                : "شما هیچ سفارشی ندارید.";
        }

        let response = currentLanguage === LANGUAGE.ENGLISH
            ? "Your orders:\n\n"
            : "لیست سفارشات شما:\n\n";

        // If we're showing a limited number of orders, add a note
        if (totalCount && totalCount > orders.length) {
            response += currentLanguage === LANGUAGE.ENGLISH
                ? `(Showing ${orders.length} recent orders out of ${totalCount} total)\n\n`
                : `(نمایش ${orders.length} سفارش اخیر از مجموع ${totalCount} سفارش)\n\n`;
        }

        for (const order of orders) {
            const statusText = this.getOrderStatusText(order.status);

            // Format items with individual prices
            const itemsList = order.items
                .map(
                    (item: { amount: number; name: string; price: string }) => {
                        // Format the item price using our utility function
                        const formattedPrice = formatPrice(item.price, currentLanguage);

                        return currentLanguage === LANGUAGE.ENGLISH
                            ? `  - ${item.amount} ${item.name}: ${formattedPrice}`
                            : `  - ${item.amount} عدد ${item.name}: ${formattedPrice}`;
                    },
                )
                .join("\n");

            // Format total price using our utility function
            const formattedTotalPrice = formatPrice(order.price, currentLanguage);

            if (currentLanguage === LANGUAGE.ENGLISH) {
                response += `Order #${order.id}:\n`;
                response += `- Status: ${statusText}\n`;
                response += `- Items: \n${itemsList}\n`;
                response += `- Total price: ${formattedTotalPrice}\n\n`;
            } else {
                response += `سفارش شماره ${order.id}:\n`;
                response += `- وضعیت: ${statusText}\n`;
                response += `- اقلام: \n${itemsList}\n`;
                response += `- قیمت کل: ${formattedTotalPrice}\n\n`;
            }
        }

        // Normalize the response to remove any Markdown formatting
        return normalizeOrderResponse(response.trim());
    }

    /**
     * Formats a single order into a readable text based on current language
     * @param order Order to format
     * @returns Formatted order text with Markdown formatting removed
     */
    static formatOrderDetails(order: ClientOrderResponse): string {
        // Get current language
        const currentLanguage = getCurrentLanguage();

        if (!order) {
            return currentLanguage === LANGUAGE.ENGLISH
                ? "Order not found."
                : "سفارش مورد نظر یافت نشد.";
        }

        const statusText = this.getOrderStatusText(order.status);

        // Format items with more details
        const itemsList = order.items
            .map(
                (
                    item: { amount: number; name: string; price: string },
                    index: number,
                ) => {
                    // Format the item price using our utility function
                    const formattedPrice = formatPrice(item.price, currentLanguage);

                    if (currentLanguage === LANGUAGE.ENGLISH) {
                        return `${index + 1}. ${item.name}\n   - Quantity: ${item.amount}\n   - Price: ${formattedPrice}`;
                    } else {
                        return `${index + 1}. ${item.name}\n   - تعداد: ${item.amount} عدد\n   - قیمت: ${formattedPrice}`;
                    }
                },
            )
            .join("\n\n");

        // Format price using our utility function
        const formattedTotalPrice = formatPrice(order.price, currentLanguage);

        let response;
        if (currentLanguage === LANGUAGE.ENGLISH) {
            response = `Order #${order.id}:\n\n`;
            response += `- Status: ${statusText}\n`;
            response += `- Product details:\n\n${itemsList}\n\n`;
            response += `- Total price: ${formattedTotalPrice}`;
        } else {
            response = `سفارش شماره ${order.id}:\n\n`;
            response += `- وضعیت: ${statusText}\n`;
            response += `- جزئیات محصولات:\n\n${itemsList}\n\n`;
            response += `- قیمت کل: ${formattedTotalPrice}`;
        }

        // Normalize the response to remove any Markdown formatting
        return normalizeOrderResponse(response);
    }

    /**
     * Checks if a message is related to orders
     */
    static isOrderQuery(text: string): boolean {
        const lowerText = text.toLowerCase();

        // Persian order-related terms
        const persianTerms = ["سفارش", "سفارشات", "سفارش‌های من"];

        // English order-related terms
        const englishTerms = ["order", "orders", "my order", "my orders", "purchase", "purchases"];

        // Check if any Persian term is included
        const hasPersianTerm = persianTerms.some(term => lowerText.includes(term));

        // Check if any English term is included
        const hasEnglishTerm = englishTerms.some(term => lowerText.includes(term));

        return hasPersianTerm || hasEnglishTerm;
    }

    /**
     * Process raw order text to ensure proper currency formatting
     * @param orderText The raw order text
     * @returns Properly formatted order text
     */
    static processOrderText(orderText: string): string {
        // Get current language
        const currentLanguage = getCurrentLanguage();

        // If we're in English mode, replace "Toman" with "$" in the text
        if (currentLanguage === LANGUAGE.ENGLISH) {
            // Replace price patterns like "X.XX Toman" or "X,XXX Toman" with "$X.XX"
            let formattedText = orderText.replace(/(\d+(?:[.,]\d+)?)\s*(?:Toman|تومان)/gi, (_, price) => {
                // Clean the price and convert to a number
                const cleanPrice = price.replace(/,/g, '');
                const numericPrice = parseFloat(cleanPrice);

                // Format as dollar
                return `$${numericPrice.toFixed(2)}`;
            });

            return formattedText;
        }

        return orderText;
    }
}
