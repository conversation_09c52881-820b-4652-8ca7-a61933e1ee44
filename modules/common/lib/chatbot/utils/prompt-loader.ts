import { SystemMessage } from "@langchain/core/messages";
import {
  ragPromptEn,
  cartPromptEn,
  orderPromptEn,
  inventoryPromptEn,
  conversationStatePromptEn,
} from "../../../base/constant/prompts/language/en";
import {
  ragPromptFa,
  cartPromptFa,
  orderPromptFa,
  inventoryPromptFa,
  conversationStatePromptFa,
} from "../../../base/constant/prompts/language/fa";

import { LANGUAGE } from "../../../base/types/typing";

interface PromptOptions {
  userId: string | number;
}

/**
 * Get a prompt for the specified type
 * @param promptType The type of prompt to get
 * @param options Options for customizing the prompt
 * @returns A SystemMessage containing the prompt
 */
export async function getPrompt(
  promptType: string,
  options: PromptOptions & { lang?: LANGUAGE; persona?: string }
): Promise<SystemMessage> {
  console.log(`[prompt-loader] Getting prompt for type: ${promptType}`);

  // Determine language from options or environment variable
  const optionsLang = options.lang;
  const envLang = process.env.PUBLIC_APP_LANG === LANGUAGE.ENGLISH ? LANGUAGE.ENGLISH : LANGUAGE.PERSIAN;

  // Log detailed information about language selection
  console.log(`[prompt-loader] Language from options: ${optionsLang || 'not provided'}`);
  console.log(`[prompt-loader] Language from PUBLIC_APP_LANG: ${envLang || 'not set'}`);

  // Check if language is provided either in options or environment variable
  if (!optionsLang && !process.env.PUBLIC_APP_LANG) {
    throw new Error("PUBLIC_APP_LANG environment variable must be defined! Please set it to 'en' or 'fa' in your environment.");
  }

  // Select language with priority: options > env var
  const lang: LANGUAGE = optionsLang || envLang;
  console.log(`[prompt-loader] Final language selected: ${lang}`);

  try {
    switch (promptType) {
      case 'sales_agent':
        // Select the appropriate prompts based on language
        const ragPromptByLang = lang === LANGUAGE.ENGLISH ? ragPromptEn : ragPromptFa;
        const cartPromptByLang = lang === LANGUAGE.ENGLISH ? cartPromptEn : cartPromptFa;
        const orderPromptByLang = lang === LANGUAGE.ENGLISH ? orderPromptEn : orderPromptFa;
        const inventoryPromptByLang = lang === LANGUAGE.ENGLISH ? inventoryPromptEn : inventoryPromptFa;
        const conversationStatePromptByLang = lang === LANGUAGE.ENGLISH ? conversationStatePromptEn : conversationStatePromptFa;

        // Combine all prompts for the sales agent
        const combinedContent = combinePrompts([
          extractContent(ragPromptByLang),
          extractContent(cartPromptByLang),
          extractContent(orderPromptByLang),
          extractContent(inventoryPromptByLang),
          extractContent(conversationStatePromptByLang),
        ]);

        // Add user-specific information
        const customizedContent = `USER_ID: ${options.userId}\n\n${combinedContent}`;

        console.log(`[prompt-loader] Created combined prompt (${customizedContent.length} chars)`);
        return new SystemMessage(customizedContent);

      default:
        // Default to using just the RAG prompt based on language
        const defaultPrompt = lang === LANGUAGE.ENGLISH ? ragPromptEn : ragPromptFa;
        const content = extractContent(defaultPrompt);
        return new SystemMessage(content);
    }
  } catch (error) {
    console.error("[prompt-loader] Error creating prompt:", error);
    return createFallbackPrompt(lang);
  }
}

/**
 * Combine multiple prompts into a single string
 * @param prompts Array of prompt strings
 * @returns Combined prompt string
 */
function combinePrompts(prompts: string[]): string {
  const filteredPrompts = prompts.filter(Boolean);
  console.log(`[prompt-loader] Combining ${filteredPrompts.length} prompts`);
  return filteredPrompts.join('\n\n');
}

/**
 * Extract content from a prompt object
 * @param prompt The prompt object
 * @returns The content of the prompt as a string
 */
function extractContent(prompt: any): string {
  if (!prompt) return "";

  // If it's already a string, return it
  if (typeof prompt === 'string') return prompt;

  // If it's an object, try to extract the content
  if (typeof prompt === 'object') {
    // Try different properties that might contain the content
    if ('content' in prompt && typeof prompt.content === 'string') {
      return prompt.content;
    }

    if ('_content' in prompt && typeof prompt._content === 'string') {
      return prompt._content;
    }

    // For older versions of LangChain
    if ('text' in prompt && typeof prompt.text === 'string') {
      return prompt.text;
    }

    // Try toString as a last resort
    try {
      const str = prompt.toString();
      if (str && str !== '[object Object]') {
        return str;
      }
    } catch (e) {
      // Ignore toString errors
    }
  }

  return "";
}

/**
 * Create a fallback prompt if everything else fails
 * @param lang The language to use for the fallback prompt
 * @returns A SystemMessage with a basic prompt
 * @throws Error if lang is not provided
 */
function createFallbackPrompt(lang: LANGUAGE): SystemMessage {
  // Check if language is provided
  if (!lang) {
    const envLang = process.env.PUBLIC_APP_LANG;
    if (!envLang) {
      throw new Error("PUBLIC_APP_LANG environment variable must be defined! Please set it to 'en' or 'fa' in your environment.");
    }
    lang = envLang === LANGUAGE.ENGLISH ? LANGUAGE.ENGLISH : LANGUAGE.PERSIAN;
  }

  console.log(`[prompt-loader:createFallbackPrompt] Creating fallback prompt with language: ${lang}`);

  // Log environment variable for debugging
  console.log(`[prompt-loader:createFallbackPrompt] PUBLIC_APP_LANG from env: ${process.env.PUBLIC_APP_LANG || 'not set'}`);

  if (lang === LANGUAGE.ENGLISH) {
    console.log(`[prompt-loader:createFallbackPrompt] Using English fallback prompt`);
    return new SystemMessage(
      "You are a helpful sales assistant. Help the customer find products and place orders. " +
      "Always respond in English language. Be polite, friendly, and professional."
    );
  } else {
    console.log(`[prompt-loader:createFallbackPrompt] Using Persian fallback prompt`);
    return new SystemMessage(
      "You are a helpful sales assistant. Help the customer find products and place orders. " +
      "Always respond in Persian (Farsi) language. Be polite, friendly, and professional."
    );
  }
}
