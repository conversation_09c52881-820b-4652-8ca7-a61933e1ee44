import { AgentExecutor, createOpenAIToolsAgent } from "langchain/agents";
import { SystemMessage } from "@langchain/core/messages";
import { llmFactory } from "../llm-factory";
import { CHAT_MODEL } from "../../../base/types/typing";
import { ChatPromptTemplate, MessagesPlaceholder } from "@langchain/core/prompts";
import { Tool as BaseTool } from "langchain/tools";

interface ExecutorLoaderOptions {
  tools: BaseTool[];
  prompt: SystemMessage;
  maxIterations?: number;
  verbose?: boolean;
}

export async function loadExecutor(options: ExecutorLoaderOptions): Promise<AgentExecutor> {
  const { tools, prompt, maxIterations, verbose } = options;

  if (!tools || tools.length === 0) {
    throw new Error("No tools provided to the agent.");
  }
  if (!prompt) {
    throw new Error("System prompt is required.");
  }

  const llm = llmFactory(process.env.CHATBOT as CHAT_MODEL);

  console.log(`[executor-loader] Creating agent with ${tools.length} tools`);

  // Create a more robust chat prompt with clear placeholders
  const chatPrompt = ChatPromptTemplate.fromMessages([
    prompt,
    new MessagesPlaceholder("chat_history"),
    ["human", "{input}"],
    new MessagesPlaceholder("agent_scratchpad"),
  ]);

  const agent = await createOpenAIToolsAgent({
    llm,
    tools,
    prompt: chatPrompt,
  });

  const executor = new AgentExecutor({
    agent,
    tools,
    verbose: verbose ?? process.env.NODE_ENV !== 'production',
    maxIterations: maxIterations ?? 5,
    returnIntermediateSteps: true, // Return intermediate steps for debugging and state management
  });

  return executor;
}