import { LANGUAGE } from "../../../base/types/typing";
import { getCurrentLanguage } from "../../utils/language";

/**
 * Generates appropriate error message when requested quantity exceeds total stock
 * @param requestedQuantity The quantity user requested
 * @param totalStock The total stock quantity (not considering reserved)
 * @param productName Optional product name for more specific messaging
 * @returns Error message in appropriate language
 */
export function getInsufficientStockMessage(
    requestedQuantity: number,
    totalStock: number,
    productName?: string
): string {
    const currentLanguage = getCurrentLanguage();

    if (totalStock === 0) {
        // No stock available
        return currentLanguage === LANGUAGE.ENGLISH
            ? `Sorry, this product is currently out of stock.`
            : `متاسفانه این محصول در حال حاضر موجود نیست.`;
    }

    if (totalStock > 0) {
        // Some stock available but less than requested
        if (currentLanguage === LANGUAGE.ENGLISH) {
            return `Sorry, we only have ${totalStock} item${totalStock > 1 ? 's' : ''} available${productName ? ` for ${productName}` : ''}. You requested ${requestedQuantity}.`;
        } else {
            return `متاسفانه از این محصول تنها ${totalStock} عدد موجود است${productName ? ` (${productName})` : ''}. شما ${requestedQuantity} عدد درخواست کرده‌اید.`;
        }
    }

    // Fallback message
    return currentLanguage === LANGUAGE.ENGLISH
        ? "The requested quantity is not available."
        : "تعداد درخواستی موجود نیست.";
}

/**
 * Validates if requested quantity is available in total stock
 * @param requestedQuantity The quantity user wants to add
 * @param totalStock The total stock quantity (not considering reserved)
 * @returns Object with validation result and available quantity
 */
export function validateStockQuantity(
    requestedQuantity: number,
    totalStock: number
): {
    isValid: boolean;
    availableQuantity: number;
    canPartiallyFulfill: boolean;
} {
    return {
        isValid: requestedQuantity <= totalStock,
        availableQuantity: totalStock,
        canPartiallyFulfill: totalStock > 0 && requestedQuantity > totalStock,
    };
}
