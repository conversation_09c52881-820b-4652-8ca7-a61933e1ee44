// // import { AIMessage, BaseMessage, ToolMessage } from "@langchain/core/messages";
// // import { removeMarkdownFormatting } from "../../utils/text-normalizer";

// // /**
// //  * Utility functions for error handling and memory management in the chatbot
// //  */
// // export class ErrorUtils {
// //     /**
// //      * Checks if an error is the specific OpenAI message format error we're trying to fix
// //      */
// //     static isOpenAIMessageFormatError(error: unknown): boolean {
// //         if (!(error instanceof Error)) return false;

// //         const errorMessage = error.message || '';
// //         return errorMessage.includes("Missing required parameter: 'messages'") &&
// //                errorMessage.includes("content[0].type");
// //     }

// //     /**
// //      * Creates a fallback AI message for when an error occurs
// //      */
// //     static createFallbackResponse(): AIMessage {
// //         const fallbackMessage = "متاسفانه در پردازش پیام شما مشکلی پیش آمد. لطفا مجددا تلاش کنید یا سوال دیگری بپرسید.";
// //         return new AIMessage(removeMarkdownFormatting(fallbackMessage));
// //     }

// //     /**
// //      * Fixes any ToolMessage objects in an array of messages
// //      */
// //     static fixToolMessages(messages: BaseMessage[]): BaseMessage[] {
// //         return messages.map((msg, idx) => {
// //             if (msg instanceof ToolMessage || msg.constructor.name === 'ToolMessage') {
// //                 const toolMsg = msg as any;

// //                 if (Array.isArray(toolMsg.content)) {
// //                     return new ToolMessage({
// //                         tool_call_id: toolMsg.tool_call_id || "unknown_tool_id",
// //                         content: typeof toolMsg.content[0] === 'object'
// //                             ? JSON.stringify(toolMsg.content[0])
// //                             : String(toolMsg.content[0])
// //                     });
// //                 } else if (typeof toolMsg.content === 'object' && toolMsg.content !== null) {
// //                     return new ToolMessage({
// //                         tool_call_id: toolMsg.tool_call_id || "unknown_tool_id",
// //                         content: JSON.stringify(toolMsg.content)
// //                     });
// //                 }
// //             }
// //             return msg;
// //         });
// //     }

// //     /**
// //      * Creates a simplified version of messages based on maxTokens instead of just message count
// //      * Tries to stay within LLM context window.
// //      */
// //     static createSimplifiedMessagesByTokens(
// //         messages: BaseMessage[],
// //         maxTokens: number = 3000,
// //         reserveTokensForAnswer: number = 500
// //     ): BaseMessage[] {
// //         const importantMessages = messages.filter(msg =>
// //             msg.constructor.name === 'SystemMessage' ||
// //             msg.constructor.name === 'HumanMessage'
// //         );

// //         console.log(`[createSimplifiedMessagesByTokens] Found ${importantMessages.length} important messages.`);

// //         let accumulatedTokens = 0;
// //         const selectedMessages: BaseMessage[] = [];

// //         // Traverse from the end to get the latest messages first
// //         for (let i = importantMessages.length - 1; i >= 0; i--) {
// //             const msg = importantMessages[i];
// //             const estimatedTokens = ErrorUtils.estimateTokens(msg);

// //             if (accumulatedTokens + estimatedTokens > (maxTokens - reserveTokensForAnswer)) {
// //                 console.warn(`[createSimplifiedMessagesByTokens] Reached token limit at message ${i}.`);
// //                 break;
// //             }

// //             selectedMessages.unshift(msg); // Insert at beginning
// //             accumulatedTokens += estimatedTokens;
// //         }

// //         console.log(`[createSimplifiedMessagesByTokens] Selected ${selectedMessages.length} messages using ~${accumulatedTokens} tokens.`);

// //         return selectedMessages;
// //     }

// //     /**
// //      * Very rough estimation of tokens in a message
// //      */
// //     static estimateTokens(message: BaseMessage): number {
// //         if (!message.content) return 0;

// //         const content = typeof message.content === 'string'
// //             ? message.content
// //             : JSON.stringify(message.content);

// //         // Approx: 1 token ≈ 4 characters (very rough for Persian/English mixed text)
// //         const estimatedTokens = Math.ceil(content.length / 4);

// //         // Bonus tokens for system messages (prompt instructions)
// //         if (message.constructor.name === 'SystemMessage') {
// //             return estimatedTokens + 20;
// //         }

// //         return estimatedTokens;
// //     }
// // }






// import { AIMessage, BaseMessage, ToolMessage } from "@langchain/core/messages";
// import { removeMarkdownFormatting } from "../../utils/text-normalizer";

// /**
//  * Utility functions for error handling in the chatbot
//  */
// export class ErrorUtils {
//     /**
//      * Checks if an error is the specific OpenAI message format error we're trying to fix
//      */
//     static isOpenAIMessageFormatError(error: unknown): boolean {
//         if (!(error instanceof Error)) return false;

//         const errorMessage = error.message || '';
//         return errorMessage.includes("Missing required parameter: 'messages'") &&
//                errorMessage.includes("content[0].type");
//     }

//     /**
//      * Creates a fallback AI message for when an error occurs
//      */
//     static createFallbackResponse(): AIMessage {
//         const fallbackMessage = "متاسفانه در پردازش پیام شما مشکلی پیش آمد. لطفا مجددا تلاش کنید یا سوال دیگری بپرسید.";
//         return new AIMessage(removeMarkdownFormatting(fallbackMessage));
//     }

//     /**
//      * Fixes any ToolMessage objects in an array of messages (if they have wrong structure)
//      */
//     static fixToolMessages(messages: BaseMessage[]): BaseMessage[] {
//         return messages.map((msg, idx) => {
//             if (msg instanceof ToolMessage || msg.constructor.name === 'ToolMessage') {
//                 const toolMsg = msg as any;

//                 if (Array.isArray(toolMsg.content)) {
//                     return new ToolMessage({
//                         tool_call_id: toolMsg.tool_call_id || "unknown_tool_id",
//                         content: typeof toolMsg.content[0] === 'object'
//                             ? JSON.stringify(toolMsg.content[0])
//                             : String(toolMsg.content[0])
//                     });
//                 } else if (typeof toolMsg.content === 'object' && toolMsg.content !== null) {
//                     return new ToolMessage({
//                         tool_call_id: toolMsg.tool_call_id || "unknown_tool_id",
//                         content: JSON.stringify(toolMsg.content)
//                     });
//                 }
//             }
//             return msg;
//         });
//     }


//     static createSimplifiedMessages(messages: BaseMessage[], maxMessages: number = 24): BaseMessage[] {
//         const importantMessages = messages.filter(msg =>
//             msg.constructor.name === 'SystemMessage' ||
//             msg.constructor.name === 'HumanMessage'
//         );

//         const totalImportant = importantMessages.length;

//         console.log(`[createSimplifiedMessages] Found ${totalImportant} important messages.`);

//         if (totalImportant <= maxMessages) {
//             console.log(`[createSimplifiedMessages] Returning all important messages (count: ${totalImportant}).`);
//             return importantMessages;
//         }

//         let fallbackCount = 8;

//         if (totalImportant <= 10) {
//             fallbackCount = totalImportant; 
//         } else if (totalImportant <= 30) {
//             fallbackCount = 10; 
//         } else {
//             fallbackCount = 5;
//         }

//         console.warn(`[createSimplifiedMessages] Reducing messages to last ${fallbackCount} important messages.`);

//         return importantMessages.slice(-fallbackCount);
//     }
// }
