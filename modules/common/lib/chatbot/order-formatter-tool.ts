import { z } from "zod";
import { OrderUtils } from "./utils/order-utils";

/**
 * Create a wrapper for the order status tool that formats the results
 * @param orderStatusTool The original order status tool
 * @returns A new tool that wraps the order status tool and formats its results
 */
export function createFormattedOrderStatusTool(orderStatusTool: any) {
    // Define the schema for the tool - must match the original tool's schema
    const schema = z.object({
        userId: z.number().describe("User ID to identify the user"),
    });

    // Create a custom tool that wraps the order status tool
    return {
        name: "orderStatus",
        description: "Get information about the user's ORDERS (سفارش) - NOT cart (سبد خرید). Use this tool when users ask about their orders using words like 'سفارش', 'سفارشات', or 'سفارش‌های من'. NEVER use this tool for cart inquiries.",
        schema: schema,
        invoke: async (input: { userId: number }) => {
            try {
                // Call the original order status tool
                console.log(`[FormattedOrderStatus] Calling order status tool with userId: ${input.userId}`);
                const rawResults = await orderStatusTool.invoke(input);

                // Process and format the results
                console.log(`[FormattedOrderStatus] Processing results`);
                console.log(`[FormattedOrderStatus] Raw results: ${rawResults.substring(0, 200)}...`);
                const formattedResults = OrderUtils.processOrderText(rawResults);
                console.log(`[FormattedOrderStatus] Formatted results: ${formattedResults.substring(0, 200)}...`);

                return formattedResults;
            } catch (error) {
                console.error("[FormattedOrderStatus] Error:", error);
                throw error;
            }
        }
    };
}
