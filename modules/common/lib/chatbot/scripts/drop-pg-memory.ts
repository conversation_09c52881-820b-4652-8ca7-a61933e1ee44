// import "reflect-metadata";

// import { config } from "dotenv";
// config({ path: "/etc/palette/api/.env" });

// import { container } from "tsyringe";
// import { PostgresPool } from "../memory";

// const run = async () => {
//     const PgPool = container.resolve(PostgresPool);
//     const conn = await PgPool.pool.connect();

//     await conn.query("DROP TABLE checkpoint_blobs");
//     await conn.query("DROP TABLE checkpoint_migrations");
//     await conn.query("DROP TABLE checkpoint_writes");
//     await conn.query("DROP TABLE checkpoints");

//     conn.release();
// };

// run().catch(console.error);
