import { OpenAIEmbeddings } from "@langchain/openai";
import { EMBEDDING_MODEL, MODEL_CONFIG } from "../../../base/types/typing";


export function embeddingFactory(model: EMBEDDING_MODEL) {
    if (model === EMBEDDING_MODEL.OPENAI_EMBEDDING) {
        return new OpenAIEmbeddings({
            model: MODEL_CONFIG.OPENAI_EMBEDDING_MODEL,
        });
    }

    throw new Error("Unsupported model name");
}
