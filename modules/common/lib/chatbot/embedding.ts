import { EMBEDDING_MODEL } from "../../base/types/typing";
import { embeddingFactory } from "./llm-factory";

export class EmbeddingService {
    private _embeddings;
    modelName: EMBEDDING_MODEL;

    constructor() {
        this.modelName = process.env.EMBEDDING as EMBEDDING_MODEL;

        this._embeddings = embeddingFactory(this.modelName);
    }

    embed = async (text: string) => {
        const vector = await this._embeddings.embedQuery(text);
        return vector;
    };
}
