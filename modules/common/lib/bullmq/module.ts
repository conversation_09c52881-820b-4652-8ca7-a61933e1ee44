import { JobsOptions, Processor, Queue, RepeatOptions, Worker } from "bullmq";
import { injectable } from "tsyringe";
import { RedisConnection } from "../redis";

@injectable()
export class BullMQModule<T = unknown> {
    private _queue: Queue;
    private _worker: Worker;
    private _redisConnection: RedisConnection;

    constructor(
        queueName: string,
        processor: Processor<T>,
        redisConnection: RedisConnection,
    ) {
        this._redisConnection = redisConnection;
        this._queue = new Queue(queueName, {
            prefix: `${process.env.REDIS_PREFIX}:${queueName}`,
            connection: this._redisConnection.getConnection(),
        });
        this._worker = new Worker<T>(this._queue.name, processor, {
            prefix: `${process.env.REDIS_PREFIX}:${this._queue.name}`,
            connection: this._redisConnection.getConnection(),
        });
    }

    public async addJob(jobName: string, data: T, opts?: JobsOptions) {
        return this._queue.add(jobName, data, opts);
    }

    public async addRepeatableJob(
        jobName: string,
        data: T,
        repeatOpts: RepeatOptions,
        opts?: JobsOptions,
    ) {
        return this._queue.add(jobName, data, {
            repeat: repeatOpts,
            ...opts,
        });
    }
    public async close() {
        await this._queue.obliterate({ force: true });
        await this._queue.drain(true);
        await this._queue.close();
        await this._worker.close();
    }

    public getQueue(): Queue {
        return this._queue;
    }

    public getWorker(): Worker {
        return this._worker;
    }
}
