import { Job, Processor } from "bullmq";

import { RedisConnection } from "../redis";

import { BullMQModule } from "./module";

export function createBullMQModule<T = unknown>(
    queueName: string,
    processor: Processor<T>,
    redisConnection: RedisConnection,
): BullMQModule<T> {
    return new BullMQModule<T>(queueName, processor, redisConnection);
}

export function createJobProcessor<T = unknown>(
    processorFunction: (job: Job<T>) => Promise<unknown>,
): Processor<T> {
    return async (job: Job<T>) => {
        await processorFunction(job);
    };
}
