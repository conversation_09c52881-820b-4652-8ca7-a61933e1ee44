// logRetention
// import { removeOldLogs, rotateLogsIfNecessary } from "./logRetention";

import { LogTransformer } from "./transformers/LogTransformer";

export type LogLevel = "info" | "warn" | "error" | "debug";

export interface LogContext {
    [key: string]: unknown;
}

export class Logger {
    private static defaultSampleRate: Record<LogLevel, number> = {
        info: 1.0, // Log 100% of info logs
        warn: 1.0, // Log 100% of warn logs
        error: 1.0, // Log 100% of error logs (most important)
        debug: 0.1, // Log only 10% of debug logs
    };

    private static shouldLog(level: LogLevel): boolean {
        const sampleRate = Logger.defaultSampleRate[level] || 1.0;
        return Math.random() < sampleRate;
    }

    private static transformers: LogTransformer[] = [];

    static addTransformer(transformer: LogTransformer): void {
        Logger.transformers.push(transformer);
    }

    private static log(
        level: LogLevel,
        message: string,
        context?: LogContext,
    ): void {
        if (this.shouldLog(level)) {
            Logger.transformers.forEach((transformer) => {
                transformer.transformLog(level, message, context);
            });
        }
    }

    static info(message: string, context?: LogContext): void {
        this.log("info", message, context);
    }

    static warn(message: string, context?: LogContext): void {
        this.log("warn", message, context);
    }

    static error(message: string, context?: LogContext): void {
        this.log("error", message, context);
    }

    static debug(message: string, context?: LogContext): void {
        this.log("debug", message, context);
    }

    // adjust sampling rates dynamically
    static setSampleRate(level: LogLevel, rate: number): void {
        Logger.defaultSampleRate[level] = rate;
    }
}

export default Logger;
