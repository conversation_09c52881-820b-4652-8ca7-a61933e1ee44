import client from "prom-client";

const createCounter = (name: string, help: string) =>
    new client.Counter({ name, help });

export const updateChannelByIdLog = createCounter(
    "update_channel_by_id",
    "Total number of update channel by id",
);

export const channelNotFoundErrorLog = createCounter(
    "channel_not_found_error",
    "Total number of channel not found errors",
);

export const createChannel = createCounter(
    "create_channel",
    "Total number of create channel",
);

export const getProfileTotal = createCounter(
    "get_profile_total",
    "Total number of get profile of instagram user",
);

export const getAccessToken = createCounter(
    "get_access_token",
    "Total number of getAccessToken successfully",
);

export const getAccessTokenFailure = createCounter(
    "get_access_token_failure",
    "Total number of get access token fail to fetch",
);

export const getLongLivedToken = createCounter(
    "get_longLived_token",
    "Total number of get long lived token",
);

export const getLongLivedTokenFailure = createCounter(
    "get_longLived_token_failure",
    "Total number of get long lived token fail to fetch",
);

export const subscribeToEventsTotal = createCounter(
    "subscribe_to_events_total",
    "Total number of successful subscribe_to_events",
);

export const getProfileFailedToFetch = createCounter(
    "get_profile_failure_total",
    "Total number of get profile failure to fetch and badRequestError",
);

export const subscriptionToEventsFailure = createCounter(
    "subscribe_to_events_failure_total",
    "Total number of subscribe to events fail to fetch and badRequestError",
);

export const clientAddLog = new client.Counter({
    name: "client_add_total",
    help: "Total number of clients added",
});

export const clientEditLog = new client.Counter({
    name: "client_edit_total",
    help: "Total number of clients edited",
});

export const clientGetLog = new client.Counter({
    name: "client_get_total",
    help: "Total number of client retrievals",
});

export const clientGetClientsLog = new client.Counter({
    name: "client_get_clients_total",
    help: "Total number of client list retrievals",
});

export const clientNotFoundErrorLog = new client.Counter({
    name: "client_not_found_errors_total",
    help: "Total number of client not found errors",
});

// Counter for adding chat history
export const chatAddHistoryLog = new client.Counter({
    name: "chat_add_history_total",
    help: "Total number of times chat history is added",
});

// Counter for retrieving chat history
export const chatGetHistoryLog = new client.Counter({
    name: "chat_get_history_total",
    help: "Total number of times chat history is retrieved",
});

// Counter for retrieving client chat history
export const chatGetClientHistoryLog = new client.Counter({
    name: "chat_get_client_history_total",
    help: "Total number of times client chat history is retrieved",
});

// Counter for chat history not found errors
export const chatHistoryNotFoundErrorLog = new client.Counter({
    name: "chat_history_not_found_total",
    help: "Total number of chat history not found errors",
});

export const userLoginLog = new client.Counter({
    name: "user_login_total",
    help: "Total number of successful user login attempts",
});

export const userAddLog = new client.Counter({
    name: "user_add_total",
    help: "Total number of user created",
});

export const userOtpSentLog = new client.Counter({
    name: "user_otp_sent_total",
    help: "Total number of OTPs sent to users",
});

export const sellerOrderNotificationSentLog = new client.Counter({
    name: "seller_order_notification_sent_total",
    help: "Total number of orders notifications sent to sellers",
});

export const userProfileGetLog = new client.Counter({
    name: "user_profile_get_total",
    help: "Total number of successfully retrieved user profiles",
});

export const userNotFoundErrorLog = new client.Counter({
    name: "user_not_found_error_total",
    help: "Total number of NotFoundError occurrences for users",
});

export const wrongOtpErrorLog = new client.Counter({
    name: "wrong_otp_error_total",
    help: "Total number of WrongOtpError occurrences",
});

export const wrongOtpTestUserErrorLog = new client.Counter({
    name: "wrong_otp_test_user_error_total",
    help: "Total number of WrongOtpError for test users occurrences",
});

export const expiredOtpErrorLog = new client.Counter({
    name: "expired_otp_error_total",
    help: "Total number of ExpiredOtpError occurrences",
});

export const otpVerifiedLog = new client.Counter({
    name: "otp_verified_total",
    help: "Total number of successful OTP verifications",
});

export const otpVerifiedTestUserLog = new client.Counter({
    name: "otp_verified_test_user_total",
    help: "Total number of successful test user OTP verifications",
});

export const googleAuthSuccessLog = new client.Counter({
    name: "google_auth_success_total",
    help: "Total number of successful Google authentications",
});

export const googleAuthFailureLog = new client.Counter({
    name: "google_auth_failure_total",
    help: "Total number of failed Google authentications",
});

export const googleAuthNewUserLog = new client.Counter({
    name: "google_auth_new_user_total",
    help: "Total number of new users created via Google authentication",
});

export const faqAddLog = createCounter(
    "faq_created_total",
    "Total number of successfully created FAQs",
);

export const faqGetLog = createCounter(
    "faq_get_total",
    "Total number of successfully retrieved FAQs",
);

export const faqEditLog = createCounter(
    "faq_edit_total",
    "Total number of successfully edited FAQs",
);

export const faqDeleteLog = createCounter(
    "faq_delete_total",
    "Total number of successfully deleted FAQs",
);

export const faqNotFoundErrorLog = createCounter(
    "faq_not_found_errors_total",
    "Total number of NotFoundError occurrences for FAQs",
);

export const forbiddenErrorLog = createCounter(
    "forbidden_errors_total",
    "Total number of ForbiddenError occurrences",
);

export const conditionResetLog = createCounter(
    "condition_reset_total",
    "Total number of conditions reset",
);

export const conditionRemoveLog = createCounter(
    "condition_remove_total",
    "Total number of conditions removed",
);

export const conditionInvalidErrorLog = createCounter(
    "condition_invalid_error_total",
    "Total number of InvalidConditionError occurrences",
);

export const categoryNotFoundErrorLog = createCounter(
    "category_not_found_error_total",
    "Total number of category not found error",
);

export const inventoryNotFoundErrorLog = createCounter(
    "inventory_not_found_total",
    "Total number of inventory not found error",
);

export const editOrderLog = createCounter(
    "edit_order_total",
    "Total number of edit order",
);

export const orderNotFoundErrorLog = createCounter(
    "order_not_found_error_total",
    "Total number of order not found error",
);

export const getOrdersLog = createCounter(
    "get_orders_total",
    "Total number of get orders",
);

export const newOrderCreationLog = createCounter(
    "new_order_creation_total",
    "Total number of successfully created order",
);

export const editInventoryLog = createCounter(
    "edit_inventory_total",
    "Total number of editInventory call",
);

export const getInventoriesLog = createCounter(
    "get_inventories_total",
    "Total number of get inventories call",
);

export const inventoryCreationLog = createCounter(
    "inventory_creation_total",
    "Total number of successfully inventory creation",
);

export const actionValidateLog = createCounter(
    "action_validate_total",
    "Total number of successfully validated actions",
);

export const actionResetLog = createCounter(
    "action_reset_total",
    "Total number of successfully reset actions",
);

export const actionDeleteLog = createCounter(
    "action_delete_total",
    "Total number of successfully deleted actions",
);

export const actionValidationErrorLog = createCounter(
    "action_validation_errors_total",
    "Total number of Action validation errors",
);

export const automationAddLog = createCounter(
    "automation_created_total",
    "Total number of successfully created automations",
);

export const automationGetLog = createCounter(
    "automation_get_total",
    "Total number of successfully automations",
);

export const automationEditLog = createCounter(
    "automation_edit_total",
    "Total number of successfully edited automations",
);

export const automationDeleteLog = createCounter(
    "automation_delete_total",
    "Total number of successfully deleted automations",
);

export const adminAddLog = createCounter(
    "admin_created_total",
    "Total number of successfully created admins",
);

export const adminLoginLog = createCounter(
    "admin_login_total",
    "Total number of successful admin logins",
);

export const adminGetProfileLog = createCounter(
    "admin_profile_retrieved_total",
    "Total number of successful admin profile retrievals",
);

export const emailAlreadyExistsErrorLog = createCounter(
    "email_already_exists_errors_total",
    "Total number of EmailAlreadyExistError occurrences",
);

export const usernameAlreadyExistsErrorLog = createCounter(
    "username_already_exists_errors_total",
    "Total number of UsernameAlreadyExistError occurrences",
);

export const adminNotFoundErrorLog = createCounter(
    "admin_not_found_errors_total",
    "Total number of NotFoundError occurrences for Admin",
);

export const automationNotFoundErrorLog = createCounter(
    "automation_not_found_errors_total",
    "Total number of NotFoundError occurrences for Automation",
);

export const unauthorizedErrorLog = createCounter(
    "unauthorized_errors_total",
    "Total number of UnauthorizedError occurrences",
);

// Gauge to store the time of the most recent error
export const latestErrorTimeGauge = new client.Gauge({
    name: "latest_error_time",
    help: "Records the timestamp of the most recent error",
    labelNames: ["errorType"],
});

// log the actual error message
export const latestErrorMessageSummary = new client.Summary({
    name: "latest_error_message",
    help: "Stores the most recent error message as a label",
    labelNames: ["errorType", "errorMessage"],
});

// store the actual error message and the time of the error
export const recordErrorValue = (errorType: string, errorMessage: string) => {
    latestErrorTimeGauge.labels(errorType).setToCurrentTime();
    latestErrorMessageSummary.labels(errorType, errorMessage).observe(1); // '1' is a dummy value just to trigger the observation
};
