import fs from "fs";
import path from "path";

const MAX_LOG_AGE_DAYS = 30;
const MAX_LOG_FILE_SIZE_MB = 5;

const logDirectory = path.join(__dirname, "logs");
const logFilePath = path.join(logDirectory, "app.log");

// Helper to delete old log files
export function removeOldLogs() {
    const logFiles = fs.readdirSync(logDirectory);

    logFiles.forEach((file) => {
        const filePath = path.join(logDirectory, file);
        const fileStats = fs.statSync(filePath);

        const now = new Date().getTime();
        const fileCreationTime = new Date(fileStats.ctime).getTime();
        const ageInDays = (now - fileCreationTime) / (1000 * 60 * 60 * 24);

        if (ageInDays > MAX_LOG_AGE_DAYS) {
            fs.unlinkSync(filePath);
            console.log(
                `Deleted log file: ${filePath} (older than ${MAX_LOG_AGE_DAYS} days)`,
            );
        }
    });
}

// Rotate logs if they exceed the max size
export function rotateLogsIfNecessary() {
    const logFileStats = fs.statSync(logFilePath);
    const logFileSizeInMB = logFileStats.size / (1024 * 1024); // Convert bytes to MB

    if (logFileSizeInMB > MAX_LOG_FILE_SIZE_MB) {
        const archiveFileName = `app-${new Date().toISOString()}.log`;
        const archiveFilePath = path.join(logDirectory, archiveFileName);
        fs.renameSync(logFilePath, archiveFilePath); // Rotate the log file

        console.log(
            `Rotated log file: ${archiveFileName} (size exceeded ${MAX_LOG_FILE_SIZE_MB} MB)`,
        );

        // Create new empty log file
        fs.writeFileSync(logFilePath, "");
    }
}
