import { LogLevel } from "../logger";
import { LogTransformer } from "./ LogTransformer";

export class ConsoleLogTransformer implements LogTransformer {
    transformLog(
        level: LogLevel,
        message: string,
        context?: Record<string, never>,
    ): void {
        const logEntry = {
            level,
            timestamp: new Date().toISOString(),
            message,
            context,
        };
        console.log(JSON.stringify(logEntry));
    }
}
