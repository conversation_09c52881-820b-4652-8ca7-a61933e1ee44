import { LogLevel } from "../logger";
import fs from "fs";
import path from "path";
import { LogTransformer } from "./ LogTransformer";

export class FileLogTransformer implements LogTransformer {
    private logFilePath: string;

    constructor(logDirectory: string) {
        if (!fs.existsSync(logDirectory)) {
            fs.mkdirSync(logDirectory, { recursive: true });
        }
        this.logFilePath = path.join(logDirectory, "app.log");
    }

    transformLog(
        level: LogLevel,
        message: string,
        context?: Record<string, never>,
    ): void {
        const logEntry = {
            level,
            timestamp: new Date().toISOString(),
            message,
            context,
        };
        const logString = JSON.stringify(logEntry);
        fs.appendFileSync(this.logFilePath, logString + "\n");
    }
}
