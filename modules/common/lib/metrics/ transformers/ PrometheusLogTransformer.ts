import { LogLevel } from "../logger";
import { latestErrorMessageSummary } from "../metrics";
import { LogTransformer } from "./ LogTransformer";

export class PrometheusLogTransformer implements LogTransformer {
    transformLog(
        level: LogLevel,
        message: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        context?: Record<string, any>,
    ): void {
        if (
            level === "error" &&
            <PERSON><PERSON>(context?.errorMessage) &&
            <PERSON><PERSON>an(context?.errorType)
        ) {
            latestErrorMessageSummary
                .labels(context!.errorType, context!.errorMessage)
                .observe(1);
            // record the time of the error (gauge)
            // latestErrorTimeGauge.labels(context.errorType).setToCurrentTime();
        }
    }
}
