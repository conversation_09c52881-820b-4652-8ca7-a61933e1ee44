import { singleton } from "tsyringe";
import {
    createRequestAPIResponse,
    createRequestResponse,
    inquiryRequestAPIResponse,
    verifyRequestAPIResponse,
    verifyRequestResponse,
} from ".";
import { utils } from "../..";
import { pick } from "../../utils";

@singleton()
export class ZibalService {
    private _zibalMerchantId: string;
    private _zibalPrefixUrl: string;
    private _callbackUrl: string;
    private _paymentLinkPrefixUrl: string;

    constructor() {
        this._zibalMerchantId = process.env.ZIBAL_MERCHANT_ID;
        this._zibalPrefixUrl = process.env.ZIBAL_PREFIX_URL;
        this._callbackUrl = process.env.ZIBAL_CALL_BACK_URL;
        this._paymentLinkPrefixUrl = process.env.PAYMENT_LINK_PREFIX_URL;
    }

    private statuses = {
        "-1": "PENDING",
        "-2": "INTERNAL_ERROR",
        "1": "PAYED_ACCEPTED",
        "2": "PAYED_NOT_ACCEPTED",
        "3": "USER_CANCEL",
        "4": "INVALID_CARD_NUMBER",
        "5": "INSUFFICIENT_BALANCE",
        "6": "INVALID_PASSWORD",
        "7": "REQUESTS_EXCEEDED",
        "8": "DAILY_PAYMENT_EXCEEDED",
        "9": "DAILY_PAYMENT_AMOUNT_EXCEEDED",
        "10": "INVALID_PUBLISHER",
        "11": "SWITCH_ERROR",
        "12": "NOT_ACCESSIBLE_CARD",
    };

    createRequest = async (amount: number): Promise<createRequestResponse> => {
        const response = await fetch(`${this._zibalPrefixUrl}/request`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                merchant: this._zibalMerchantId,
                amount,
                callbackUrl: this._callbackUrl,
            }),
        });

        const jsonResponse: createRequestAPIResponse = await response.json();
        const error = this.createRequestResponseHandler(jsonResponse.result);

        const { trackId, payLink } = jsonResponse;

        const paymentLink = utils.isNotNil(payLink)
            ? jsonResponse.payLink
            : `${this._paymentLinkPrefixUrl}/${trackId}`;

        return {
            trackId,
            payLink: paymentLink,
            error,
        };
    };

    verifyDeposit = async (trackId: string): Promise<verifyRequestResponse> => {
        const response = await fetch(`${this._zibalPrefixUrl}/verify`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                merchant: this._zibalMerchantId,
                trackId,
            }),
        });

        const result: verifyRequestAPIResponse = await response.json();

        const responseError = this.createRequestResponseHandler(result.result);

        const { state, error } = this.callbackStatusHandler(result.status);

        return {
            ...pick(result, "description", "paidAt", "refNumber", "trackId"),
            error,
            responseError,
            state,
        };
    };

    inquiryDeposit = async (trackId: string) => {
        const response = await fetch(`${this._zibalPrefixUrl}/inquiry`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                merchant: this._zibalMerchantId,
                trackId,
            }),
        });

        const result: inquiryRequestAPIResponse = await response.json();

        const responseError = this.createRequestResponseHandler(result.result);

        const { state, error } = this.callbackStatusHandler(result.status);

        return {
            ...result,
            error,
            responseError,
            state,
        };
    };

    createRequestResponseHandler = (
        result: createRequestAPIResponse["result"],
    ): createRequestResponse["error"] | undefined => {
        if (result === 100) {
            return;
        } else if (result < 105) {
            return "MERCHANT_ISSUE";
        } else if (result === 106) {
            return "CALL_BACK_ISSUE";
        } else if (result === 105) {
            return "LESS_AMOUNT_ISSUE";
        } else {
            return "MORE_AMOUNT_ISSUE";
        }
    };

    callbackStatusHandler = (
        statusCode: keyof typeof this.statuses,
    ): { error?: string; state?: string } => {
        if (statusCode === "-1") {
            return { state: this.statuses[statusCode] };
        } else if (statusCode === "-2") {
            return { error: this.statuses[statusCode] };
        } else if (Number(statusCode) < 3) {
            return { state: this.statuses[statusCode] };
        } else {
            return { error: this.statuses[statusCode] };
        }
    };
}
