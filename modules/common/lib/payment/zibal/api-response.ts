export interface createRequestAPIResponse {
    trackId: string;
    result: 100 | 102 | 103 | 104 | 105 | 106 | 113;
    payLink: string;
    message: string;
}

export interface verifyRequestAPIResponse {
    paidAt: Date;
    cardNumber: string;
    trackId: string;
    status:
        | "-1"
        | "-2"
        | "1"
        | "2"
        | "3"
        | "4"
        | "5"
        | "6"
        | "7"
        | "8"
        | "9"
        | "10"
        | "11"
        | "12";
    result: 100 | 102 | 103 | 104 | 105 | 106 | 113;
    amount: string;
    refNumber: string;
    description: string;
    orderId: number;
    message: string;
}

export interface inquiryRequestAPIResponse {
    createdAt: Date;
    paidAt: Date;
    verifiedAt: Date;
    status:
        | "-1"
        | "-2"
        | "1"
        | "2"
        | "3"
        | "4"
        | "5"
        | "6"
        | "7"
        | "8"
        | "9"
        | "10"
        | "11"
        | "12";
    cardNumber: string;
    amount: string;
    refNumber: string;
    description: string;
    orderId: string;
    wage: 0 | 1 | 2;
    result: 100 | 102 | 103 | 104 | 105 | 106 | 113;
    message: string;
}
