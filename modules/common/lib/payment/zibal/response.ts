export interface createRequestResponse {
    trackId?: string;
    error?:
        | "MERCHANT_ISSUE"
        | "LESS_AMOUNT_ISSUE"
        | "MORE_AMOUNT_ISSUE"
        | "CALL_BACK_ISSUE";
    payLink?: string;
}

export interface verifyRequestResponse {
    trackId: string;
    responseError?:
        | "MERCHANT_ISSUE"
        | "LESS_AMOUNT_ISSUE"
        | "MORE_AMOUNT_ISSUE"
        | "CALL_BACK_ISSUE";
    description: string;
    paidAt: Date;
    refNumber: string;
    error?: string;
    state?: string;
}
