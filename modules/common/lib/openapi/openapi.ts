/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import "reflect-metadata";
import { utils } from "..";

interface RouteController {
    prefix: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    controller: any;
}

export class OpenAPI {
    private _routes: RouteController[];
    private _openAPIPaths: Record<string, Record<string, object>> = {};

    constructor(routes: RouteController[]) {
        this._routes = routes;
    }

    generateDoc() {
        for (const { prefix, controller } of this._routes) {
            const paths = Reflect.getMetadata("routes", controller);
            for (const path of paths) {
                const key = `${prefix}${path.path}`;
                if (utils.isNil(this._openAPIPaths[key])) {
                    this._openAPIPaths[key] = {};
                }
                this._openAPIPaths[key][path.method] = {
                    operationId: path.operationId,
                    tags: [path.tag],
                    ...(utils.isNotNil(path.security)
                        ? {
                              security: [
                                  {
                                      [path.security]: [],
                                  },
                              ],
                          }
                        : {}),
                    parameters: [
                        ...((path.method === "get" && !path.isHealthCheck)
                            ? [
                                  {
                                      in: "query",
                                      name: "page",
                                      schema: { type: "string", example: "1" },
                                  },
                                  {
                                      in: "query",
                                      name: "pageSize",
                                      schema: { type: "string", example: "30" },
                                  },
                                  {
                                      in: "query",
                                      name: "sort",
                                      schema: { type: "string", example: "id" },
                                  },
                              ]
                            : []),

                        ...(utils.isNotNil(path.parameters)
                            ? path.parameters
                            : []),
                    ],
                    ...(utils.isNotNil(path.schema)
                        ? {
                              requestBody: {
                                  required: true,
                                  content: {
                                      "application/json": {
                                          schema: path.schema,
                                      },
                                      "multipart/form-data": {
                                          schema: path.schema,
                                      },
                                  },
                              },
                          }
                        : {}),
                    responses: {
                        "200": {
                            content: {
                                "application/json": {
                                    schema: utils.isNotNil(path.response)
                                        ? path.response
                                        : {},
                                },
                            },
                        },
                    },
                };
            }
        }

        return this._openAPIPaths;
    }
}
