/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    DataSource,
    DeepPartial,
    DeleteResult,
    EntityManager,
    EntitySchema,
    FindManyOptions,
    FindOptionsWhere,
    InsertResult,
    ObjectLiteral,
    Repository,
    SelectQueryBuilder,
    UpdateResult,
} from "typeorm";
import {
    QueryDeepPartialEntity,
    QueryPartialEntity,
} from "typeorm/query-builder/QueryPartialEntity";

import { AppDataSource } from "../typeorm";
import { utils } from "..";
export type SearchField<T> = keyof T;
export type Filter<T> = {
    [P in keyof T]?: T[P] | Range<T[P]>;
};
export type Sort<T> = Partial<Record<keyof T, SortOrder>>;
export type SortOrder = "ASC" | "DESC";
export type Range<T> = {
    start: T;
    end: T;
};

SelectQueryBuilder.prototype.paginate = function <Entity extends ObjectLiteral>(
    this: SelectQueryBuilder<Entity>,
    pageNumber = 1,
    pageSize = 30,
) {
    this.addSelect(`${pageNumber} as page_number, ${pageSize} as page_size`);

    return this;
};

SelectQueryBuilder.prototype.search = function <Entity extends ObjectLiteral>(
    this: SelectQueryBuilder<Entity>,
    search?: string,
    searchFields?: string[] | string,
    ...aliases: string[]
) {
    if (utils.isNil(search) || utils.isNil(searchFields)) return this;
    if (!Array.isArray(searchFields)) {
        searchFields = [searchFields];
    }
    const searchQuery = searchFields
        .filter((field) => {
            if (utils.isNotNil(aliases) && aliases.length > 0) {
                return aliases.some((alias) => field.startsWith(`${alias}.`));
            }
            return field.includes(".") ? false : true;
        })
        .map((field) => {
            if (field.includes(".")) {
                return `${field} ILIKE :search`;
            } else {
                return `${this.alias}.${field} ILIKE :search`;
            }
        })
        .join(" OR ");
    if (searchQuery !== "") {
        this.andWhere(`(${searchQuery})`, { search: `%${search}%` });
    }
    return this;
};

SelectQueryBuilder.prototype.sort = function <Entity extends ObjectLiteral>(
    this: SelectQueryBuilder<Entity>,
    sort?: Record<string, SortOrder>,
    ...aliases: string[]
) {
    if (utils.isNil(sort)) {
        this.addOrderBy(`"${this.alias}"."id"`, "DESC");
        return this;
    }

    Object.entries(sort).forEach(([key, order]) => {
        if (key.includes(".")) {
            const [entityAlias, field] = key.split(".");
            if (aliases.includes(entityAlias)) {
                this.addOrderBy(`"${entityAlias}"."${field}"`, order);
            }
        } else {
            this.addOrderBy(`"${this.alias}"."${String(key)}"`, order);
        }
    });
    return this;
};

SelectQueryBuilder.prototype.filter = function <Entity extends ObjectLiteral>(
    this: SelectQueryBuilder<Entity>,
    filter?: Partial<Record<string, any>>,
    ...aliases: string[]
) {
    if (utils.isNil(filter)) {
        return this;
    }
    if (aliases.length > 0) {
        aliases.forEach((alias) => {
            this.filterByJoined(alias, filter);
        });
    } else {
        Object.entries(filter).forEach(([field, value]) => {
            if (field.split(".").length === 1)
                this.filterByEntity(this.alias, field, value);
        });
    }

    return this;
};

SelectQueryBuilder.prototype.filterByJoined = function <
    Entity extends ObjectLiteral,
>(
    this: SelectQueryBuilder<Entity>,
    alias: string,
    filter: Partial<Record<string, unknown>>,
): SelectQueryBuilder<Entity> {
    Object.entries(filter).forEach(([key, value]) => {
        const field = key.split(".");
        const hasMatchedEntity = field.length === 2 && field[0] === alias;
        if (hasMatchedEntity) {
            this.filterByEntity(field[0], field[1], value);
        }
    });
    return this;
};

SelectQueryBuilder.prototype.filterByEntity = function <
    Entity extends ObjectLiteral,
>(
    this: SelectQueryBuilder<Entity>,
    entity: string,
    field: string,
    value: unknown,
) {
    if (Array.isArray(value)) {
        this.andWhere(`${entity}.${field} IN (:...array_${field})`, {
            [`array_${field}`]: value,
        });
    } else if (typeof value === "object" && value !== null) {
        if (!("start" in value) || !("end" in value)) return this;
        this.andWhere(
            `${entity}.${field} BETWEEN :start_${field} AND :end_${field}`,
            {
                [`start_${field}`]: value.start,
                [`end_${field}`]: value.end,
            },
        );
    } else if (value === "null") {
        this.andWhere(`${entity}.${field} IS NULL`);
    } else if (field.slice(-1) === "!") {
        field = field.slice(0, -1);
        this.andWhere(`${entity}.${field} IS DISTINCT FROM :${field}`, {
            [field]: value,
        });
    } else {
        this.andWhere(`${entity}.${field} = :${field}`, { [field]: value });
    }
    return this;
};

SelectQueryBuilder.prototype.getRawManyAndCount = async function <
    Entity extends ObjectLiteral,
>(this: SelectQueryBuilder<Entity>) {
    const totalRecords = await this.getCount();
    this.addSelect(`${totalRecords} as total_records`);
    return this.getRawMany();
};

export interface TypeormRepository<T extends ObjectLiteral> {
    _repo: Repository<T>;

    create(args: DeepPartial<T>): Promise<T>;
    // findAll(): Promise<T[]>;
    findOne(filter: DeepPartial<T>): Promise<T | null>;
    upsert(args: DeepPartial<T>, findBy: (keyof T)[]): Promise<number>;
    // updateOneOrCreate(args: DeepPartial<T>, findBy: (keyof T)[]): Promise<T>;
    findById(id: number): Promise<T | null>;
    findOneByQuery(filter: DeepPartial<T>): Promise<T | null>;
    findByQuery(
        filter: FindOptionsWhere<T>,
        relations?: (keyof T)[] & string[],
    ): Promise<T[]>;
    // count(filter: FilterQuery<T>): Promise<number>;
    // estimate(options: QueryOptions<T>): Promise<number>;
    updateById(id: number, update: DeepPartial<T>): Promise<boolean>;
    updateOneByQuery(
        filter: DeepPartial<T>,
        update: DeepPartial<T>,
    ): Promise<boolean>;
    // updateByQuery(
    //     filter: FilterQuery<T>,
    //     update: UpdateQuery<T>,
    // ): Promise<UpdateWriteOpResult>;
    deleteById(id: number): Promise<boolean>;
    deleteOneByQuery(filter: DeepPartial<T>): Promise<boolean>;
    // deleteByQuery(filter: FilterQuery<T>): Promise<DeleteResult>;
    bulkCreate(args: DeepPartial<T>[]): Promise<T[]>;
    // bulkUpdate(
    //     updates: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
    // ): Promise<BulkWriteResult>;
    // bulkUpsert(
    //     upserts: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
    // ): Promise<BulkWriteResult>;
    // aggregate(pipeline: PipelineStage[]): Promise<any[]>;
}

export abstract class BaseTypeormRepository<T extends ObjectLiteral>
    implements TypeormRepository<T>
{
    _dataSource: DataSource;
    _repo: Repository<T>;
    _manager: EntityManager;
    _schema: EntitySchema<T>;

    constructor(schema: EntitySchema<T>) {
        this._dataSource = AppDataSource.getInstance();
        this._repo = this._dataSource.getRepository<T>(schema);
        this._manager = this._dataSource.manager;
        this._schema = schema;
    }

    create(args: DeepPartial<T>, { manager = this._manager } = {}): Promise<T> {
        return manager.getRepository(this._schema).save(args);
    }

    removeByQuery = async (
        query: FindOptionsWhere<T>,
        { manager = this._manager } = {},
    ) => {
        return manager.getRepository(this._schema).softDelete(query);
    };

    createOrIgnore = async (
        alias: string,
        args: QueryPartialEntity<T>[],
        columns: string[],
        returningColumns: string[] = columns,
        { manager = this._manager } = {},
    ): Promise<InsertResult> => {
        return manager
            .getRepository(this._schema)
            .createQueryBuilder()
            .insert()
            .into(alias, columns)
            .values(args)
            .returning(returningColumns)
            .orIgnore()
            .execute();
    };

    // findAll(): Promise<T[]> {
    //     return this._model.find();
    // }
    findOne(
        filter: DeepPartial<T>,
        relations?: (keyof T)[] & string[],
        { manager = this._manager } = {},
    ): Promise<T | null> {
        return manager.getRepository(this._schema).findOne({
            where: filter as FindOptionsWhere<T>,
            relations,
        });
    }
    async upsert(
        args: DeepPartial<T>,
        findBy: (keyof T)[],
        { manager = this._manager } = {},
    ): Promise<number> {
        const result = await manager
            .getRepository(this._schema)
            .upsert(args as QueryDeepPartialEntity<T>, findBy as string[]);
        return result.identifiers.length;
    }
    // updateOneOrCreate(args: DeepPartial<T>, findBy: (keyof T)[]): Promise<T> {
    //     return this._repo.findOneAndUpdate(
    //         Object.fromEntries(
    //             findBy.map((key) => [key, args[key]]),
    //         ) as FilterQuery<T>,
    //         args,
    //         {
    //             upsert: true,
    //             new: true,
    //         },
    //     );
    // }
    findById(
        id: number,
        relations?: (keyof T)[] & string[],
        { manager = this._manager } = {},
    ): Promise<T | null> {
        return manager.getRepository(this._schema).findOne({
            where: { id } as unknown as FindOptionsWhere<T>,
            relations,
        });
    }
    findOneByQuery(
        filter: DeepPartial<T>,
        relations?: (keyof T)[] & string[],
        { manager = this._manager } = {},
    ): Promise<T | null> {
        return manager
            .getRepository(this._schema)
            .findOne({ where: filter as FindOptionsWhere<T>, relations });
    }
    findByQuery(
        filter: FindOptionsWhere<T>,
        relations?: (keyof T)[] & string[],
        { manager = this._manager } = {},
    ): Promise<T[]> {
        return manager
            .getRepository(this._schema)
            .find({ where: filter, relations });
    }
    find(
        filter: FindManyOptions<T>,
        { manager = this._manager } = {},
    ): Promise<T[]> {
        return manager.getRepository(this._schema).find(filter);
    }
    // count(filter: FilterQuery<T>): Promise<number> {
    //     return this._model.count(filter);
    // }
    // estimate(options: QueryOptions<T>): Promise<number> {
    //     return this._model.estimatedDocumentCount(options);
    // }
    async updateById(
        id: number,
        update: DeepPartial<T>,
        { manager = this._manager } = {},
    ): Promise<boolean> {
        const result = await manager
            .getRepository(this._schema)
            .update(id, update as QueryDeepPartialEntity<T>);
        return result.affected === 1;
    }
    async updateOneByQuery(
        filter: DeepPartial<T>,
        update: DeepPartial<T>,
        { manager = this._manager } = {},
    ): Promise<boolean> {
        const result = await manager
            .getRepository(this._schema)
            .update(
                filter as FindOptionsWhere<T>,
                update as QueryDeepPartialEntity<T>,
            );
        return result.affected === 1;
    }
    updateByQuery(
        filter: FindOptionsWhere<T>,
        update: DeepPartial<T>,
        { manager = this._manager } = {},
    ): Promise<UpdateResult> {
        return manager
            .getRepository(this._schema)
            .update(filter, update as QueryDeepPartialEntity<T>);
    }

    incrementFieldByQuery(
        filter: FindOptionsWhere<T>,
        property: keyof T & string,
        quantity: number,
        { manager = this._manager } = {},
    ) {
        return manager
            .getRepository(this._schema)
            .increment(filter, property, quantity);
    }

    decrementFieldByQuery(
        filter: FindOptionsWhere<T>,
        property: keyof T & string,
        quantity: number,
        { manager = this._manager } = {},
    ) {
        return manager
            .getRepository(this._schema)
            .decrement(filter, property, quantity);
    }

    async deleteById(
        id: number,
        { manager = this._manager } = {},
    ): Promise<boolean> {
        const result = await manager.getRepository(this._schema).delete(id);
        return result.affected === 1;
    }
    async deleteOneByQuery(
        filter: DeepPartial<T>,
        { manager = this._manager } = {},
    ): Promise<boolean> {
        const result = await manager
            .getRepository(this._schema)
            .delete(filter as FindOptionsWhere<T>);
        return result.affected === 1;
    }
    deleteByQuery(
        filter: FindOptionsWhere<T>,
        { manager = this._manager } = {},
    ): Promise<DeleteResult> {
        return manager.getRepository(this._schema).delete(filter);
    }
    bulkCreate(
        entities: DeepPartial<T>[],
        { manager = this._manager } = {},
    ): Promise<T[]> {
        return manager.getRepository(this._schema).save(entities);
    }
    bulkUpdate(
        entities: DeepPartial<T>[],
        findBy: (keyof T)[],
        { manager = this._manager } = {},
    ): Promise<InsertResult> {
        return manager
            .getRepository(this._schema)
            .upsert(entities as QueryDeepPartialEntity<T>, {
                conflictPaths: findBy as string[],
                upsertType: "on-conflict-do-update",
            });
    }
    // bulkUpsert(
    //     upserts: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
    // ): Promise<BulkWriteResult> {
    //     return this._model.bulkWrite(
    //         upserts.map(
    //             ({ filter, update }) =>
    //                 ({
    //                     updateOne: {
    //                         filter,
    //                         update,
    //                         upsert: true,
    //                     },
    //                 }) as AnyBulkWriteOperation,
    //         ),
    //     );
    // }

    async save(entity: DeepPartial<T>, { manager = this._manager } = {}) {
        return manager.getRepository(this._schema).save(entity);
    }

    async runTransaction<T>(
        fn: (manager: EntityManager) => Promise<T>,
    ): Promise<T> {
        const queryRunner = this._dataSource.createQueryRunner();
        await queryRunner.startTransaction();
        try {
            const result = await fn(queryRunner.manager);
            await queryRunner.commitTransaction();
            await queryRunner.release();
            return result;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            await queryRunner.release();
            throw error;
        }
    }

    createQueryBuilder(alias: string, { manager = this._manager } = {}) {
        return manager.getRepository(this._schema).createQueryBuilder(alias);
    }
}
