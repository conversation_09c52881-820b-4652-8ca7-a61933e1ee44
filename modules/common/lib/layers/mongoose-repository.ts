// import {
//     AnyKeys,
//     Document,
//     FilterQuery,
//     Model,
//     ObjectId,
//     PipelineStage,
//     QueryOptions,
//     UpdateQuery,
//     UpdateWriteOpResult,
// } from "mongoose";
// import { AnyBulkWriteOperation, BulkWriteResult, DeleteResult } from "mongodb";

// interface RepositoryQuery<T> {
//     filter: FilterQuery<T>;
//     sort: { [key: string]: 1 | -1 };
//     q: string;
//     qField: string;
//     pageNumber: number;
//     pageSize: number;
// }

// export type QueryFN<T> = (
//     query: RepositoryQuery<T>,
//     ...args: any[]
// ) => Promise<PaginatedResult>;

// export class PaginatedResult {
//     pagination: {
//         totalRecords: number;
//         pageNumber: number;
//         pageSize: number;
//     };
//     records: any[];

//     constructor(data: any[], pageNumber: number, pageSize: number) {
//         if (data[0]) {
//             this.pagination = data[0].pagination;
//             this.records = data[0].records;
//         } else {
//             this.pagination = {
//                 totalRecords: 0,
//                 pageNumber,
//                 pageSize,
//             };
//             this.records = [];
//         }
//     }
// }

// export interface Repository<T> {
//     _model: Model<T>;

//     create(args: AnyKeys<T>): Promise<T>;
//     findAll(): Promise<T[]>;
//     findOne(filter: object): Promise<T | null>;
//     findOneOrCreate(
//         args: FilterQuery<T>,
//         findBy: (keyof T)[],
//         returnNew?: boolean,
//     ): Promise<T | null>;
//     updateOneOrCreate(args: FilterQuery<T>, findBy: (keyof T)[]): Promise<T>;
//     findById(id: ObjectId | string): Promise<T | null>;
//     findOneByQuery(filter: FilterQuery<T>): Promise<T | null>;
//     findByQuery(filter: FilterQuery<T>): Promise<T[]>;
//     count(filter: FilterQuery<T>): Promise<number>;
//     estimate(options: QueryOptions<T>): Promise<number>;
//     updateById(
//         id: ObjectId | string,
//         update: UpdateQuery<T>,
//     ): Promise<T | null>;
//     updateOneByQuery(
//         filter: FilterQuery<T>,
//         update: UpdateQuery<T>,
//     ): Promise<T | null>;
//     updateByQuery(
//         filter: FilterQuery<T>,
//         update: UpdateQuery<T>,
//     ): Promise<UpdateWriteOpResult>;
//     deleteById(id: ObjectId | string): Promise<T | null>;
//     deleteOneByQuery(filter: FilterQuery<T>): Promise<T | null>;
//     deleteByQuery(filter: FilterQuery<T>): Promise<DeleteResult>;
//     bulkCreate(docs: Array<AnyKeys<T>>): Promise<Array<T>>;
//     bulkUpdate(
//         updates: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
//     ): Promise<BulkWriteResult>;
//     bulkUpsert(
//         upserts: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
//     ): Promise<BulkWriteResult>;
//     aggregate(pipeline: PipelineStage[]): Promise<any[]>;
// }

// export abstract class BaseRepository<T extends Document>
//     implements Repository<T>
// {
//     _model: Model<T>;

//     constructor(model: Model<T>) {
//         this._model = model;
//     }

//     create(args: AnyKeys<T>): Promise<T> {
//         return this._model.create(args);
//     }

//     findAll(): Promise<T[]> {
//         return this._model.find();
//     }
//     findOne(filter: object): Promise<T | null> {
//         return this._model.findOne(filter);
//     }
//     findOneOrCreate(
//         args: FilterQuery<T>,
//         findBy: (keyof T)[],
//         returnNew = true,
//     ): Promise<T | null> {
//         return this._model.findOneAndUpdate(
//             Object.fromEntries(
//                 findBy.map((key) => [key, args[key]]),
//             ) as FilterQuery<T>,
//             {
//                 $setOnInsert: args,
//             },
//             {
//                 upsert: true,
//                 new: returnNew,
//             },
//         );
//     }
//     updateOneOrCreate(args: FilterQuery<T>, findBy: (keyof T)[]): Promise<T> {
//         return this._model.findOneAndUpdate(
//             Object.fromEntries(
//                 findBy.map((key) => [key, args[key]]),
//             ) as FilterQuery<T>,
//             args,
//             {
//                 upsert: true,
//                 new: true,
//             },
//         );
//     }
//     findById(id: ObjectId | string): Promise<T | null> {
//         return this._model.findById(id);
//     }
//     findOneByQuery(filter: FilterQuery<T>): Promise<T | null> {
//         return this._model.findOne(filter);
//     }
//     findByQuery(filter: FilterQuery<T>): Promise<T[]> {
//         return this._model.find(filter);
//     }
//     count(filter: FilterQuery<T>): Promise<number> {
//         return this._model.count(filter);
//     }
//     estimate(options: QueryOptions<T>): Promise<number> {
//         return this._model.estimatedDocumentCount(options);
//     }
//     updateById(
//         id: ObjectId | string,
//         update: UpdateQuery<T>,
//     ): Promise<T | null> {
//         return this._model.findByIdAndUpdate(id, update, { new: true });
//     }
//     updateOneByQuery(
//         filter: FilterQuery<T>,
//         update: UpdateQuery<T>,
//     ): Promise<T | null> {
//         return this._model.findOneAndUpdate(filter, update, { new: true });
//     }
//     updateByQuery(
//         filter: FilterQuery<T>,
//         update: UpdateQuery<T>,
//     ): Promise<UpdateWriteOpResult> {
//         return this._model.updateMany(filter, update);
//     }
//     deleteById(id: ObjectId | string): Promise<T | null> {
//         return this._model.findByIdAndDelete(id);
//     }
//     deleteOneByQuery(filter: FilterQuery<T>): Promise<T | null> {
//         return this._model.findOneAndDelete(filter);
//     }
//     deleteByQuery(filter: FilterQuery<T>): Promise<DeleteResult> {
//         return this._model.deleteMany(filter);
//     }
//     bulkCreate(docs: Array<AnyKeys<T>>): Promise<Array<T>> {
//         return this._model.insertMany(docs);
//     }
//     bulkUpdate(
//         updates: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
//     ): Promise<BulkWriteResult> {
//         return this._model.bulkWrite(
//             updates.map(
//                 ({ filter, update }) =>
//                     ({
//                         updateOne: {
//                             filter,
//                             update,
//                         },
//                     }) as AnyBulkWriteOperation,
//             ),
//         );
//     }
//     bulkUpsert(
//         upserts: Array<{ filter: FilterQuery<T>; update: UpdateQuery<T> }>,
//     ): Promise<BulkWriteResult> {
//         return this._model.bulkWrite(
//             upserts.map(
//                 ({ filter, update }) =>
//                     ({
//                         updateOne: {
//                             filter,
//                             update,
//                             upsert: true,
//                         },
//                     }) as AnyBulkWriteOperation,
//             ),
//         );
//     }
//     aggregate(pipeline: PipelineStage[]): Promise<any[]> {
//         return this._model.aggregate(pipeline);
//     }
//     async aggregateWithPagination(
//         pipeline: PipelineStage[],
//         paginationPipeline?: PipelineStage.FacetPipelineStage[],
//         pageNumber = 1,
//         pageSize = 30,
//     ): Promise<PaginatedResult> {
//         const result = await this._model.aggregate([
//             ...pipeline,
//             {
//                 $facet: {
//                     pagination: [
//                         { $count: "totalRecords" },
//                         { $addFields: { pageSize, pageNumber } },
//                     ],
//                     records: [
//                         { $skip: (pageNumber - 1) * pageSize },
//                         { $limit: pageSize },
//                         ...(paginationPipeline ?? []),
//                     ],
//                 },
//             },
//             {
//                 $unwind: "$pagination",
//             },
//         ]);

//         return new PaginatedResult(result, pageNumber, pageSize);
//     }
// }
