import { Client } from "@elastic/elasticsearch";
import {
    QueryDslBoolQuery,
    SearchResponse,
} from "@elastic/elasticsearch/lib/api/types";
import { injectable } from "tsyringe";

export type SortOrder = "ASC" | "DESC";

@injectable()
export class BaseElasticRepository<T> {
    private _client: Client;
    private _index: string;
    private _mappings: Record<string, unknown>;

    constructor(index: string, mappings: Record<string, unknown>) {
        this._client = new Client({ node: process.env.ELASTIC_URI });
        this._index = index;
        this._mappings = mappings;

        this.createIndex()
            .then(() => {
                console.log(`Created ${this._index} index`);
            })
            .catch(console.error);
    }

    protected get client() {
        return this._client;
    }

    async createIndex() {
        const exists = await this._client.indices.exists({
            index: this._index,
        });
        if (!exists) {
            await this._client.indices.create({
                index: this._index,
                body: { mappings: this._mappings },
            });
        }
    }

    async create(
        document: T,
        id?: number | string,
    ): Promise<{ _id: string; result: string }> {
        return this._client.index({
            index: this._index,
            id: id?.toString(),
            body: document,
        });
    }

    async bulkCreate(documents: T[]): Promise<unknown> {
        const body = documents.flatMap((doc) => [
            { index: { _index: this._index } },
            doc,
        ]);
        return this._client.bulk({ body });
    }

    async findById(id: string): Promise<T | null> {
        const result = await this._client.get({ index: this._index, id });
        return result._source as T;
    }

    async updateById(
        id: number | string,
        document: Partial<T>,
    ): Promise<unknown> {
        return this._client.update({
            index: this._index,
            id: id.toString(),
            body: {
                doc: document,
            },
        });
    }

    async deleteById(id: number | string): Promise<unknown> {
        return this._client.delete({
            index: this._index,
            id: id.toString(),
        });
    }

    async search(
        query: Record<string, unknown>,
        sort: Record<string, SortOrder> = {},
        from = 0,
        size = 10,
    ): Promise<T[]> {
        const esResponse = await this._client.search({
            index: this._index,
            body: {
                query: {
                    bool: query as unknown as QueryDslBoolQuery,
                },
                sort: Object.entries(sort).map(([field, order]) => ({
                    [field]: { order: order.toLowerCase() as "asc" | "desc" },
                })),
                from,
                size,
            },
        });

        return (esResponse as SearchResponse<T>).hits.hits.map(
            (hit) => hit._source as T,
        );
    }

    async count(query: Record<string, unknown>): Promise<number> {
        const esResponse = await this._client.count({
            index: this._index,
            body: {
                query: {
                    bool: query as unknown as QueryDslBoolQuery,
                },
            },
        });
        return esResponse.count;
    }

    protected buildMultiMatch(
        search: string,
        fields: string[],
    ): Record<string, unknown> {
        return {
            // eslint-disable-next-line camelcase
            multi_match: {
                query: search,
                fields,
                type: "best_fields",
                operator: "and",
            },
        };
    }
}
