import { singleton } from "tsyringe";
import { Kavenegar, SendResponse } from "kavenegar-client";
import { sanitizeContent } from "../utils/strings";

@singleton()
export class KavenegarService {
    private _client: Kavenegar;

    constructor() {
        this._client = new Kavenegar(process.env.KAVENEGAR_API_KEY);
    }

    async sendOtp(phone: string, otp: string): Promise<SendResponse> {
        // Apply sanitization to ensure the OTP doesn't contain invalid characters
        const sanitizedOtp = sanitizeContent(otp);

        console.log('[KavenegarService] Sending OTP with sanitized value:', {
            original: otp,
            sanitized: sanitizedOtp,
            phone,
            template: "signupVerification"
        });

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedOtp,
                template: "signupVerification",
            });

            console.log('[KavenegarService] Successfully sent OTP SMS:', {
                phone,
                otp: sanitizedOtp,
                response
            });

            return response;
        } catch (error: any) {
            console.error('[KavenegarService] Failed to send OTP SMS:', {
                phone,
                otp: sanitizedOtp,
                error: error?.message || 'Unknown error',
                stack: error?.stack
            });
            throw error;
        }
    }

    async sendSellerNotification(
        phone: string,
        orderId: string,
        userName: string,
    ): Promise<SendResponse> {
        // Apply sanitization directly in the service to ensure it's always done
        const sanitizedOrderId = sanitizeContent(orderId);
        const sanitizedUserName = sanitizeContent(userName);

        console.log('[KavenegarService] Sending seller notification SMS with sanitized orderId:', {
            original: orderId,
            sanitizedOrderId: sanitizedOrderId,
            sanitizedUserName: sanitizedUserName,
            phone,
            template: "sellerNotification"
        });

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedOrderId,
                token2: sanitizedUserName,
                template: "sellerNotification",
            });

            console.log('[KavenegarService] Successfully sent seller notification SMS:', {
                phone,
                orderId: sanitizedOrderId,
                response
            });

            return response;
        } catch (error: any) {
            console.error('[KavenegarService] Failed to send seller notification SMS:', {
                phone,
                orderId: sanitizedOrderId,
                error: error?.message || 'Unknown error',
                stack: error?.stack
            });
            throw error;
        }
    }
}
