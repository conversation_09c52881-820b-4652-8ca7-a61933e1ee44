/* eslint-disable @typescript-eslint/no-unsafe-call */
import passport from "passport";
import {
    Strategy as InstagramStrategy,
    StrategyOption,
} from "passport-instagram";

const { INSTAGRAM_CLIENT_ID, INSTAGRAM_CLIENT_SECRET, INSTAGRAM_CALLBACK_URL } =
    process.env;

export function initPassportInstagram() {
    const options: StrategyOption = {
        clientID: INSTAGRAM_CLIENT_ID,
        clientSecret: INSTAGRAM_CLIENT_SECRET,
        callbackURL: INSTAGRAM_CALLBACK_URL,
    };

    passport.serializeUser(function (user: Express.User | undefined, done) {
        done(null, user);
    });

    passport.deserializeUser(function (user: Express.User | undefined, done) {
        done(null, user);
    });

    passport.use(
        "instagram",
        new InstagramStrategy(
            options,
            (accessToken, refreshToken, profile, done) => {
                // TODO: Test and build user object
                console.log({ accessToken, refreshToken, profile });
                done(profile);
            },
        ),
    );
}
