import jwt from "jsonwebtoken";
import passport from "passport";
import { ExtractJwt, Strategy } from "passport-jwt";

import { UnauthorizedError } from "../errors";

const { JWT_SECRET } = process.env;

interface JWTPayload {
    id: number;
    role: string;
    [key: string]: unknown;
}

export function signJWT(payload: JWTPayload) {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: "2 days" });
}

export function initPassportJWT() {
    const jwtOptions = {
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        secretOrKey: JWT_SECRET,
    };

    passport.serializeUser(function (user: Express.User | undefined, done) {
        done(null, user);
    });

    passport.deserializeUser(function (user: Express.User | undefined, done) {
        done(null, user);
    });

    passport.use(
        "jwt",
        new Strategy(jwtOptions, (payload: JWTPayload, done) => {
            try {
                const user = {
                    id: payload.id,
                    role: payload.role,
                };

                return done(null, user);
            } catch (err) {
                return done(new UnauthorizedError(), undefined);
            }
        }),
    );
}
