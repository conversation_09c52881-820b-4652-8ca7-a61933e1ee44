import passport from "passport";
import { ExtractJwt, Strategy } from "passport-jwt";

const { JWT_SECRET } = process.env;

export function initPassportJWTExists() {
    const jwtOptions = {
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        secretOrKey: JWT_SECRET,
    };

    passport.serializeUser(function (user: Express.User | undefined, done) {
        done(null, user);
    });

    passport.deserializeUser(function (user: Express.User | undefined, done) {
        done(null, user);
    });

    passport.use(
        "jwt-exists",
        new Strategy(jwtOptions, (payload, done) => {
            try {
                return done(null, payload);
            } catch (err) {
                console.error(err);
            }
        }),
    );
}
