import { OAuth2Client } from "google-auth-library";
import { injectable } from "tsyringe";
import { errors, utils } from "../../../common";
import axios from "axios";

@injectable()
export class GoogleOAuthService {
    private _client: OAuth2Client;

    constructor() {

        try {
            this._client = new OAuth2Client(
                process.env.GOOGLE_CLIENT_ID,
                process.env.GOOGLE_CLIENT_SECRET,
                "postmessage",
            );
        } catch (error) {
            console.error("Failed to initialize OAuth client:", error);
            throw error;
        }
    }

    public async verifyGoogleToken(token: string): Promise<{ email: string }> {
        try {
            // Since we're receiving access tokens from the client, handle it directly
            console.log("Handling as access token - fetching user info...");
            let userInfo: any = null;

            try {
                // Get user info using the access token with a direct HTTP request
                const googleUserInfoUrl = process.env.GOOGLE_USERINFO_URL || 'https://www.googleapis.com/oauth2/v3/userinfo';
                console.log("Using Google userinfo URL:", googleUserInfoUrl);
                const userInfoResponse = await axios.get(googleUserInfoUrl, {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                });
                userInfo = userInfoResponse.data;

                console.log("User info retrieved successfully:", {
                    email: userInfo.email,
                    verified_email: userInfo.email_verified,
                    name: userInfo.name || 'Not provided'
                });
            } catch (userInfoError) {
                console.log("Failed to get user info from access token:", userInfoError);
                throw userInfoError;
            }

            // Process user info regardless of how we obtained it
            console.log("User info/payload:", userInfo ? {
                sub: userInfo.sub,
                email: userInfo.email,
                email_verified: userInfo.email_verified || userInfo.verified_email,
                name: userInfo.name,
                picture: userInfo.picture ? 'Present' : 'Not present'
            } : 'null');

            if (userInfo === null || userInfo === undefined) {
                console.log("ERROR: No user info received from Google");
                throw new errors.GoogleOauthError(
                    "No user info received from Google",
                );
            }

            if (utils.isNil(userInfo?.email)) {
                console.log("ERROR: Email not found in user info");
                throw new errors.GoogleOauthError("Email not found");
            }

            return { email: userInfo.email };
        } catch (error: unknown) {
            if (error instanceof Error && 'response' in error) {
                // @ts-ignore - Accessing potential response data in OAuth errors
                const response = error.response?.data || error.response;
                console.log("Error response data:", JSON.stringify(response, null, 2));
            }

            throw new errors.GoogleOauthError(
                error instanceof Error
                    ? error.message
                    : "Failed to verify Google token",
            );
        }
    }
}
