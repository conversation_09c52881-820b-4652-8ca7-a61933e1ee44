export class OTPService {
    private static OTP_EXPIRATION_MINUTES: number = 5;

    private static generateOTP(): string {
        return `${Math.floor(Math.random() * 100000) + 10000}`.substring(0, 5);
    }

    public static generateOTPObject(): {
        value: string;
        expirationDate: Date;
        expirationInSeconds: number;
    } {
        const OTP = OTPService.generateOTP();
        const now = new Date();
        const expirationDate = OTPService.addMinutes(
            now,
            OTPService.OTP_EXPIRATION_MINUTES,
        );
        const expirationInSeconds = this.getSecondsDifference(
            now,
            expirationDate,
        );
        return { value: OTP, expirationDate, expirationInSeconds };
    }

    public static isOTPCorrect(userOTP: string, OTP: string): boolean {
        return userOTP === OTP;
    }

    public static isOTPExpired(OTPExpirationDate: Date): boolean {
        const now = new Date();
        return OTPExpirationDate < now;
    }

    public static addExpirationInSecondsToOTPObject(OTP: {
        expirationDate: Date;
        expirationInSeconds?: number;
    }): void {
        const now = new Date();
        OTP.expirationInSeconds = OTPService.getSecondsDifference(
            now,
            OTP.expirationDate,
        );
    }

    private static addMinutes(date: Date, minutes: number): Date {
        return new Date(date.getTime() + minutes * 60000);
    }

    private static getSecondsDifference(date1: Date, date2: Date): number {
        const diff = date2.getTime() - date1.getTime();
        return Math.floor(diff / 1000);
    }
}
