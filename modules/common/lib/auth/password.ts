import { randomBytes, scrypt, timingSafeEqual } from "crypto";
import { promisify } from "util";

const scryptAsync = promisify(scrypt);

export async function encodePassword(password: string) {
    const salt = randomBytes(8).toString("hex");
    const buf = (await scryptAsync(password, salt, 64)) as Buffer;

    return `${buf.toString("hex")}.${salt}`;
}

export async function comparePassword(
    encodedPassword: string,
    password: string,
) {
    const [hashedPassword, salt] = encodedPassword.split(".");
    const buf = (await scryptAsync(password, salt, 64)) as Buffer;

    return timingSafeEqual(Buffer.from(hashedPassword, "hex"), buf);
}
