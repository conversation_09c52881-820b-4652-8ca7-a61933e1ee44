/**
 * Success messages for multilingual support
 * Contains both English and Persian translations
 */

import { LANGUAGE } from "../../../base/types/typing";

/**
 * Type for success message keys
 */
export type SuccessMessageKey =
    | "USER_OTP_SENT_SUCCESSFULLY"
    | "USER_LOGGED_IN_SUCCESSFULLY"
    | "DEFAULT_SUCCESS_MESSAGE";

/**
 * Success messages by language
 */
export const successMessages: Record<
    LANGUAGE,
    Record<SuccessMessageKey, string>
> = {
    [LANGUAGE.ENGLISH]: {
        USER_OTP_SENT_SUCCESSFULLY: "SMS sent successfully",
        USER_LOGGED_IN_SUCCESSFULLY: "You have logged in successfully",
        DEFAULT_SUCCESS_MESSAGE: "Operation completed successfully",
    },
    [LANGUAGE.PERSIAN]: {
        USER_OTP_SENT_SUCCESSFULLY: "پیامک ارسال شد",
        USER_LOGGED_IN_SUCCESSFULLY: "شما با موفقیت وارد شدید",
        DEFAULT_SUCCESS_MESSAGE: "با موفقیت انجام شد",
    },
};

/**
 * Get a success message in the specified language
 * @param key The message key
 * @returns The translated message
 * @throws Error if PUBLIC_APP_LANG environment variable is not set
 */
export function getSuccessMessage(
    key: SuccessMessageKey,
): string {
    // Get language from environment variable
    const appLang = process.env.PUBLIC_APP_LANG;

    // Throw error if PUBLIC_APP_LANG is not set
    if (!appLang) {
        throw new Error(
            "PUBLIC_APP_LANG environment variable must be defined! Please set it to 'en' or 'fa' in your environment.",
        );
    }

    // Use the environment variable
    let lang: LANGUAGE = appLang === LANGUAGE.ENGLISH ? LANGUAGE.ENGLISH : LANGUAGE.PERSIAN;

    // Validate that the language is one of the supported languages
    if (lang !== LANGUAGE.ENGLISH && lang !== LANGUAGE.PERSIAN) {
        console.warn(
            `Warning: Unsupported language "${lang}". Using default language "${LANGUAGE.ENGLISH}" instead.`,
        );
        lang = LANGUAGE.ENGLISH;
    }

    // Return the message in the specified language
    return (
        successMessages[lang][key] || successMessages[LANGUAGE.ENGLISH].DEFAULT_SUCCESS_MESSAGE
    );
}
