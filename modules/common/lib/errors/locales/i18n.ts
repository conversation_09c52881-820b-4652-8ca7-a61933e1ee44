import i18next from "i18next";
import { resources } from "./resources";
import { LANGUAGE } from "../../../base/types/typing";

export type ResourceKey = keyof typeof resources.en.errors;

let i18nInitialized = false;

export async function initializeI18n(): Promise<void> {
    if (!i18nInitialized) {
        await i18next.init({
            resources,
            lng: "en",
            fallbackLng: "en",
        });
        i18nInitialized = true;
    }
}

// Function overloads
export function t(
    key: ResourceKey,
    options?: { lng?: LANGUAGE } & Record<string, string | undefined>,
): string;
export function t(
    key: string,
    options?: { lng?: LANGUAGE } & Record<string, string | undefined>,
): string;

export function t(
    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
    key: ResourceKey | string,
    options?: { lng?: LANGUAGE } & Record<string, string | undefined>,
): string {
    if (!i18nInitialized) {
        console.warn(
            "i18next is not initialized yet. Translations may not work correctly.",
        );
    }

    if (isResourceKey(key)) {
        return i18next.t(`errors:${key}`, options);
    } else {
        return i18next.t(key, options);
    }
}

function isResourceKey(key: string): key is ResourceKey {
    return key in resources.en.errors;
}
