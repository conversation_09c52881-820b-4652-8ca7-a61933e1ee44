import { DefinedError } from "ajv";
import { QueryFailedError, TypeORMError } from "typeorm";
import { DatabaseError } from "pg-protocol";
import { utils } from "..";
import { PGErrorCode } from "./pg-codes";
import { t } from "./locales";
import { getCurrentLanguage } from "../utils/language";
import { LANGUAGE } from "../../base/types/typing";

type SerializedError = { message: string; messageFa?: string; field?: string };

export abstract class APIError extends Error {
    abstract statusCode: number;
    messageFa?: string;

    constructor(message: string, messageFa?: string) {
        super(message);
        this.messageFa = messageFa;
    }

    abstract serializeErrors(): SerializedError[];
}

export class PGError extends APIError {
    statusCode = 400;
    private _isParsed = false;

    constructor(error: TypeORMError) {
        const message = t("pgError");
        const messageFa = t("pgError", { lng: getCurrentLanguage() });

        super(message, messageFa);

        if (!(error instanceof QueryFailedError)) {
            return;
        }

        const err = error.driverError as DatabaseError;
        if (err.code === PGErrorCode.DUPLICATE) {
            const column = err.detail
                ?.match(/\(.+\)=/)?.[0]
                ?.slice(1, -2)
                .replace(/[a-zA-Z]+\(/g, `"`)
                .replace(/::[a-zA-Z]+\)/g, `"`);
            this.message = t("duplicateColumn", { column });
            this.messageFa = t("duplicateColumn", { lng: getCurrentLanguage(), column });
            this._isParsed = true;
        } else if (err.code === PGErrorCode.EXISTING_RELATION) {
            const table = err.detail?.match(/".+"\./)?.[0]?.slice(1, -2);
            this.message = t("dependencyExists", { table });
            this.messageFa = t("dependencyExists", { lng: getCurrentLanguage(), table });
            this._isParsed = true;
        }
    }

    serializeErrors(): SerializedError[] {
        if (!this._isParsed) {
            throw new Error("Error not parsed correctly.");
        }
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class ValidationError extends APIError {
    statusCode = 400;
    _errors: DefinedError[];

    constructor(errors: DefinedError[]) {
        const message = t("validationError");
        const messageFa = t("validationError", { lng: getCurrentLanguage() });

        super(message, messageFa);
        this._errors = errors;
    }

    serializeErrors(): SerializedError[] {
        return this._errors.map((err) => {
            const field = err.instancePath.split("/").pop();

            const messageEn = utils.isNotNil(field)
                ? `${field} ${err.message}`
                : `${err.message}`;

            const messageFa = utils.isNotNil(field)
                ? `${field} ${err.message}`
                : `${err.message}`;

            return {
                message: t("validationError", {
                    lng: LANGUAGE.ENGLISH,
                    field,
                    defaultValue: messageEn,
                }),
                messageFa: t("validationError", {
                    lng: LANGUAGE.PERSIAN,
                    field,
                    defaultValue: messageFa,
                }),
                field,
            };
        });
    }
}

export class ActionsArrayError extends APIError {
    statusCode = 400;

    constructor() {
        super("Actions must be provided as an array");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class FileNotProvidedError extends APIError {
    statusCode = 400;

    constructor() {
        super("File does not provide successfully");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class UnknownActionTypeError extends APIError {
    statusCode = 400;

    constructor(actionType: string) {
        super(`Invalid response type, expected a ${actionType} response`);
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class MissingClientIdError extends APIError {
    statusCode = 400;

    constructor() {
        super("clientId is required for form actions.");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class MissingTextPropertyError extends APIError {
    statusCode = 400;

    constructor() {
        super("Response does not contain text property");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class NoConditionsMatchedError extends APIError {
    statusCode = 400;

    constructor() {
        super("No conditions matched the input text");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class MissingConfigurationKeyError extends APIError {
    statusCode = 400;

    constructor(key: string) {
        super(`Configuration key ${key} is missing`);
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class InvalidIntervalError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("invalidInterval");
        const messageFa = t("invalidInterval", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class BadRequestError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("badRequest");
        const messageFa = t("badRequest", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class InvalidConditionError extends APIError {
    statusCode = 400;

    constructor(message?: string, messageFa?: string) {
        const errorMessage = utils.isNotNil(message)
            ? message
            : t("invalidCondition");
        const errorMessageFa = utils.isNotNil(messageFa)
            ? t(messageFa, { lng: getCurrentLanguage() })
            : t("invalidCondition", { lng: getCurrentLanguage() });
        super(errorMessage, errorMessageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message ?? t("invalidCondition"),
                messageFa:
                    this.messageFa ?? t("invalidCondition", { lng: getCurrentLanguage() }),
            },
        ];
    }
}

export class WrongOtpError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("wrongOtp");
        const messageFa = t("wrongOtp", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class UnsuccessfulPayment extends APIError {
    statusCode = 400;

    constructor() {
        super("payment was unsuccessful");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class AlreadyPaid extends APIError {
    statusCode = 400;

    constructor() {
        super("Already Paid");
    }
    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class LargeAmountError extends APIError {
    statusCode = 400;

    constructor() {
        super("Amount is larger than what is should be");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class LittleAmountError extends APIError {
    statusCode = 400;

    constructor() {
        //!! what should we do here for english version?
        super("Amount must be more than 1000 Rial");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class InvalidFileNameError extends APIError {
    statusCode = 404;

    constructor() {
        const message = t("invalidFileName");
        const messageFa = t("invalidFileName", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class ExpiredOtpError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("otpExpired");
        const messageFa = t("otpExpired", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class InternalError extends APIError {
    statusCode = 500;

    constructor() {
        const message = t("internalError");
        const messageFa = t("internalError", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class UnauthorizedError extends APIError {
    statusCode = 401;

    constructor() {
        const message = t("unauthorized");
        const messageFa = t("unauthorized", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class InvalidCredentialsError extends APIError {
    statusCode = 401;

    constructor() {
        const message = t("invalidCredentials");
        const messageFa = t("invalidCredentials", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class UsernameAlreadyExistError extends APIError {
    statusCode = 409;

    constructor(resource?: string) {
        const translatedResource = resource!
            ? t(resource, { lng: getCurrentLanguage() })
            : undefined;

        const message = t("usernameAlreadyExist", {
            resource: resource!
                ? resource.charAt(0).toUpperCase() + resource.slice(1)
                : undefined,
        });
        const messageFa = t("usernameAlreadyExist", {
            lng: getCurrentLanguage(),
            resource: translatedResource,
        });

        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class EmailAlreadyExistError extends APIError {
    statusCode = 409;

    constructor(resource?: string) {
        const translatedResource = resource!
            ? t(resource, { lng: getCurrentLanguage() })
            : undefined;

        const message = t("emailAlreadyExist", {
            resource: resource!
                ? resource.charAt(0).toUpperCase() + resource.slice(1)
                : undefined,
        });
        const messageFa = t("emailAlreadyExist", {
            lng: getCurrentLanguage(),
            resource: translatedResource,
        });

        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class NotFoundError extends APIError {
    statusCode = 404;

    constructor(resource?: string) {
        // Use i18next to translate the resource and fallback if not provided
        const translatedResource = resource!
            ? t(resource, { lng: getCurrentLanguage() })
            : undefined;

        const message = utils.isNotNil(resource)
            ? t("resourceNotFound", {
                  resource:
                      resource.charAt(0).toUpperCase() + resource.slice(1),
              })
            : t("notFound");

        const messageFa = utils.isNotNil(resource)
            ? t("resourceNotFound", {
                  lng: getCurrentLanguage(),
                  resource: translatedResource,
              })
            : t("notFound", { lng: getCurrentLanguage() });

        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class DuplicationError extends APIError {
    statusCode = 400;

    constructor(resource?: string, uniqueColumn?: string) {
        const message =
            utils.isNotNil(resource) && utils.isNotNil(uniqueColumn)
                ? t("cannotDuplicateResource", { resource, uniqueColumn })
                : t("cannotDuplicate");
        const messageFa =
            utils.isNotNil(resource) && utils.isNotNil(uniqueColumn)
                ? t("cannotDuplicateResource", {
                      lng: getCurrentLanguage(),
                      resource,
                      uniqueColumn,
                  })
                : t("cannotDuplicate", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class ForbiddenError extends APIError {
    statusCode = 403;

    constructor() {
        const message = t("forbidden");
        const messageFa = t("forbidden", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class MissingArgumentsError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("missingArguments");
        const messageFa = t("missingArguments", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors() {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class TokenExpiredError extends APIError {
    statusCode = 401;

    constructor() {
        const message = t("tokenExpired");
        const messageFa = t("tokenExpired", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class MerchantError extends APIError {
    statusCode = 401;

    constructor() {
        super("Merchant Error");
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class DeleteDependencyError extends APIError {
    statusCode = 409;

    constructor(resource?: string) {
        const message = utils.isNotNil(resource)
            ? t("deleteDependencyErrorResource", {
                  resource:
                      resource.charAt(0).toUpperCase() + resource.slice(1),
              })
            : t("deleteDependencyError");
        const messageFa = utils.isNotNil(resource)
            ? t("deleteDependencyErrorResource", {
                  lng: getCurrentLanguage(),
                  resource,
              })
            : t("deleteDependencyError", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class AlreadyExistsError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("alreadyExists");
        const messageFa = t("alreadyExists", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}

export class GoogleOauthError extends APIError {
    statusCode = 400;

    constructor(message: string) {
        super(`${message}`);
    }

    serializeErrors(): SerializedError[] {
        return [{ message: this.message }];
    }
}

export class IncorrectLoginMethodError extends APIError {
    statusCode = 400;

    constructor() {
        const message = t("incorrectLoginMethod");
        const messageFa = t("incorrectLoginMethod", { lng: getCurrentLanguage() });
        super(message, messageFa);
    }

    serializeErrors(): SerializedError[] {
        return [
            {
                message: this.message,
                messageFa: this.messageFa,
            },
        ];
    }
}
