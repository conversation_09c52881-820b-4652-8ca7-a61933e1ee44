import { singleton } from "tsyringe";
import { NotificationJob } from "../../../../orders/apps/orders/types/jobs";
import { Job } from "bullmq";
import NotificationService from "../../../../notifications/apps/notifications/notification.service";

export interface NajvaApiResponse {
    detail: string;
}

@singleton()
export class NajvaService {
    private _najvaToken: string;
    private _najvaAPIKey: string;

    constructor(private _notificationService: NotificationService) {
        this._najvaToken = process.env.NAJVA_TOKEN;
        this._najvaAPIKey = process.env.NAJVA_API_KEY;
    }

    sendSmsToAllUsers = async () => {
        const url =
            "https://app.najva.com/api/v2/notification/management/send-direct";

        const sentTime = new Date().toLocaleString("en-US", {
            timeZone: "Asia/Tehran",
        });

        const payload = {
            title: "title",
            body: "body",
            ["onclick_action"]: 4,
            url: "https://app.palette-tech.io",
            utm: {},
            content: "content",
            ["light_up_screen"]: false,
            button: [],
            ["phone_number"]: "09335910559",
            ["sent_time"]: sentTime,
            subscribers: [],
        };

        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    Authorization: `Token ${this._najvaToken}`,
                    "X-api-key": this._najvaAPIKey,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });

            const data = await response.json();
            console.log("Response:", data);
        } catch (error) {
            console.error("Error:", error);
        }
    };

    openLink = async () => {
        const url =
            "https://app.najva.com/api/v2/notification/management/send-direct";

        const sentTime = new Date().toLocaleString("en-US", {
            timeZone: "Asia/Tehran",
        });

        const payload = {
            title: "title",
            body: "body",
            ["onclick_action"]: 0,
            url: "https://app.palette-tech.io",
            utm: {},
            ["light_up_screen"]: false,
            button: [],
            ["sent_time"]: sentTime,
        };

        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    Authorization: this._najvaToken,
                    "X-api-key": this._najvaAPIKey,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });

            const data = await response.json();
            console.log("Response:", data);
        } catch (error) {
            console.error("Error:", error);
        }
    };

    sendNotificationOpenActivity = async (job: Job<NotificationJob>) => {
        const { title, body, userId } = job.data;

        const iranTime = new Date().toLocaleString("en-US", {
            timeZone: "Asia/Tehran",
        });

        console.log(userId);

        const url =
            "https://app.najva.com/api/v2/notification/management/send-direct";

        const bodyData = {
            title,
            body,
            ["onclick_action"]: "open-activity",
            ["activity_package_name"]: "com.example.myapp",
            ["light_up_screen"]: false,
            ["sent_time"]: iranTime,
            utm: {},
            buttons: [],
            subscribers: [],
        };

        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Token ${this._najvaToken}`,
                    "X-api-key": this._najvaAPIKey,
                },
                body: JSON.stringify(bodyData),
            });

            if (response.ok) {
                const result = await response.json();
                console.log("Notification sent successfully:", result);
            } else {
                const errorResponse = await response.text();
                console.error(
                    "Failed to send notification. Status:",
                    response.status,
                    errorResponse,
                    response.statusText,
                );
            }
        } catch (error) {
            console.error("Error sending notification:", error);
        }
    };
}
