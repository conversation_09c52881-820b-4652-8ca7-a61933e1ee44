import { singleton } from "tsyringe";
import { App, applicationDefault, initializeApp } from "firebase-admin/app";
import { messaging } from "firebase-admin";

interface FcmMessage {
    notification: {
        title: string;
        body: string;
        imageUrl?: string;
    };
    data: Record<string, string>;
    token: string;
}

@singleton()
export class FcmService {
    private _firebaseAdmin: App;

    constructor() {
        this._firebaseAdmin = initializeApp({
            credential: applicationDefault(),
        });
    }

    sendNotification = async (message: FcmMessage[]) => {
        await messaging(this._firebaseAdmin).sendEach(message);
    };
}
