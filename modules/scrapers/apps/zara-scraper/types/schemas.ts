export interface ZaraScraperJob {
    name: string;
}

export interface ZaraCategoriesType {
    id: number;
    name: string;
    sectionName: string;
    seo: {
        seoCategoryId: number;
        keyword: string;
    };
    subcategories: ZaraCategoriesType[];
    hasSubcategories: boolean;
}

export interface ZaraCategoryInfoType {
    paginationInfo?: { isLastPage: boolean };
    productGroups?: Array<{
        elements: Array<{
            commercialComponents?: Array<{
                id: number;
                type: string;
                kind: string;
                name: string;
                sectionName: string;
                price: number;
                oldPrice: number;
                seo: {
                    keyword: string;
                    seoProductId: string;
                };
            }>;
        }>;
    }>;
}

export interface ZaraCategory {
    name: string;
    keyword: string;
    categoryId: number;
    url: string;
}

export interface ZaraProduct {
    name: string;
    url: string;
}

export interface ZaraProductDetail {
    productId: number;
    sku: number;
    name: string;
    kind: string;
    type: string;
    price: number;
    isLowInStock: boolean;
    isInStock: boolean;
    isDiscounted: boolean;
    attributes: {
        key: string;
        value: string;
    }[];
    images: string[];
}

export interface ZaraProductDetailResponse {
    product: {
        id: number;
        type: string;
        kind: string;
        name: string;
        detail: {
            colors: {
                name: string;
                productId: number;
                price: number;
                oldPrice?: number;
                sizes: {
                    name: string;
                    sku: number;
                    price: number;
                    oldPrice: number;
                    availability: StringWithAutocomplete<"in_stock">;
                }[];
                xmedia: {
                    url: string;
                }[];
                tagTypes: { type: StringWithAutocomplete<"FEW_ITEMS_LEFT"> }[];
            }[];
        };
    };
}
