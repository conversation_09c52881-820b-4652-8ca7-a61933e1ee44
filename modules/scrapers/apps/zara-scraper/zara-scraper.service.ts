import Redis from "ioredis";
import { inject, injectable, registry } from "tsyringe";
import { RedisConnection } from "../../../common/lib/redis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { Job, Processor } from "bullmq";
import {
    ZaraCategoriesType,
    ZaraCategory,
    ZaraCategoryInfoType,
    ZaraProduct,
    ZaraProductDetail,
    ZaraProductDetailResponse,
} from "./types";
import { registries } from "./registries";
import { utils } from "../../../common";
import InventoryService from "../../../inventories/apps/inventories/inventories.service";
import WebsiteService from "../../../websites/apps/websites/website.service";

const BASE_URL = "https://www.zara.com/tr/en";
const WEBSITE_NAME = "Zara";

@registry(registries)
@injectable()
export default class ZaraScraperService {
    private _zaraScraperQueue: BullMQModule;
    private _zaraScraperJobName: string = "zara-scraper";
    private _redisConnection: RedisConnection;
    private _redis: Redis;
    private _userAgent =
        "Mozilla/5.0 (Windows NT 10.4; Win64; x64; en-US) Gecko/20100101 Firefox/71.0";

    constructor(
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
        private _inventoryService: InventoryService,
        private _websiteService: WebsiteService,
    ) {
        this._redisConnection = createRedisConnection();
        this._redis = this._redisConnection.getConnection();
        const zaraScraperProcessor = createJobProcessor(
            this._zaraScraperProcessor.bind(this),
        );
        this._zaraScraperQueue = createQueueModule(
            this._zaraScraperJobName,
            zaraScraperProcessor,
            this._redisConnection,
        );
    }

    private _zaraScraperProcessor = async (job: Job) => {
        const website =
            await this._websiteService.getWebsiteByName(WEBSITE_NAME);
        const categories = await this._scrapeCategories();
        for (const category of categories) {
            const lastPageOfCategory =
                await this._getLastPageOfCategory(category);

            if (utils.isNil(lastPageOfCategory)) {
                continue;
            }
            const zaraProducts: ZaraProduct[] =
                this._scrapeProductsOfCategory(lastPageOfCategory);

            for (const zaraProduct of zaraProducts) {
                const detailedProducts =
                    await this._scrapeProductDetails(zaraProduct);
                if (
                    utils.isNil(detailedProducts) ||
                    detailedProducts.length === 0
                ) {
                    continue;
                }

                await Promise.all(
                    detailedProducts.map((detailedProduct) => {
                        const url = new URL(zaraProduct.url);
                        url.searchParams.append(
                            "v1",
                            detailedProduct.productId.toString(),
                        );

                        return this._inventoryService.addAutomaticInventory({
                            websiteId: website.id,
                            referenceId: detailedProduct.productId,
                            name: detailedProduct.name,
                            image: detailedProduct.images,
                            cost: detailedProduct.price.toString(),
                            url: url.href,
                            attributes: detailedProduct.attributes,
                            sku: detailedProduct.sku.toString(),
                            state: !detailedProduct.isInStock
                                ? "NO_STOCK"
                                : detailedProduct.isLowInStock
                                  ? "LOW_STOCK"
                                  : "AVAILABLE",
                        });
                    }),
                );
            }
        }

        await job.updateProgress(100);
    };

    private async _getLastPageOfCategory(
        category: ZaraCategory,
    ): Promise<ZaraCategoryInfoType | null> {
        let page = 1;
        let response = await this._getCategoryInfo(category, page);
        if (
            utils.isNil(response.paginationInfo) ||
            utils.isNil(response.productGroups)
        ) {
            return null;
        }

        while (!response.paginationInfo!.isLastPage) {
            ++page;
            response = await this._getCategoryInfo(category, page);
        }

        return response;
    }

    private _scrapeProductsOfCategory = (
        category: ZaraCategoryInfoType,
    ): ZaraProduct[] => {
        return (category.productGroups ?? []).flatMap((productGroup) =>
            productGroup.elements.flatMap((element) =>
                (element.commercialComponents ?? []).map(
                    (commercialComponent) => ({
                        name: commercialComponent.name,
                        url: `${BASE_URL}/${commercialComponent.seo.keyword}-p${commercialComponent.seo.seoProductId}.html`,
                    }),
                ),
            ),
        );
    };

    private _scrapeProductDetails = async (
        product: ZaraProduct,
    ): Promise<ZaraProductDetail[]> => {
        const response = await this._getProductDetail(product.url);
        if (utils.isNil(response) || utils.isNil(response.product)) {
            return [];
        }
        const { name, kind, type, detail } = response.product;

        return detail.colors.flatMap((color) => {
            const images = color.xmedia.map((media) => media.url);
            const isLowInStock = utils.isNotNil(
                color.tagTypes?.find((tag) => tag.type === "FEW_ITEMS_LEFT"),
            );

            return color.sizes.map((size) => ({
                productId: color.productId,
                sku: size.sku,
                name,
                kind,
                type,
                attributes: [
                    { key: "color", value: color.name },
                    { key: "size", value: size.name },
                ],
                images,
                price: color.price,
                isDiscounted:
                    utils.isNotNil(color.oldPrice) &&
                    color.price !== color.oldPrice,
                color: color.name,
                size: size.name,
                isLowInStock,
                isInStock: size.availability === "in_stock",
            }));
        });
    };

    private _scrapeCategories = async (): Promise<ZaraCategory[]> => {
        const categories = await this._getCategories();
        const leafCategories = this._findLeafCategories(categories);
        const result = leafCategories
            .map((leaf) => {
                if (utils.isNotNil(leaf.seo)) {
                    return {
                        name: leaf.sectionName,
                        keyword: leaf.seo.keyword,
                        categoryId: leaf.seo.seoCategoryId,
                        url: "",
                    };
                }
                return null;
            })
            .filter(utils.isNotNil);

        const uniqueCategories = Array.from(
            new Set(result.map((c) => c.categoryId)),
        ).map((id) => result.find((c) => c.categoryId === id));

        for (const category of uniqueCategories) {
            category!.url = `${BASE_URL}/${category!.keyword}-l${category!.categoryId}.html`;
        }

        return uniqueCategories.map((category) => ({
            name: category!.name,
            keyword: category!.keyword,
            categoryId: category!.categoryId,
            url: category!.url,
        }));
    };

    private _getCategories = async () => {
        const url = new URL("https://www.zara.com/tr/en/categories");
        url.searchParams.set("ajax", "true");

        const response = await this._fetchWithUserAgent(url);
        if (!response.ok) {
            return [];
        }
        const body = await response.json();
        return (body as { categories: ZaraCategoriesType[] }).categories;
    };

    private _getCategoryInfo = async (
        category: ZaraCategory,
        page: number = 1,
    ) => {
        const url = new URL(category.url);
        url.searchParams.set("ajax", "true");
        url.searchParams.set("page", page.toString());

        const response = await this._fetchWithUserAgent(url);
        if (!response.ok) {
            return {};
        }
        const body = await response.json();
        return body as ZaraCategoryInfoType;
    };

    private _getProductDetail = async (productUrl: string) => {
        const url = new URL(productUrl);
        url.searchParams.set("ajax", "true");

        const response = await this._fetchWithUserAgent(url);
        if (!response.ok) {
            return null;
        }
        const body = await response.json();
        return body as ZaraProductDetailResponse;
    };

    private _fetchWithUserAgent = async (url: URL) => {
        return fetch(url.toString(), {
            method: "GET",
            headers: {
                "User-Agent": this._userAgent,
            },
        });
    };

    private _findLeafCategories = (categories: ZaraCategoriesType[]) => {
        const leafCategories: ZaraCategoriesType[] = [];

        function recurse(categoryList: ZaraCategoriesType[]) {
            for (const category of categoryList) {
                if (category.hasSubcategories) {
                    recurse(category.subcategories);
                } else {
                    leafCategories.push(category);
                }
            }
        }

        recurse(categories);
        return leafCategories;
    };

    async initializeJobs() {
        // await this._zaraScraperQueue.close();
        await this._zaraScraperQueue.addJob(this._zaraScraperJobName, {});
        await this._zaraScraperQueue.addJob(
            this._zaraScraperJobName,
            {},
            { repeat: { every: 1 * 60 * 60 * 1000 } },
        );
    }

    async closeJob() {
        await this._zaraScraperQueue.close();
    }

    get queue() {
        return this._zaraScraperQueue;
    }
}
