import { injectable } from "tsyringe";
import { Request, Response } from "express";

import RateService from "./rates.service";
import { OpenAPI } from "../../../common/lib/decorators";

import { GetRatesResponseSchema } from "./responses";

@injectable()
export default class RateController {
    constructor(private _service: RateService) {}

    @OpenAPI(
        "rates",
        "/",
        "get",
        undefined,
        undefined,
        GetRatesResponseSchema,
        "bearerAuth",
    )
    getRates = async (req: Request, res: Response) => {
        const rates = await this._service.getRates();

        res.success(rates);
    };
}
