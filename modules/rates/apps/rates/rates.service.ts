import { inject, injectable, registry } from "tsyringe";
import { LiraPrice } from "./types";
import { RedisConnection } from "../../../common/lib/redis";
import Redis from "ioredis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { Processor } from "bullmq";
import { registries } from "./registries";
import { utils } from "../../../common";

const { NAVASAN_KEY } = process.env;

const CURRENCIES = ["lira"];

@registry(registries)
@injectable()
export default class UsersService {
    private _liraQueue: BullMQModule<unknown>;
    private _liraJobName: string = "lira";
    private _redisConnection: RedisConnection;
    private _redis: Redis;

    constructor(
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();
        this._redis = this._redisConnection.getConnection();

        const liraProcessor: Processor = createJobProcessor(
            this.fetchLiraPrice.bind(this),
        );
        this._liraQueue = createQueueModule(
            this._liraJobName,
            liraProcessor,
            this._redisConnection,
        );
    }

    private async fetchLiraPrice() {
        const url = `http://api.navasan.tech/latest/?item=try&api_key=${NAVASAN_KEY}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Response status: ${response.status}`);
        }

        const result: LiraPrice = await response.json();

        await this._redis.set("lira", result.try.value);
    }

    getRates = async () => {
        const rates = [];
        for (const currency of CURRENCIES) {
            const price = await this._redis.get(currency);
            if (utils.isNotNil(price)) {
                rates.push({
                    currency,
                    price,
                });
            }
        }

        return rates;
    };

    async scheduleLiraPriceJob() {
        await this._liraQueue.addRepeatableJob(
            this._liraJobName,
            {},
            { every: 60 * 5 * 1000 },
        );
    }

    async addLiraJob() {
        await this._liraQueue.addJob(this._liraJobName, {});
    }

    async closeJob() {
        await this._liraQueue.close();
    }
}
