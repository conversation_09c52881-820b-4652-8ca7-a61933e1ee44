import { JSONSchemaType } from "ajv";
import { ClientOrderResponse, OrderStatus } from "../types";
import { OrderItemResponseSchema } from "../../../../order-items/apps/order-items/responses";

export const GetClientOrdersResponseSchema: JSONSchemaType<
    ClientOrderResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "order_",
        properties: {
            id: {
                type: "number",
            },
            price: {
                type: "string",
            },
            cost: {
                type: "string",
            },
            shippingPrice: {
                type: "string",
            },
            status: {
                type: "string",
                enum: Object.values(OrderStatus),
            },
            items: OrderItemResponseSchema,
        },
        required: [],
    },
};
