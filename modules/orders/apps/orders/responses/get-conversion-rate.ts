import { JSONSchemaType } from "ajv";
import { OrdersConversionResponse } from "../types";

export const GetConversionRateResponseSchema: JSONSchemaType<OrdersConversionResponse> =
    {
        type: "object",
        properties: {
            metadata: {
                type: "object",
                properties: {
                    from: {
                        type: "string",
                    },
                    to: {
                        type: "string",
                    },
                    interval: {
                        type: "string",
                    },
                },
                required: [],
            },
            data: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        total: {
                            type: "integer",
                        },
                        success: {
                            type: "integer",
                        },
                    },
                    required: [],
                },
            },
        },
        required: [],
    };
