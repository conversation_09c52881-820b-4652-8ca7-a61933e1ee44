import { paginatedSerializer, serializer } from "../../../../common/lib/utils";
import { GetOrdersResponseSchema } from "./get-orders";
import { GetClientOrderResponseSchema } from "./get-client-order";
import { GetClientOrdersResponseSchema } from "./get-client-orders";

export const getOrdersSerializer = paginatedSerializer(GetOrdersResponseSchema);
export const getClientOrderSerializer = serializer(
    GetClientOrderResponseSchema,
);
export const getClientOrdersSerializer = serializer(
    GetClientOrdersResponseSchema,
);

export * from "./get-orders";
export * from "./get-count";
export * from "./get-retention-rate";
export * from "./get-conversion-rate";
export * from "./get-client-order";
