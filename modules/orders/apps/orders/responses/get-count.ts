import { JSONSchemaType } from "ajv";
import { OrdersCountResponse } from "../types";

export const GetOrdersCountResponseSchema: JSONSchemaType<OrdersCountResponse> =
    {
        type: "object",
        properties: {
            metadata: {
                type: "object",
                properties: {
                    from: {
                        type: "string",
                    },
                    to: {
                        type: "string",
                    },
                },
                required: [],
            },
            data: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        count: {
                            type: "integer",
                        },
                        price: {
                            type: "string",
                        },
                    },
                    required: [],
                },
            },
        },
        required: [],
    };
