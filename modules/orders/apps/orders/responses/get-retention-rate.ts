import { JSONSchemaType } from "ajv";
import { OrdersRetentionResponse } from "../types";

export const GetRetentionRateResponseSchema: JSONSchemaType<OrdersRetentionResponse> =
    {
        type: "object",
        properties: {
            metadata: {
                type: "object",
                properties: {
                    from: {
                        type: "string",
                    },
                    to: {
                        type: "string",
                    },
                    interval: {
                        type: "string",
                    },
                },
                required: [],
            },
            data: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        total: {
                            type: "integer",
                        },
                        success: {
                            type: "integer",
                        },
                    },
                    required: [],
                },
            },
        },
        required: [],
    };
