import { Order } from "./entities";

export interface OrderInventoryDto {
    id: number;
    amount: number;
}

export type AddOrderDto = Pick<
    Order,
    "origin" | "destination" | "postalCode" | "shippingPrice"
> & {
    clientId?: number;
    inventories: OrderInventoryDto[];
};

export type EditOrderDto = Pick<
    Order,
    "origin" | "destination" | "postalCode" | "shippingPrice"
> & {
    inventories: OrderInventoryDto[];
};

export type EditOrderStatusDto = Pick<Order, "status">;

export type OrderDto = Pick<Order, "status" | "destination" | "postalCode">;

export interface OrderItemDto {
    id: number;
    name: string;
    amount: number;
    cost: string;
    price: string;
    total: number;
    reserved: number;
}
