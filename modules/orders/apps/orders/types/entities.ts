import { Client } from "../../../../users/apps/client/types";
import { User } from "../../../../users/apps/users/types";
import { ExchangeRateType, OrderStatus } from "./enums";

export interface Order {
    id: number;
    origin: string;
    destination: string;
    receiver: string;
    exchangeRateType: ExchangeRateType ;
    exchangeRateValue: number;
    status: OrderStatus,
    user: User;
    userId: number;
    client?: Client;
    clientId: number;
    postalCode: string;
    price: string;
    cost: string;
    shippingPrice: string;
    profit: string;
    createdAt: Date;
    updatedAt: Date;
}
