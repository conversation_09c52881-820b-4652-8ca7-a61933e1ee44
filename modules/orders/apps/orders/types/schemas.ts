import { OrderItemSchema } from "../../../../order-items/apps/order-items/types";
import { Order } from "../types";

export type OrdersResponse = Pick<
    Order,
    | "origin"
    | "status"
    | "destination"
    | "price"
    | "cost"
    | "profit"
    | "exchangeRateType"
    | "exchangeRateValue"
    | "shippingPrice"
    | "postalCode"
    | "receiver"
> & {
    items: OrderItemSchema[];
    createdAt: string;
};

export type ClientOrderResponse = Pick<
    Order,
    "id" | "status" | "price" | "cost" | "shippingPrice"
> & {
    items: OrderItemSchema[];
};

export interface OrdersCountMetadata {
    from: string;
    to: string;
}
export interface OrdersCountData {
    count: number;
    price: string;
}

export interface OrdersCountResponse {
    metadata: OrdersCountMetadata;
    data: OrdersCountData[];
}

export interface OrdersConversionMetadata {
    from: string;
    to: string;
    interval: string;
}
export interface OrdersConversionData {
    total: number;
    success: number;
}

export interface OrdersConversionResponse {
    metadata: OrdersConversionMetadata;
    data: OrdersConversionData[];
}

export interface OrdersRetentionMetadata {
    from: string;
    to: string;
    interval: string;
}
export interface OrdersRetentionData {
    total: number;
    success: number;
}

export interface OrdersRetentionResponse {
    metadata: OrdersRetentionMetadata;
    data: OrdersRetentionData[];
}
