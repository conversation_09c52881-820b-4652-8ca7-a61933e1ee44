import { JSONSchemaType } from "ajv";
import { AddOrderDto } from "../types";

export const AddOrderSchema: JSONSchemaType<AddOrderDto> = {
    $id: "add-order",
    type: "object",
    properties: {
        origin: {
            type: "string",
            example: "Turkey",
        },
        postalCode: {
            type: "string",
            example: "1234567890",
        },
        destination: {
            type: "string",
            example: "Iran",
        },
        clientId: {
            type: "number",
            nullable: true,
            example: 1,
        },
        shippingPrice: {
            type: "string",
            example: "20000",
        },
        inventories: {
            type: "array",
            items: {
                type: "object",
                properties: {
                    amount: {
                        type: "number",
                    },
                    id: {
                        type: "number",
                    },
                },
                required: ["amount", "id"],
            },
            example: [
                {
                    amount: 1,
                    id: 1,
                },
            ],
        },
    },
    required: ["postalCode", "origin", "destination", "inventories"],
    additionalProperties: false,
};
