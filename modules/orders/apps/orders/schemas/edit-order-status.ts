import { JSONSchemaType } from "ajv";
import { EditOrderStatusDto, OrderStatus } from "../types";

export const EditOrderStatusSchema: JSONSchemaType<EditOrderStatusDto> = {
    $id: "edit-order-status",
    type: "object",
    properties: {
              status: {
                  type: "string",
                  enum: Object.values(OrderStatus),
              },
    },
    required: ["status"],
    additionalProperties: false,
};
