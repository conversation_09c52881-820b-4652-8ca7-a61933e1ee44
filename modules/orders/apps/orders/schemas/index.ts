import { validators } from "../../../../common";
import { AddOrderSchema } from "./add-order";
import { EditOrderStatusSchema } from "./edit-order-status";
import { EditOrderSchema } from "./edit-order";

const bv = validators.AJVValidator([
    AddOrderSchema,
    EditOrderSchema,
    EditOrderStatusSchema,
]);

export * from "./add-order";
export * from "./edit-order";
export * from "./edit-order-status";

export const addOrder = bv("add-order");
export const editOrder = bv("edit-order");
export const editOrderStatus = bv("edit-order-status");
