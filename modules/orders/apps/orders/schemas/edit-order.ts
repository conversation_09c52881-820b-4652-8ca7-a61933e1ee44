import { JSONSchemaType } from "ajv";
import { EditOrderDto } from "../types";

export const EditOrderSchema: JSONSchemaType<EditOrderDto> = {
    $id: "edit-order",
    type: "object",
    properties: {
        origin: {
            type: "string",
            example: "Turkey",
        },
        postalCode: {
            type: "string",
            example: "1234556789",
        },
        destination: {
            type: "string",
            example: "Iran",
        },
        shippingPrice: {
            type: "string",
            example: "20000",
        },
        inventories: {
            type: "array",
            items: {
                type: "object",
                properties: {
                    amount: {
                        type: "number",
                    },
                    id: {
                        type: "number",
                    },
                },
                required: ["amount", "id"],
            },
            example: [
                {
                    amount: 1,
                    id: 1,
                },
            ],
        },
    },
    required: [],
    additionalProperties: false,
};
