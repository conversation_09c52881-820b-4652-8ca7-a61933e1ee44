import express from "express";
import { middlewares } from "../../../common";
import { container } from "tsyringe";
import OrdersController from "./order.controller";
import * as schemas from "./schemas";

const { JWT } = middlewares;

const router = express.Router();

const controller = container.resolve(OrdersController);

router
    .route("/")
    .post(JWT, schemas.addOrder, controller.addOrder)
    .get(JWT, controller.getOrders);

router.route("/:id").patch(JWT, schemas.editOrder, controller.editOrder);

router.route("/report/count").get(JWT, controller.getOrdersCount);

router.route("/report/conversion").get(JWT, controller.getConversionRate);

router.route("/report/retention").get(JWT, controller.getRetentionRate);

router
    .route("/:id/status")
    .patch(JWT, schemas.editOrderStatus, controller.editOrderStatus);

export default router;
