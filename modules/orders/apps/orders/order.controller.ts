import { injectable } from "tsyringe";
import OrdersService from "./order.service";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import {
    AddOrderSchema,
    EditOrderSchema,
    EditOrderStatusSchema,
} from "./schemas";
import {
    GetConversionRateResponseSchema,
    GetOrdersCountResponseSchema,
    GetOrdersResponseSchema,
    GetRetentionRateResponseSchema,
} from "./responses";

@injectable()
export default class OrdersController {
    constructor(private _service: OrdersService) {}

    @OpenAPI(
        "orders",
        "/",
        "post",
        AddOrderSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addOrder = async (req: Request, res: Response) => {
        const { body, user } = req;
        await this._service.addOrder(body, user!);
        res.success({});
    };

    @OpenAPI(
        "orders",
        "/",
        "get",
        undefined,
        undefined,
        GetOrdersResponseSchema,
        "bearerAuth",
    )
    getOrders = async (req: Request, res: Response) => {
        const { parsedQuery, user } = req;

        const orders = await this._service.getOrders(user!, parsedQuery);

        res.success(orders);
    };

    @OpenAPI(
        "orders",
        "/{id}",
        "patch",
        EditOrderSchema,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editOrder = async (req: Request, res: Response) => {
        const {
            body,
            user,
            params: { id },
        } = req;

        await this._service.editOrder(parseInt(id), user!, body);

        res.success({});
    };

    @OpenAPI(
        "orders",
        "/{id}/status",
        "patch",
        EditOrderStatusSchema,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editOrderStatus = async (req: Request, res: Response) => {
        const {
            body,
            user,
            params: { id },
        } = req;

        await this._service.editOrderStatus(parseInt(id), user!, body);

        res.success({});
    };

    @OpenAPI(
        "orders",
        "/report/count",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2000-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
        ],
        GetOrdersCountResponseSchema,
        "bearerAuth",
    )
    getOrdersCount = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const ordersCount = await this._service.getOrdersCount(
            user!,
            parsedQuery,
        );

        res.success(ordersCount);
    };

    @OpenAPI(
        "orders",
        "/report/conversion",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2000-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "interval",
                schema: {
                    type: "string",
                    enum: ["daily", "weekly", "monthly", "yearly"],
                },
            },
        ],
        GetConversionRateResponseSchema,
        "bearerAuth",
    )
    getConversionRate = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const conversionRate = await this._service.getConversionRate(
            user!,
            parsedQuery,
        );

        res.success(conversionRate);
    };

    @OpenAPI(
        "orders",
        "/report/retention",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2000-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "interval",
                schema: {
                    type: "string",
                    enum: ["daily", "weekly", "monthly", "yearly"],
                },
            },
        ],
        GetRetentionRateResponseSchema,
        "bearerAuth",
    )
    getRetentionRate = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const retentionRate = await this._service.getRetentionRate(
            user!,
            parsedQuery,
        );

        res.success(retentionRate);
    };
}
