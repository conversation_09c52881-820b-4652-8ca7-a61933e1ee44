import { EntitySchema } from "typeorm";
import { Order } from "./types/entities";
import { OrderStatus, ExchangeRateType } from "./types/enums";

export const OrderSchema = new EntitySchema<Order>({
    name: "order",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        origin: {
            type: String,
            nullable: true,
        },
        destination: {
            type: String,
            nullable: true,
        },
        receiver: {
            type: String,
            nullable: true,
        },
        exchangeRateType: {
            type: "enum",
            enum: ExchangeRateType,
        },
        exchangeRateValue: {
            type: Number,
        },
        userId: {
            type: Number,
        },
        clientId: {
            type: Number,
            nullable: true,
        },
        status: {
            type: "enum",
            enum: OrderStatus,
            default: OrderStatus.PENDING_SELLER_REVIEW,
        },
        postalCode: {
            type: String,
            nullable: true,
        },
        profit: {
            type: "numeric",
        },
        price: {
            type: "numeric",
        },
        cost: {
            type: "numeric",
        },
        shippingPrice: {
            type: "numeric",
            default: 0,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
        },
        client: {
            type: "many-to-one",
            target: "client",
            nullable: true,
        },
    },
});
