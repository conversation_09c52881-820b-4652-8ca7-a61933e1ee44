import { SelectQueryBuilder } from "typeorm";
import { layers } from "../../../common";
import { OrderSchema } from "./order.model";
import { Order } from "./types";

export default class OrderRepo extends layers.BaseTypeormRepository<Order> {
    relations = [];
    constructor() {
        super(OrderSchema);
    }

    private _addSelectTimeWindow(
        queryBuilder: SelectQueryBuilder<Order>,
        interval: string,
    ) {
        if (interval === "daily") {
            queryBuilder.addSelect(
                "TO_CHAR(order.createdAt, 'YYYY-MM-DD')",
                "date",
            );
        } else if (interval === "weekly") {
            queryBuilder.addSelect(
                `TO_CHAR(order.createdAt, 'YYYY-WW')`,
                "date",
            );
        } else if (interval === "monthly") {
            queryBuilder.addSelect(
                "TO_CHAR(order.createdAt, 'YYYY-MM')",
                "date",
            );
        } else if (interval === "yearly") {
            queryBuilder.addSelect("TO_CHAR(order.createdAt, 'YYYY')", "date");
        }
    }

    getOrdersOfUser = (userId: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("order")
            .where("order.userId = :userId", { userId })
            .filter(query.filter)
            .leftJoinAndSelect(
                "order-items",
                "order-items",
                "order-items.orderId = order.id",
            )
            .leftJoinAndSelect(
                "inventory",
                "inventory",
                "order-items.inventoryId = inventory.id",
            )
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };

    getConversionRate = async (
        userId: number,
        from: string,
        to: string,
        interval: string,
    ) => {
        const query = this.createQueryBuilder("order")
            .select("COUNT(*)::INTEGER as total")
            .addSelect(
                "COUNT(CASE WHEN order.status != 'PENDING_SELLER_REVIEW' THEN 1 END)::INTEGER as success",
            )
            .where("order.userId = :userId", { userId })
            .andWhere("order.createdAt >= :from", { from })
            .andWhere("order.createdAt <= :to", { to });

        this._addSelectTimeWindow(query, interval);

        query.groupBy("date");

        return query.getRawMany();
    };

    getOrdersCount = async (userId: number, from: string, to: string) => {
        const query = this.createQueryBuilder("order")
            .select("COUNT(*)::INTEGER AS count")
            .addSelect("SUM(order.price) AS price")
            .where("order.userId = :userId", {
                userId,
            })
            .andWhere("order.createdAt >= :from", { from })
            .andWhere("order.createdAt <= :to", { to });

        return query.getRawMany();
    };

    getRetentionRate = async (
        userId: number,
        from: string,
        to: string,
        interval: string,
    ) => {
        const multipleOrdersQuery = this.createQueryBuilder("order")
            .select("COUNT(*)::INTEGER as total")
            .addSelect(
                "COUNT(CASE WHEN pOrder.id IS NOT NULL THEN 1 END)::INTEGER as success",
            )
            .leftJoin(
                "order",
                "pOrder",
                `pOrder.clientId = order.clientId AND pOrder.createdAt < order.createdAt AND pOrder.status <> :status`,
                { status: "PENDING_SELLER_REVIEW" },
            )
            .where("order.userId = :userId", { userId })
            .andWhere("order.createdAt >= :from", { from })
            .andWhere("order.createdAt <= :to", { to })
            .andWhere("order.status <> :status", {
                status: "PENDING_SELLER_REVIEW",
            });

        this._addSelectTimeWindow(multipleOrdersQuery, interval);

        multipleOrdersQuery.groupBy("date");

        return multipleOrdersQuery.getRawMany();
    };
    
    getClientActiveOrder = async (clientId: number) => {
        return this._repo
            .createQueryBuilder("order")
            .where("order.clientId = :clientId", { clientId })
            .andWhere("order.status = 'PENDING_SELLER_REVIEW'")
            .leftJoinAndSelect(
                "order-items",
                "order-items",
                "order-items.orderId = order.id",
            )
            .leftJoinAndSelect(
                "inventory",
                "inventory",
                "order-items.inventoryId = inventory.id",
            )
            .getRawMany();
    };

    getClientOrders = async (clientId: number) => {
        return this._repo
            .createQueryBuilder("order")
            .where("order.clientId = :clientId", { clientId })
            .leftJoinAndSelect(
                "order-items",
                "order-items",
                "order-items.orderId = order.id",
            )
            .leftJoinAndSelect(
                "inventory",
                "inventory",
                "order-items.inventoryId = inventory.id",
            )
            .getRawMany();
    };
}
