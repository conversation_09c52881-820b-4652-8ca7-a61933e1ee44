import { createBullMQModule } from "../../../common/lib/bullmq";
import { createNotificationService } from "../../../common/lib/notifications";
import { createRedisConnection } from "../../../common/lib/redis";

export const registries = [
    { token: "redis", useFactory: () => createRedisConnection },
    { token: "queue", useFactory: () => createBullMQModule },
    { token: "notification", useFactory: () => createNotificationService },
];
