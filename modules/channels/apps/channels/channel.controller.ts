import { injectable } from "tsyringe";
import ChannelService from "./channel.service";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import { GetChannelResponseSchema, DisconnectInstagramResponseSchema } from "./responses";
import { EditChannelSchema } from "./schemas";

const { WEB_APP_URL } = process.env;

@injectable()
export default class ChannelController {
    constructor(private _service: ChannelService) {}

    @OpenAPI(
        "channels",
        "/instagram/connect",
        "get",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    initInstagramSso = (req: Request, res: Response) => {
        const { user } = req;

        const url = this._service.initInstagramSso(user!);

        res.success(url);
    };
    @OpenAPI(
        "channels",
        "/instagram/callback",
        "get",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    processInstagramSso = async (req: Request, res: Response) => {
        const {
            query: { code, state },
        } = req;

        try {
            // Log the received parameters for debugging
            console.log('Instagram callback received:', { code, state });

            // Handle the case where state might be undefined or not a valid number
            const userId = state ? parseInt(String(state), 10) : null;

            if (!code) {
                console.error('Missing code parameter in Instagram callback');
                return res.status(400).json({ errors: [{ message: 'Missing code parameter' }] });
            }

            await this._service.processInstagramSso(String(code), userId);

            res.redirect(`${WEB_APP_URL}/`);
        } catch (error) {
            console.error('Error processing Instagram SSO:', error);
            res.status(500).json({ errors: [{ message: 'Something went wrong' }] });
        }
    };
    //** This function is used to verify the webhook callback url
    //** After adding the URL to the facebook dashboard, this function is called to verify it.
    @OpenAPI(
        "channels",
        "/instagram/webhook",
        "get",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    verifyInstagramWebhook = (req: Request, res: Response) => {
        const { query } = req;

        const value = this._service.verifyInstagramWebhook(query);

        res.send(value);
    };

    //** This function is used to process instagram events  */
    @OpenAPI(
        "channels",
        "/instagram/webhook",
        "post",
        undefined,
        undefined,
        undefined,
        "bearerAuth",
    )
    processInstagramEvents = async (req: Request, res: Response) => {
        const { body } = req;
        await this._service.processInstagramEvents(body);
        res.send({});
    };

    @OpenAPI(
        "channels",
        "/",
        "get",
        undefined,
        undefined,
        GetChannelResponseSchema,
        "bearerAuth",
    )
    getChannel = async (req: Request, res: Response) => {
        const { user } = req;

        const channel = await this._service.getChannel(user!);

        res.success(channel);
    };

    @OpenAPI(
        "channels",
        "/{channelId}",
        "patch",
        EditChannelSchema,
        [
            {
                in: "path",
                name: "channelId",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    updateChannel = async (req: Request, res: Response) => {
        const { body, user } = req;

        await this._service.updateChannel(body, user!);

        res.success({});
    };

    @OpenAPI(
        "channels",
        "/instagram/disconnect",
        "post",
        undefined,
        undefined,
        DisconnectInstagramResponseSchema,
        "bearerAuth",
    )
    disconnectInstagram = async (req: Request, res: Response) => {
        const { user } = req;

        const result = await this._service.disconnectInstagram(user!);

        res.success(result);
    };


}
