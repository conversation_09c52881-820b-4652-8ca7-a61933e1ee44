import { JSONSchemaType } from "ajv";
import { GetChannelResponse } from "../types";

export const GetChannelResponseSchema: JSONSchemaType<GetChannelResponse> = {
    type: "object",
    prefix: "channel_",
    properties: {
        id: {
            type: "number",
        },
        name: {
            type: "string",
        },
        username: {
            type: "string",
        },
        field: {
            type: "string",
            nullable: true,
        },
        workingHoursStart: {
            type: "string",
            nullable: true,
        },
        workingHoursEnd: {
            type: "string",
            nullable: true,
        },
        description: {
            type: "string",
        },
        exchangeRateType: {
            type: "string",
            enum: ["PERCENTAGE", "FIXED"],
        },
        exchangeRateValue: {
            type: "number",
        },
        createdAt: {
            type: "string",
            format: "date",
        },
        updatedAt: {
            type: "string",
            format: "date",
        },
    },
    required: [],
};
