// Note: Using 'any' type for schema due to complex nullable field handling

export const GetChannelResponseSchema: any = {
    type: "object",
    prefix: "channel_",
    properties: {
        id: {
            type: "number",
        },
        name: {
            type: "string",
        },
        username: {
            type: "string",
        },
        field: {
            type: "string",
            nullable: true,
        },
        workingHoursStart: {
            type: "string",
            nullable: true,
        },
        workingHoursEnd: {
            type: "string",
            nullable: true,
        },
        description: {
            type: "string",
        },
        exchangeRateType: {
            type: "string",
            enum: ["PERCENTAGE", "FIXED"],
        },
        exchangeRateValue: {
            type: "number",
        },
        createdAt: {
            type: "string",
            format: "date",
        },
        updatedAt: {
            type: "string",
            format: "date",
        },
        isDisconnected: {
            type: "boolean",
        },
    },
    required: [],
};
