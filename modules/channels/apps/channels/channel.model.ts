import { EntitySchema } from "typeorm";
import { Channel } from "./types";

// TODO: add username
export const ChannelSchema = new EntitySchema<Channel>({
    name: "channel",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        name: {
            type: String,
        },
        username: {
            type: String,
            nullable: true,
        },
        description: {
            type: String,
            nullable: true,
        },
        userId: {
            type: Number,
        },
        field: {
            type: String,
            nullable: true,
        },
        workingHoursStart: {
            type: String,
            nullable: true,
        },
        workingHoursEnd: {
            type: String,
            nullable: true,
        },
        exchangeRateType: {
            type: "enum",
            enum: ["PERCENTAGE", "FIXED"],
            default: "PERCENTAGE",
        },
        exchangeRateValue: {
            type: Number,
            default: 0,
        },
        accessToken: {
            type: String,
            nullable: true,
        },
        accessTokenExpiresAt: {
            type: Date,
            nullable: true,
        },
        platformId: {
            type: String,
            nullable: true,
        },
        chatbotEnabled: {
            type: Boolean,
            default: true,
        },
        isDisconnected: {
            type: Boolean,
            default: false,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "one-to-one",
            target: "user",
        },
    },
});
