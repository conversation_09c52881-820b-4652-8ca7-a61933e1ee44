import { container } from "tsyringe";
import ChannelController from "./channel.controller";
import { middlewares } from "../../../common";
import express from "express";
import * as schemas from "./schemas";

const { JWT } = middlewares;

const router = express.Router();

const controller = container.resolve(ChannelController);

router
    .route("/instagram/connect")
    .get(middlewares.JWT, controller.initInstagramSso);

router.route("/instagram/callback").get(controller.processInstagramSso);

router
    .route("/instagram/disconnect")
    .post(middlewares.JWT, controller.disconnectInstagram);

router
    .route("/instagram/webhook")
    .get(controller.verifyInstagramWebhook)
    .post(controller.processInstagramEvents);

router.route("/").get(JWT, controller.getChannel);

router
    .route("/:channelId")
    .patch(JWT, schemas.editChannel, controller.updateChannel);

export default router;
