import { IsNull, Not } from "typeorm";
import { layers } from "../../../common";
import { ChannelSchema } from "./channel.model";
import { Channel } from "./types";

export default class ChannelRepo extends layers.BaseTypeormRepository<Channel> {
    constructor() {
        super(ChannelSchema);
    }

    getChannelForUser = (userId: number) => {
        return this._repo
            .createQueryBuilder("channel")
            .where("channel.userId = :userId", { userId })
            .getRawOne();
    };

    getInstagramEnabledChannels = async () => {
        const channels = await this.findByQuery({ platformId: Not(IsNull()) });
        return channels;
    };
}
