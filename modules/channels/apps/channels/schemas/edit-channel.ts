import { JSONSchemaType } from "ajv";
import { EditChannelDto } from "../types";

export const EditChannelSchema: JSONSchemaType<EditChannelDto> = {
    $id: "edit-channel",
    type: "object",
    properties: {
        description: {
            type: "string",
            example: "beautiful cloths",
        },
        field: {
            type: "string",
            nullable: true,
            example: "cloths",
        },
        workingHoursStart: {
            type: "string",
            format: "time",
            nullable: true,
            example: "9:00am",
        },
        workingHoursEnd: {
            type: "string",
            format: "time",
            nullable: true,
            example: "9:00pm",
        },
        exchangeRateType: {
            type: "string",
            enum: ["PERCENTAGE", "FIXED"],
            example: "FIXED",
        },
        exchangeRateValue: {
            type: "number",
            example: 2,
        },
    },
    required: [],
    additionalProperties: false,
};
