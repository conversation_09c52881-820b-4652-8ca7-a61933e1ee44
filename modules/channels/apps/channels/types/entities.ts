import { User } from "../../../../users/apps/users/types";

export interface Channel {
    id: number;
    name: string;
    username: string;
    description: string;
    field?: string;
    user: User;
    userId: number;
    workingHoursStart?: string;
    workingHoursEnd?: string;
    exchangeRateType: "PERCENTAGE" | "FIXED";
    exchangeRateValue: number;
    accessToken: string;
    accessTokenExpiresAt: Date;
    platformId: string;
    chatbotEnabled: boolean;
    isDisconnected: boolean;
    createdAt: Date;
    updatedAt: Date;
}
