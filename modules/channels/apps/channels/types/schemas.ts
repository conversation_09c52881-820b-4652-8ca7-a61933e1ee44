import { Channel } from "./entities";

export type GetChannelResponse = Pick<
    Channel,
    | "id"
    | "name"
    | "username"
    | "workingHoursStart"
    | "workingHoursEnd"
    | "exchangeRateType"
    | "exchangeRateValue"
    | "description"
    | "field"
> & {
    createdAt: string;
    updatedAt: string;
    accessToken: string | null;
    accessTokenExpiresAt: string | null;
    platformId: string | null;
};
