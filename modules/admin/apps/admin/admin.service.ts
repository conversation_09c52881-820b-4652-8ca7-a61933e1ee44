import { injectable } from "tsyringe";
import AdminRepo from "./admin.repo";

import { auth, errors, utils } from "../../../common";
import { AddAdminDto, LoginAdminDto } from "./types";
import UsersService from "../../../users/apps/users/users.service";
import { comparePassword, encodePassword } from "../../../common/lib/auth";
import { getAdminSerializer } from "./responses";

import Logger from "../../../common/lib/metrics/logger";
import {
    adminAddLog,
    adminGetProfileLog,
    adminLoginLog,
    adminNotFoundErrorLog,
    emailAlreadyExistsErrorLog,
    recordErrorValue,
    usernameAlreadyExistsErrorLog,
} from "../../../common/lib/metrics/metrics";
import ChatsService from "../../../chat/apps/chat/chat.service";
import ClientService from "../../../users/apps/client/client.service";

@injectable()
export default class AdminsService {
    constructor(
        private _repo: AdminRepo,
        private _userService: UsersService,
        private _clientService: ClientService,
        private _chatService: ChatsService,
    ) {}

    getMessagesCount(userId: number, parsedQuery: Partial<Express.Query>) {
        return this._chatService.getMessagesCount(parsedQuery, userId);
    }

    getUserRegistrationReport(parsedQuery: Partial<Express.Query>) {
        return this._userService.getUserRegistrationReport(parsedQuery);
    }

    getClient(userId: number, parsedQuery: Partial<Express.Query>) {
        return this._clientService.getClientReport(parsedQuery, userId);
    }

    register = async (args: AddAdminDto) => {
        const { username, email, password, role } = args;

        const existingAdmin = await this._repo.findOneByQuery({ email });
        if (existingAdmin) {
            Logger.error("Admin registration failed", {
                email,
                action: "register",
                error: "EmailAlreadyExistError",
            });

            emailAlreadyExistsErrorLog.inc();
            recordErrorValue(
                "EmailAlreadyExistError",
                "Admin with this email already exists",
            );

            throw new errors.EmailAlreadyExistError("Admin");
        }

        const existingUsername = await this._repo.findOneByQuery({ username });
        if (existingUsername) {
            Logger.error("Admin registration failed", {
                email,
                action: "register",
                error: "EmailAlreadyExistError",
            });
            usernameAlreadyExistsErrorLog.inc();
            recordErrorValue(
                "UsernameAlreadyExistError",
                "Admin with this username already exists",
            );

            throw new errors.UsernameAlreadyExistError("Admin");
        }

        const hashedPassword = await encodePassword(password);
        await this._repo.create({
            username,
            email,
            password: hashedPassword,
            role,
        });

        adminAddLog.inc();

        Logger.info("Admin successfully registered", {
            email,
            action: "register",
        });
    };

    login = async (args: LoginAdminDto) => {
        const { username, password } = args;

        const admin = await this._repo.findOneByQuery({ username });

        if (utils.isNil(admin)) {
            Logger.warn("Admin login failed", {
                username,
                action: "login",
                error: "NotFoundError",
            });

            adminNotFoundErrorLog.inc();
            recordErrorValue("NotFoundError", "Admin not found");

            throw new errors.NotFoundError("Admin");
        }

        const passwordMatches = await comparePassword(admin.password, password);
        if (!passwordMatches) {
            recordErrorValue("UnauthorizedError", "Incorrect password");
            throw new errors.UnauthorizedError();
        }

        adminLoginLog.inc();

        Logger.info("Admin successfully logged in", {
            username,
            action: "login",
        });

        return {
            token: auth.signJWT({ id: admin.id, role: admin.role }),
        };
    };

    getUsers(user: Express.User, parsedQuery: Partial<Express.Query>) {
        return this._userService.getUsers(parsedQuery);
    }

    getAdminProfile = async (
        user: Express.User,
        parsedQuery: Partial<Express.Query>,
    ) => {
        const { id: userId } = user;

        const admin = await this._repo.getProfileForAdmin(userId, parsedQuery);
        if (utils.isNil(admin)) {
            Logger.error("Admin getProfile failed", {
                action: "getProfile",
                error: "NotFoundError",
            });

            adminNotFoundErrorLog.inc();
            recordErrorValue("NotFoundError", "Admin profile not found");

            throw new errors.NotFoundError("Admin");
        }

        adminGetProfileLog.inc();

        Logger.info("Admin Profile successfully retrieved", {
            admin,
            action: "register",
        });

        return getAdminSerializer(admin);
    };
}
