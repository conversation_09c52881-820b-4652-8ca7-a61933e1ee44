import { layers } from "../../../common";
import { Admin } from "./types";

import { AdminSchema } from "./admin.model";

export default class AdminRepo extends layers.BaseTypeormRepository<Admin> {
    relations = [];
    constructor() {
        super(AdminSchema);
    }

    getProfileForAdmin = (id: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("admin")
            .where("admin.id = :id", { id })
            .filter(query.filter)
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawOne();
    };
}
