import { JSONSchemaType } from "ajv";
import { AddAdminDto } from "../types";

export const AddAdminSchema: JSONSchemaType<AddAdminDto> = {
    $id: "add-admin",
    type: "object",
    properties: {
        username: { type: "string", example: "admin" },
        password: { type: "string", example: "123456789" },
        email: { type: "string", example: "<EMAIL>" },
        role: { type: "string", example: "admin" },
    },
    required: ["username", "password", "email"],
    additionalProperties: false,
};
