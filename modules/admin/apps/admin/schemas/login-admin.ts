import { JSONSchemaType } from "ajv";
import { LoginAdminDto } from "../types";

export const LoginAdminSchema: JSONSchemaType<LoginAdminDto> = {
    $id: "login-admin",
    type: "object",
    properties: {
        username: { type: "string", example: "admin" },
        password: { type: "string", example: "123456789" },
    },
    required: ["username", "password"],
    additionalProperties: false,
};
