import { EntitySchema } from "typeorm";
import { Admin } from "./types";

export const AdminSchema = new EntitySchema<Admin>({
    name: "admin",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        username: {
            type: String,
            unique: true,
        },
        password: {
            type: String,
        },
        email: {
            type: String,
            unique: true,
        },
        role: {
            type: String,
            default: "admin",
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {},
});
