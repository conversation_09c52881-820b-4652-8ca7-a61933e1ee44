import { injectable } from "tsyringe";
import { Request, Response } from "express";

import AdminsService from "./admin.service";
import { OpenAPI } from "../../../common/lib/decorators";
import { AddAdminSchema, LoginAdminSchema } from "./schemas";
import { OtpResponseSchema } from "../../../users/apps/users/responses";
import { GetAdminResponseSchema } from "./responses";
import { GetUsersProfileResponseSchema } from "../../../users/apps/users/responses/get-users";

@injectable()
export default class AdminController {
    constructor(private _service: AdminsService) {}

    @OpenAPI(
        "admins",
        "/",
        "post",
        AddAdminSchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addAdmin = async (req: Request, res: Response) => {
        const { body } = req;
        await this._service.register(body);
        res.success({});
    };

    @OpenAPI(
        "admins",
        "/login",
        "post",
        LoginAdminSchema,
        undefined,
        OtpResponseSchema,
        "bearerAuth",
    )
    login = async (req: Request, res: Response) => {
        const { body } = req;
        const result = await this._service.login(body);
        res.success(result);
    };

    @OpenAPI(
        "admins",
        "/users",
        "get",
        undefined,
        undefined,
        GetUsersProfileResponseSchema,
        "bearerAuth",
    )
    getUsers = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;
        const result = await this._service.getUsers(user!, parsedQuery);
        res.success(result);
    };

    @OpenAPI(
        "admins",
        "/profile",
        "get",
        undefined,
        undefined,
        GetAdminResponseSchema,
        "bearerAuth",
    )
    getAdminProfile = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;
        const profile = await this._service.getAdminProfile(user!, parsedQuery);
        res.success(profile);
    };

    @OpenAPI(
        "admins",
        "/users-registration",
        "get",
        undefined,
        [
            {
                in: "query",
                name: "from",
                schema: {
                    type: "string",
                    example: "2020-10-31T01:30:00.000Z",
                },
            },
            {
                in: "query",
                name: "to",
                schema: {
                    type: "string",
                    example: "2030-10-31T01:30:00.000Z",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    getUserRegistrations = async (req: Request, res: Response) => {
        const { parsedQuery } = req;

        const clients =
            await this._service.getUserRegistrationReport(parsedQuery);

        res.success(clients);
    };

    @OpenAPI(
        "admins",
        "/{userId}/clients",
        "get",
        undefined,
        [
            {
                in: "path",
                name: "userId",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    getClient = async (req: Request, res: Response) => {
        const {
            params: { userId },
            parsedQuery,
        } = req;

        const profile = await this._service.getClient(
            Number(userId),
            parsedQuery,
        );
        res.success(profile);
    };

    @OpenAPI(
        "admins",
        "/{userId}/messages",
        "get",
        undefined,
        [
            {
                in: "path",
                name: "userId",
                schema: { type: "number", example: "1" },
            },
        ],
        undefined,
        "bearerAuth",
    )
    getMessagesCount = async (req: Request, res: Response) => {
        const {
            params: { userId },
            parsedQuery,
        } = req;

        const count = await this._service.getMessagesCount(
            Number(userId),
            parsedQuery,
        );
        res.success(count);
    };
}
