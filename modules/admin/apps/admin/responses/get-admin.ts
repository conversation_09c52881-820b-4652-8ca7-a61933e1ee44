import { JSONSchemaType } from "ajv";
import { GetAdminResponse } from "../types";

export const GetAdminResponseSchema: JSONSchemaType<GetAdminResponse> = {
    type: "object",
    prefix: "admin_",
    properties: {
        id: { type: "number" },
        username: { type: "string" },
        password: { type: "string" },
        email: { type: "string" },
        role: { type: "string" },
        createdAt: { type: "string" },
        updatedAt: { type: "string" },
    },
    required: [
        "id",
        "username",
        "password",
        "email",
        "role",
        "createdAt",
        "updatedAt",
    ],
};
