import express from "express";
import { container } from "tsyringe";
import AdminController from "./admin.controller";

import * as schemas from "./schemas";
import { JWT } from "../../../common/lib/middlewares/jwt";
import { checkRole } from "../../../common/lib/middlewares";

const router = express.Router();

const controller = container.resolve(AdminController);

router
    .route("/profile")
    .get(JWT, checkRole(["admin"]), controller.getAdminProfile);

router.route("/login").post(schemas.loginAdmin, controller.login);

router.route("/users").get(JWT, checkRole(["admin"]), controller.getUsers);

router
    .route("/:userId/messages")
    .get(JWT, checkRole(["admin"]), controller.getMessagesCount);

router
    .route("/:userId/clients")
    .get(JWT, checkRole(["admin"]), controller.getClient);

router
    .route("/users-registration")
    .get(JWT, checkRole(["admin"]), controller.getUserRegistrations);

export default router;
