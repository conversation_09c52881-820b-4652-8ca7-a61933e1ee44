// // yarn run add-admin -- admin 123456789 <EMAIL> admin

// import "reflect-metadata";
// // import { config } from "dotenv";
// // config({ path: "/etc/palette/api/.env" });

// import { container } from "tsyringe";
// import AdminsService from "../admin/admin.service";
// import { errors, typeorm } from "../../../common";

// const adminService = container.resolve(AdminsService);

// export async function addAdminCLI(
//     username: string,
//     password: string,
//     email: string,
//     role: string,
// ) {
//     if (!username || !password || !email || !role) {
//         throw new errors.MissingArgumentsError();
//     }

//     await adminService.register({ username, password, email, role });

//     console.log("Admin added successfully");
// }

// async function run() {
//     await typeorm.AppDataSource.getInstance().initialize();

//     const [username, password, email, role] = process.argv.slice(2);

//     if (!username || !password || !email || !role) {
//         throw new errors.MissingArgumentsError();
//     }

//     await adminService.register({ username, password, email, role });
// }

// run()
//     .then(() => {
//         console.log("Done");
//         return process.exit(0);
//     })
//     .catch((err) => {
//         console.error(err);
//         return process.exit(1);
//     })
//     .finally(async () => {
//         await typeorm.AppDataSource.getInstance().destroy();
//         process.exit(0);
//     });
