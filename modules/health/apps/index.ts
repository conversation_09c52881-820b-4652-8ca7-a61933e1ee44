import express from "express";

// Using a direct import path
const router = express.Router();

// Import the controller directly
import { container } from "tsyringe";
import HealthController from "./health/health.controller";

const controller = container.resolve(HealthController);

// Define routes directly here
router.route("/check").get(controller.checkHealth);
router.route("/meta").get(controller.checkMetaHealth);
router.route("/ai").get(controller.checkAIHealth);
router.route("/google").get(controller.checkGoogleHealth);

export default router;
