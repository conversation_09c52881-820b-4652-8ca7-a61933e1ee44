import { AnySchema } from "ajv";
import { SecurityType, HTTPMethods, Parameter } from "../../../common/lib/decorators/openapi";

// Custom OpenAPI decorator for health check routes
export function HealthOpenAPI(
    tag: string,
    path: string,
    method: HTTPMethods,
    schema?: AnySchema,
    parameters?: Parameter[],
    response?: AnySchema,
    security?: SecurityType,
) {
    // Add a custom property to indicate this is a health check route
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return function (target: any, operationId: string) {
        if (!Reflect.hasMetadata("routes", target.constructor)) {
            Reflect.defineMetadata(
                "routes",
                [] as Array<any>,
                target.constructor,
            );
        }
        Reflect.defineMetadata("__controller__", true, target.constructor);

        const routes = Reflect.getMetadata(
            "routes",
            target.constructor,
        ) as Array<any>;

        routes.push({
            path,
            method,
            operationId,
            tag,
            parameters,
            schema,
            response,
            security,
            // Add a custom property to identify health check routes
            isHealthCheck: true,
        });

        Reflect.defineMetadata("routes", routes, target.constructor);
    };
}
