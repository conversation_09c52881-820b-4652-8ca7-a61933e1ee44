import { injectable } from "tsyringe";
import Logger from "../../../common/lib/metrics/logger";

// HTTP Status codes
enum HttpStatus {
    OK = 200,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    INTERNAL_SERVER_ERROR = 500,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503,
    GATEWAY_TIMEOUT = 504,
}



@injectable()
export default class HealthService {
    constructor() {}

    /**
     * Check general server health
     */
    checkServerHealth = async () => {
        try {
            // Check if server is running
            return {
                status: "ok",
                message: "Server is running",
                timestamp: new Date().toISOString(),
            };
        } catch (error) {
            Logger.error("Server health check failed", {
                action: "checkServerHealth",
                error: error instanceof Error ? error.message : String(error),
            });
            return {
                status: "error",
                message: "Server health check failed",
                error: error instanceof Error ? error.message : String(error),
                timestamp: new Date().toISOString(),
            };
        }
    };

    /**
     * Check Meta API connectivity
     */
    checkMetaHealth = async () => {
        try {
            // Try to connect to Meta Graph API - only checking network connectivity
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            try {
                await fetch("https://graph.facebook.com/v20.0/", {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    signal: controller.signal,
                });

                clearTimeout(timeoutId);

                // If we get here, network connectivity is good regardless of HTTP status
                return {
                    statusCode: HttpStatus.OK,
                    status: "ok",
                    message: "Meta API is accessible",
                    timestamp: new Date().toISOString(),
                };
            } finally {
                clearTimeout(timeoutId);
            }
        } catch (error) {
            // Determine error type for more detailed logging
            let errorType = "unknown";
            let errorMessage = error instanceof Error ? error.message : String(error);
            let statusCode = HttpStatus.BAD_GATEWAY;

            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    errorType = "timeout";
                    statusCode = HttpStatus.GATEWAY_TIMEOUT;
                } else if (errorMessage.includes('ECONNREFUSED')) {
                    errorType = "connection_refused";
                    statusCode = HttpStatus.BAD_GATEWAY;
                } else if (errorMessage.includes('ENOTFOUND')) {
                    errorType = "no_response";
                }
            }

            Logger.error("Meta API health check failed", {
                action: "checkMetaHealth",
                error: errorMessage,
                errorType: errorType
            });

            return {
                status: "error",
                message: "Meta API is not accessible",
                error: errorMessage,
                errorType: errorType,
                timestamp: new Date().toISOString(),
            };
        }
    };

    /**
     * Check OpenAI API connectivity
     */
    checkAIHealth = async () => {
        try {
            // Try to connect to OpenAI API and get the response
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            try {
                const response = await fetch("https://api.openai.com/v1/", {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    signal: controller.signal,
                });

                clearTimeout(timeoutId);

                // Get the response data
                const responseText = await response.text();
                let responseData;

                try {
                    // Try to parse as JSON
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    // Not JSON, might be HTML
                    responseData = null;
                }

                // Check for the specific "unsupported_country_region_territory" error
                if (
                    responseData &&
                    responseData.error &&
                    responseData.error.code === "unsupported_country_region_territory"
                ) {
                    Logger.error(
                        "OpenAI API not accessible due to country/region restriction",
                        {
                            action: "checkAIHealth",
                            error: "unsupported_country_region_territory",
                            errorType: "region_restricted",
                            statusCode: HttpStatus.BAD_GATEWAY,
                        },
                    );

                    return {
                        statusCode: HttpStatus.BAD_GATEWAY,
                        status: "error",
                        message:
                            "OpenAI API is not accessible due to country/region restriction",
                        error: "Country, region, or territory not supported",
                        errorType: "region_restricted",
                        timestamp: new Date().toISOString(),
                    };
                }

                // Check for HTTP protocol error (should be HTTPS)
                if (
                    responseData &&
                    responseData.error &&
                    responseData.error.code === "http_unsupported"
                ) {
                    Logger.error("OpenAI API protocol error - HTTPS required", {
                        action: "checkAIHealth",
                        error: "http_unsupported",
                        errorType: "protocol_error",
                        statusCode: HttpStatus.BAD_GATEWAY,
                    });

                    return {
                        statusCode: HttpStatus.BAD_GATEWAY,
                        status: "error",
                        message:
                            "OpenAI API is not accessible - HTTPS protocol required",
                        error: "The OpenAI API is only accessible over HTTPS",
                        errorType: "protocol_error",
                        timestamp: new Date().toISOString(),
                    };
                }

                // Check for any other error response with status code
                if (response.status >= 400) {
                    const errorMessage =
                        responseData?.error?.message || "Unknown error";
                    const errorCode = responseData?.error?.code || "unknown_error";

                    Logger.error(
                        `OpenAI API returned error status: ${response.status}`,
                        {
                            action: "checkAIHealth",
                            error: errorMessage,
                            errorCode: errorCode,
                            errorType: "api_error",
                            statusCode: HttpStatus.BAD_GATEWAY,
                        },
                    );

                    return {
                        statusCode: HttpStatus.BAD_GATEWAY,
                        status: "error",
                        message: `OpenAI API is not accessible - received ${response.status} error`,
                        error: errorMessage,
                        errorType: "api_error",
                        timestamp: new Date().toISOString(),
                    };
                }

                // Check if we got HTML response instead of expected API response
                if (
                    responseText.includes("<!DOCTYPE html>") ||
                    responseText.includes("<html")
                ) {
                    Logger.error(
                        "OpenAI API returned HTML instead of JSON - likely region restriction or network issue",
                        {
                            action: "checkAIHealth",
                            error: "html_response_received",
                            errorType: "region_restricted",
                            statusCode: HttpStatus.BAD_GATEWAY,
                        },
                    );

                    return {
                        statusCode: HttpStatus.BAD_GATEWAY,
                        status: "error",
                        message:
                            "OpenAI API is not accessible - region restriction or network issue detected",
                        error: "Received HTML response instead of API access",
                        errorType: "region_restricted",
                        timestamp: new Date().toISOString(),
                    };
                }

                // If we get here, network connectivity is good
                return {
                    statusCode: HttpStatus.OK,
                    status: "ok",
                    message: "OpenAI API is accessible",
                    timestamp: new Date().toISOString(),
                };
            } finally {
                clearTimeout(timeoutId);
            }
        } catch (error) {
            // Handle network errors
            let errorType = "network_error";
            let errorMessage =
                error instanceof Error ? error.message : String(error);
            let statusCode = HttpStatus.BAD_GATEWAY;

            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    errorType = "timeout";
                    statusCode = HttpStatus.GATEWAY_TIMEOUT;
                } else if (errorMessage.includes('ECONNREFUSED')) {
                    errorType = "connection_refused";
                    statusCode = HttpStatus.BAD_GATEWAY;
                } else if (errorMessage.includes('ENOTFOUND')) {
                    errorType = "no_response";
                }
            }

            Logger.error("OpenAI API health check failed", {
                action: "checkAIHealth",
                error: errorMessage,
                errorType: errorType
            });

            return {
                status: "error",
                message: "OpenAI API is not accessible",
                error: errorMessage,
                errorType: errorType,
                timestamp: new Date().toISOString(),
            };
        }
    };

    /**
     * Check Google API connectivity
     */
    checkGoogleHealth = async () => {
        try {
            // Try to connect to Google API - only checking network connectivity
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            try {
                await fetch("https://www.googleapis.com/", {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    signal: controller.signal,
                });

                clearTimeout(timeoutId);

                // If we get here, network connectivity is good regardless of HTTP status
                return {
                    statusCode: HttpStatus.OK,
                    status: "ok",
                    message: "Google API is accessible",
                    timestamp: new Date().toISOString(),
                };
            } finally {
                clearTimeout(timeoutId);
            }
        } catch (error) {
            // Determine error type for more detailed logging
            let errorType = "unknown";
            let errorMessage = error instanceof Error ? error.message : String(error);
            let statusCode = HttpStatus.BAD_GATEWAY;

            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    errorType = "timeout";
                    statusCode = HttpStatus.GATEWAY_TIMEOUT;
                } else if (errorMessage.includes('ECONNREFUSED')) {
                    errorType = "connection_refused";
                    statusCode = HttpStatus.BAD_GATEWAY;
                } else if (errorMessage.includes('ENOTFOUND')) {
                    errorType = "no_response";
                }
            }

            Logger.error("Google API health check failed", {
                action: "checkGoogleHealth",
                error: errorMessage,
                errorType: errorType
            });

            return {
                status: "error",
                message: "Google API is not accessible",
                error: errorMessage,
                errorType: errorType,
                timestamp: new Date().toISOString(),
            };
        }
    };
}
