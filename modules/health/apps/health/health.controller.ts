import { Request, Response } from "express";
import { injectable } from "tsyringe";
import { HealthOpenAPI } from "./health-openapi";
import HealthService from "./health.service";

@injectable()
export default class HealthController {
    constructor(private _service: HealthService) {}

    @HealthOpenAPI(
        "health",
        "/check",
        "get",
        undefined,
        undefined,
        undefined,
        undefined,
    )
    checkHealth = async (_req: Request, res: Response) => {
        const result = await this._service.checkServerHealth();
        res.success(result);
    };

    @HealthOpenAPI(
        "health",
        "/meta",
        "get",
        undefined,
        undefined,
        undefined,
        undefined,
    )
    checkMetaHealth = async (_req: Request, res: Response) => {
        const result = await this._service.checkMetaHealth();
        res.success(result);
    };

    @HealthOpenAPI(
        "health",
        "/ai",
        "get",
        undefined,
        undefined,
        undefined,
        undefined,
    )
    checkAIHealth = async (_req: Request, res: Response) => {
        const result = await this._service.checkAIHealth();
        res.success(result);
    };

    @HealthOpenAPI(
        "health",
        "/google",
        "get",
        undefined,
        undefined,
        undefined,
        undefined,
    )
    checkGoogleHealth = async (_req: Request, res: Response) => {
        const result = await this._service.checkGoogleHealth();
        res.success(result);
    };
}
