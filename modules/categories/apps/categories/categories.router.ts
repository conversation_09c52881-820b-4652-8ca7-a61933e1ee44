import express from "express";
import { container } from "tsyringe";
import { middlewares } from "../../../common";
import CategoryController from "./categories.controller";
import * as schemas from "./schemas";

const { JWT } = middlewares;

const router = express.Router();

const controller = container.resolve(CategoryController);

router
    .route("/")
    .post(JWT, schemas.addCategory, controller.addCategory)
    .get(JWT, controller.getCategories);
router
    .route("/:id")
    .patch(JWT, schemas.editCategory, controller.editCategory)
    .delete(JW<PERSON>, controller.deleteCategory);

export default router;
