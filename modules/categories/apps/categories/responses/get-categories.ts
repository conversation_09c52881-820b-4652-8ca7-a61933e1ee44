import { JSONSchemaType } from "ajv";
import { GetCategoriesResponse } from "../types";

export const GetCategoriesResponseSchema: JSONSchemaType<
    GetCategoriesResponse[]
> = {
    type: "array",
    items: {
        type: "object",
        prefix: "category_",
        properties: {
            id: {
                type: "number",
            },
            name: {
                type: "string",
            },
            parent: {
                type: "object",
                prefix: "parent_",
                properties: {
                    id: {
                        type: "number",
                    },
                    name: {
                        type: "string",
                    },
                },
                required: [],
            },
            createdAt: {
                type: "string",
            },
            updatedAt: {
                type: "string",
            },
        },
        required: [],
    },
};
