import { injectable } from "tsyringe";
import CategoryRepo from "./categories.repo";

import { AddCategoryDto, EditCategoryDto } from "./types";

import { errors, utils } from "../../../common";
import { getCategoriesSerializer } from "./responses";
import Logger from "../../../common/lib/metrics/logger";
import {
    categoryNotFoundErrorLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";

@injectable()
export default class CategoryService {
    constructor(private _repo: CategoryRepo) {}

    getCategoryOfUser = async (id: number, userId: number) => {
        const category = await this._repo.findOneByQuery({
            userId,
            id,
        });

        if (utils.isNil(category)) {
            categoryNotFoundErrorLog.inc();
            Logger.error("not found error", {
                error: "Category not found",
                statusCode: 404,
            });

            recordErrorValue("not found error", "Category not found");
            throw new errors.NotFoundError("Category");
        }
        return category;
    };

    addCategory = async (body: AddCategoryDto, user: Express.User) => {
        const { parentId } = body;
        const { id: userId } = user;

        if (utils.isNotNil(parentId)) {
            await this.getCategoryOfUser(parentId, userId);
        }

        await this._repo.create({
            ...body,
            userId,
        });
    };

    editCategory = async (
        body: EditCategoryDto,
        user: Express.User,
        id: number,
    ) => {
        const { parentId } = body;
        const { id: userId } = user;

        if (utils.isNotNil(parentId)) {
            await this.getCategoryOfUser(parentId, userId);
        }

        const isUpdated = await this._repo.updateOneByQuery(
            { id, userId },
            body,
        );

        if (!isUpdated) {
            throw new errors.NotFoundError("Category");
        }
    };

    getCategories = async (
        parsedQuery: Partial<Express.Query>,
        user: Express.User,
    ) => {
        const { id: userId } = user;

        const categories = await this._repo.getCategoriesForUser(
            userId,
            parsedQuery,
        );

        return getCategoriesSerializer(categories);
    };

    deleteCategory = async (id: number, user: Express.User) => {
        const { id: userId } = user;

        const isDeleted = await this._repo.deleteOneByQuery({
            userId,
            id,
        });

        if (!isDeleted) {
            throw new errors.NotFoundError("Category");
        }
    };
}
