import { injectable } from "tsyringe";
import { Request, Response } from "express";

import CategoryService from "./categories.service";
import { OpenAPI } from "../../../common/lib/decorators";

import { AddCategorySchema, EditCategorySchema } from "./schemas";
import { GetCategoriesResponseSchema } from "./responses";

@injectable()
export default class CategoryController {
    constructor(private _service: CategoryService) {}

    @OpenAPI(
        "categories",
        "/",
        "post",
        AddCategorySchema,
        undefined,
        undefined,
        "bearerAuth",
    )
    addCategory = async (req: Request, res: Response) => {
        const { body, user } = req;
        await this._service.addCategory(body, user!);
        res.success({});
    };

    @OpenAPI(
        "categories",
        "/",
        "get",
        undefined,
        undefined,
        GetCategoriesResponseSchema,
        "bearerAuth",
    )
    getCategories = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const categories = await this._service.getCategories(
            parsedQuery,
            user!,
        );
        res.success(categories);
    };

    @OpenAPI(
        "categories",
        "/{id}",
        "delete",
        undefined,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    deleteCategory = async (req: Request, res: Response) => {
        const {
            user,
            params: { id },
        } = req;
        await this._service.deleteCategory(parseInt(id), user!);
        res.success({});
    };

    @OpenAPI(
        "categories",
        "/{id}",
        "patch",
        EditCategorySchema,
        [
            {
                in: "path",
                name: "id",
                schema: {
                    type: "number",
                    example: "1",
                },
            },
        ],
        undefined,
        "bearerAuth",
    )
    editCategory = async (req: Request, res: Response) => {
        const {
            body,
            user,
            params: { id },
        } = req;
        await this._service.editCategory(body, user!, Number(id));
        res.success({});
    };
}
