import { layers } from "../../../common";
import { Category } from "./types";

import { CategorySchema } from "./categories.model";

export default class CategoryRepo extends layers.BaseTypeormRepository<Category> {
    relations = [];
    constructor() {
        super(CategorySchema);
    }

    getCategoriesForUser = (userId: number, query: Partial<Express.Query>) => {
        return this._repo
            .createQueryBuilder("category")
            .where("category.userId = :userId", { userId })
            .filter(query.filter)
            .leftJoinAndSelect(
                "category",
                "parent",
                "category.parentId = parent.id",
            )
            .search(query.search, query.searchField, ...this.relations)
            .sort(query.sort, ...this.relations)
            .paginate(query.page, query.pageSize)
            .getRawManyAndCount();
    };
}
