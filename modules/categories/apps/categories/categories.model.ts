import { EntitySchema } from "typeorm";
import { Category } from "./types";

export const CategorySchema = new EntitySchema<Category>({
    name: "category",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        name: {
            type: String,
        },
        userId: {
            type: Number,
            nullable: true,
        },
        parentId: {
            type: Number,
            nullable: true,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    indices: [
        {
            name: "IDX_UNIQUE_CATEGORY_NAME",
            unique: true,
            columns: ["name", "userId"],
        },
    ],
    relations: {
        parent: {
            type: "many-to-one",
            target: "category",
            nullable: true,
        },
        user: {
            type: "many-to-one",
            target: "user",
            nullable: true,
        },
    },
});
