import { JSONSchemaType } from "ajv";
import { AddCategoryDto } from "../types";

export const AddCategorySchema: JSONSchemaType<AddCategoryDto> = {
    $id: "add-category",
    type: "object",
    properties: {
        name: {
            type: "string",
            example: "furniture",
        },
        parentId: {
            type: "number",
            example: 1,
        },
    },
    required: ["name"],
    additionalProperties: false,
};
