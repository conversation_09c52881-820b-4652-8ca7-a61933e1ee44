{"openapi": "3.0.2", "info": {"title": "Palette API", "description": "Palette API", "license": {"name": "UNLICENSED"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000/api/v1"}, {"url": "https://automatic-space-winner-r4g54q5wvw72px7-3000.app.github.dev/api/v1"}, {"url": "https://api.palette-tech.io/api/v1"}, {"url": "https://backend.palette-tech.io/api/v1"}], "tags": [], "paths": {}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}