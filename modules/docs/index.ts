import express, { NextFunction, Request, Response } from "express";
import * as swaggerUi from "swagger-ui-express";

import { initSwagger } from "./init";

const router = express.Router();

let swaggerGenerated = false;
let swaggerDocument = {};

router.use(
    "/",
    swaggerUi.serve,
    (req: Request, res: Response, next: NextFunction) => {
        if (!swaggerGenerated) {
            swaggerDocument = initSwagger();
            swaggerGenerated = true;
        }
        return swaggerUi.setup(swaggerDocument)(req, res, next);
    },
);

export default router;
