import express from "express";
import { container } from "tsyringe";
import <PERSON><PERSON><PERSON><PERSON>roller from "./faq.controller";
import * as schemas from "./schemas";
import { middlewares } from "../../../common";

const router = express.Router();

const controller = container.resolve(FAQController);

router
    .route("/")
    .get(middlewares.JWT, controller.getFAQs)
    .post(middlewares.JWT, schemas.addFAQ, controller.addFAQ);

router
    .route("/:id")
    .get(middlewares.JWT, controller.getFAQ)
    .put(middlewares.JWT, schemas.editFAQ, controller.editFAQ)
    .delete(middlewares.JWT, controller.deleteFAQ);

export default router;
