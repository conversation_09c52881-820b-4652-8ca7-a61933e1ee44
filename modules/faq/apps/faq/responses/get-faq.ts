import { JSONSchemaType } from "ajv";
import { FAQResponse } from "../types";

export const GetFAQResponseSchema: JSONSchemaType<FAQResponse> = {
    type: "object",
    prefix: "faq_",
    properties: {
        id: { type: "number" },
        question: { type: "string" },
        answer: { type: "string" },
        status: { type: "boolean" },
        order: { type: "number" },
    },
    required: [],
};
