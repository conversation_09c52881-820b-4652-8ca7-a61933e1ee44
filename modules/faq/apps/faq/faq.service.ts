import { inject, injectable, registry } from "tsyringe";
import FAQRepo from "./faq.repo";
import { errors } from "../../../common";
import { AddFAQDto, EditFAQDto } from "./types";
import { getFaqsPaginatedSerializer } from "./responses";
import Logger from "../../../common/lib/metrics/logger";
import {
    faqAddLog,
    faqDeleteLog,
    faqEditLog,
    faqGetLog,
    recordErrorValue,
} from "../../../common/lib/metrics/metrics";
import { BaseVectorStore } from "../../../common/lib/chatbot/vector-stores/base-vector-store";
import { registries } from "./registries";
import { VECTOR_TOKEN_NAME } from "../../../common/base/types/typing";

@registry(registries)
@injectable()
export default class FAQService {
    constructor(
        private _repo: FAQRepo,
        @inject(VECTOR_TOKEN_NAME.FAQ_VECTOR_STORE)
        private _vectorStore: BaseVectorStore,
    ) {}

    addFAQ = async (args: AddFAQDto, profile: Express.User): Promise<void> => {
        const { id: userId } = profile;

        const description = args.question + "\n" + args.answer;

        await this._repo.runTransaction(async (manager) => {
            const createdFaq = await this._repo.create(
                { ...args, userId },
                { manager },
            );
            await this._vectorStore.index([
                {
                    pageContent: description,
                    metadata: { userId: createdFaq.userId },
                    id: createdFaq.id.toString(),
                },
            ]);
        });

        faqAddLog.inc();
        Logger.info("FAQ successfully added", { userId });
    };

    getFAQs = async (profile: Express.User) => {
        const { id: userId } = profile;

        const faqs = await this._repo.getFAQs(userId);

        faqGetLog.inc();
        Logger.info("FAQs fetched successfully", { userId });

        return getFaqsPaginatedSerializer(faqs);
    };

    getFAQ = async (id: number, profile: Express.User) => {
        const { id: userId } = profile;

        const faq = await this._repo.getFAQ(id, userId);

        faqGetLog.inc();
        Logger.info("FAQ fetched successfully", {
            faqId: id,
            userId,
        });

        return faq;
    };

    editFAQ = async (
        args: EditFAQDto,
        id: number,
        profile: Express.User,
    ): Promise<void> => {
        const { id: userId } = profile;

        const description = args.question + "\n" + args.answer;

        await this._repo.runTransaction(async (manager) => {
            const isUpdated = await this._repo.updateOneByQuery(
                { id, userId },
                args,
                { manager },
            );

            if (!isUpdated) {
                Logger.error("Error editing FAQ", {
                    faqId: id,
                    action: "editFAQ",
                });
                recordErrorValue("FAQEditError", "BadRequestError");

                throw new errors.BadRequestError();
            }

            await this._vectorStore.index([
                {
                    pageContent: description,
                    metadata: { userId },
                    id: id.toString(),
                },
            ]);
        });

        faqEditLog.inc();
        Logger.info("FAQ edited successfully", { faqId: id });
    };

    deleteFAQ = async (id: number, profile: Express.User): Promise<void> => {
        const { id: userId } = profile;

        const isDeleted = await this._repo.deleteOneByQuery({ id, userId });
        if (!isDeleted) {
            Logger.error("Error deleting FAQ", {
                faqId: id,
                action: "deleteFAQ",
            });
            recordErrorValue("FAQDeleteError", "BadRequestError");

            throw new errors.BadRequestError();
        }

        faqDeleteLog.inc();
        Logger.info("FAQ deleted successfully", { faqId: id });
    };
}
