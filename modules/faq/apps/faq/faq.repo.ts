import { layers } from "../../../common";
import { FAQSchema } from "./faq.model";
import { FAQ } from "./types";

export default class FaqRepo extends layers.BaseTypeormRepository<FAQ> {
    relations = [];
    constructor() {
        super(FAQSchema);
    }

    getFAQs(userId: number) {
        return this.createQueryBuilder("faq")
            .where("faq.userId = :userId", { userId })
            .orderBy("faq.order", "ASC")
            .getRawManyAndCount();
    }

    getFAQ(faqId: number, userId: number) {
        return this.createQueryBuilder("faq")
            .where("faq.id = :faqId", { faqId })
            .andWhere("faq.userId = :userId", { userId })
            .getOne();
    }
}
