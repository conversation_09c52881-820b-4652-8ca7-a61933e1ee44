import { EntitySchema } from "typeorm";
import { FAQ } from "./types";

export const FAQSchema = new EntitySchema<FAQ>({
    name: "faq",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        userId: {
            type: Number,
        },
        question: {
            type: "text",
        },
        answer: {
            type: "text",
        },
        status: {
            type: Boolean,
            default: true,
        },
        order: {
            type: Number,
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
    },
    relations: {
        user: {
            type: "many-to-one",
            target: "user",
        },
    },
});
