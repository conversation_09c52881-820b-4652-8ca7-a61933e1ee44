import { JSONSchemaType } from "ajv";
import { AddFAQDto } from "../types";

export const AddFAQSchema: JSONSchemaType<AddFAQDto> = {
    $id: "add-faq",
    type: "object",
    properties: {
        question: { type: "string", example: "question 1" },
        answer: { type: "string", example: "answer 1" },
        status: { type: "boolean", example: true },
        order: { type: "number", example: 1 },
    },
    required: ["question", "answer", "status", "order"],
    additionalProperties: false,
};
