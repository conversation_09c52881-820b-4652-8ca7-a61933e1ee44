import { JSONSchemaType } from "ajv";
import { EditFAQDto } from "../types";

export const EditFAQSchema: JSONSchemaType<EditFAQDto> = {
    $id: "edit-faq",
    type: "object",
    properties: {
        question: { type: "string", example: "question 1" },
        answer: { type: "string", example: "answer 1" },
        status: { type: "boolean", example: true },
        order: { type: "number", example: 1 },
    },
    required: [],
    additionalProperties: false,
};
