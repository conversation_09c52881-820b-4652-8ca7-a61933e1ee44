import {
    VECTOR_INDEX_NAME,
    VECTOR_STORE_PROVIDER,
    VECTOR_TOKEN_NAME,
    EMBEDDING_MODEL
} from "../../../../common/base/types/typing";
import {
    embeddingFactory,
} from "../../../../common/lib/chatbot/llm-factory";
import { vectorStoreFactory } from "../../../../common/lib/chatbot/vector-stores/vector-stores-factory";

export const registries = [
    {
        token: VECTOR_TOKEN_NAME.FAQ_VECTOR_STORE,
        useValue: vectorStoreFactory(
            VECTOR_STORE_PROVIDER.ELASTIC_SEARCH,
            embeddingFactory(process.env.EMBEDDING as EMBEDDING_MODEL),
            {
                index: VECTOR_INDEX_NAME.FAQS,
            },
        ),
    },
];
