1.
```bash
git clone https://github.com/palettebusiness/palette-backend
```

2. pull and run related dockers
```bash
docker-compose -f docker-compose.local.yml up -d
```
3.
```bash
yarn install
```

4.
```bash
yarn build
```

5.
```bash
yarn migration:run
```

6.
```bash
yarn run setup-pg-memory
```


7.
```bash
yarn dev
```


# Palette API

## Table of Contents

1. [Getting Started](#getting-started)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Running the Project](#running-the-project)

## Getting Started

These instructions will help you set up and run the project on your local machine for development and testing purposes.

## Installation

First, ensure you have [Node.js](https://nodejs.org/) and [Yarn](https://yarnpkg.com/) installed on your machine.

### Node.js

It's recommended to use [nvm](https://github.com/nvm-sh/nvm) (Node Version Manager) for managing Node.js versions. Install `nvm` by following the instructions [here](https://github.com/nvm-sh/nvm#installing-and-updating).

To install the project dependencies, navigate to the project directory and run:

```bash
yarn install
```

or simply:

```bash
yarn
```

and for adding a new package, you can run:

```bash
yarn add package-name
```

### Configuration

The project requires PostgreSQL and Redis to run. You can set these up manually or use Docker Compose for convenience.
To use Docker Compose, ensure you have Docker and Docker Compose installed. You can start the necessary services by running:

```bash
docker-compose -f docker-compose.local.yml up -d
```

This command will use the docker-compose.local.yml file to set up and start the PostgreSQL and Redis containers in the background.

### TypeORM and Migrations

We use TypeORM in Palette.

-   To generate a migration, use the following command:

```bash
yarn migration:generate migration-name
```

This will generate a migration in the migrations directory, and you can edit it manually if needed.
To run the migrations, use the following command:

```bash
yarn migration:run
```

Note: Before generating a new migration, always run the existing migrations first. This ensures that the new migration does not get messed up.

### Running the project

To run the project in development mode, run:

```bash
yarn dev
```
