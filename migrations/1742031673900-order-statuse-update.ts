import { MigrationInterface, QueryRunner } from "typeorm";

export class OrderStatuseUpdate1742031673900 implements MigrationInterface {
    name = 'OrderStatuseUpdate1742031673900'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."order_status_enum" AS ENUM('PENDING_SELLER_REVIEW', 'CONFIRMED_WAITING_PAYMENT', 'PAYMENT_CONFIRMED_PROCESSING', 'OUT_FOR_DELIVERY', 'DELIVERED')`);
        await queryRunner.query(`ALTER TABLE "order" ADD "status" "public"."order_status_enum" NOT NULL DEFAULT 'PENDING_SELLER_REVIEW'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."order_status_enum"`);
        await queryRunner.query(`ALTER TABLE "order" ADD "status" character varying NOT NULL DEFAULT 'AWAITING-PAYMENT'`);
    }

}
