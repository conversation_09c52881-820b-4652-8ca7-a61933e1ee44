import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRoleUserAdmin1724693616871 implements MigrationInterface {
    name = "AddRoleUserAdmin1724693616871";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "user" ADD "role" character varying NOT NULL DEFAULT 'user'`,
        );
        await queryRunner.query(
            `ALTER TABLE "admin" ADD "role" character varying NOT NULL DEFAULT 'admin'`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "admin" DROP COLUMN "role"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "role"`);
    }
}
