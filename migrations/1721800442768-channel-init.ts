import { MigrationInterface, QueryRunner } from "typeorm";

export class ChannelInit1721800442768 implements MigrationInterface {
    name = "ChannelInit1721800442768";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "website" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "link" character varying NOT NULL, "isActive" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_979e53e64186ccd315cf09b3b14" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "user-website" ("id" SERIAL NOT NULL, "userId" integer NOT NULL, "websiteId" integer NOT NULL, "channelId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_b81fd7fdabb5dfd13226adecd01" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_WEBSITE_NAME" ON "user-website" ("userId", "websiteId", "channelId") `,
        );
        await queryRunner.query(
            `CREATE TYPE "public"."channel_exchangeratetype_enum" AS ENUM('PERCENTAGE', 'FIXED')`,
        );
        await queryRunner.query(
            `CREATE TABLE "channel" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "userId" integer NOT NULL, "field" character varying, "workingHoursStart" character varying, "workingHoursEnd" character varying, "exchangeRateType" "public"."channel_exchangeratetype_enum" NOT NULL DEFAULT 'PERCENTAGE', "exchangeRateValue" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_590f33ee6ee7d76437acf362e39" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "channelId" integer`,
        );
        await queryRunner.query(
            `ALTER TABLE "user-website" ADD CONSTRAINT "FK_e3866b73b1ef4b6626f46749816" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "user-website" ADD CONSTRAINT "FK_54d6a3cb4498e86165e7da0effc" FOREIGN KEY ("websiteId") REFERENCES "website"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "user-website" ADD CONSTRAINT "FK_05bf4f8e65379e0da364be8aeed" FOREIGN KEY ("channelId") REFERENCES "channel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD CONSTRAINT "FK_ab9caff828c1a8cbd47b9602139" FOREIGN KEY ("channelId") REFERENCES "channel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP CONSTRAINT "FK_ab9caff828c1a8cbd47b9602139"`,
        );
        await queryRunner.query(
            `ALTER TABLE "user-website" DROP CONSTRAINT "FK_05bf4f8e65379e0da364be8aeed"`,
        );
        await queryRunner.query(
            `ALTER TABLE "user-website" DROP CONSTRAINT "FK_54d6a3cb4498e86165e7da0effc"`,
        );
        await queryRunner.query(
            `ALTER TABLE "user-website" DROP CONSTRAINT "FK_e3866b73b1ef4b6626f46749816"`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP COLUMN "channelId"`,
        );
        await queryRunner.query(`DROP TABLE "channel"`);
        await queryRunner.query(
            `DROP TYPE "public"."channel_exchangeratetype_enum"`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_WEBSITE_NAME"`,
        );
        await queryRunner.query(`DROP TABLE "user-website"`);
        await queryRunner.query(`DROP TABLE "website"`);
    }
}
