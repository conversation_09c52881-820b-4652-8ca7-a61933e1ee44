import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryAttribiutes1742113131089 implements MigrationInterface {
    name = 'InventoryAttribiutes1742113131089'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory" ALTER COLUMN "name" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory" ALTER COLUMN "name" DROP NOT NULL`);
    }

}
