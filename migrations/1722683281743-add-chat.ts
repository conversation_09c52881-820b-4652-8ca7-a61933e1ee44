import { MigrationInterface, QueryRunner } from "typeorm";

export class AddChat1722683281743 implements MigrationInterface {
    name = "AddChat1722683281743";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "chat" ("id" SERIAL NOT NULL, "userId" integer, "clientId" integer, "text" character varying NOT NULL, "channelId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9d0b2ba74336710fd31154738a5" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "chat" ADD CONSTRAINT "FK_52af74c7484586ef4bdfd8e4dbb" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "chat" ADD CONSTRAINT "FK_6dba494433f6420f81d19fa6de4" FOREIGN KEY ("clientId") REFERENCES "client"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "chat" ADD CONSTRAINT "FK_b33077b34fc1eca5cdaa9b7d4f8" FOREIGN KEY ("channelId") REFERENCES "channel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "chat" DROP CONSTRAINT "FK_b33077b34fc1eca5cdaa9b7d4f8"`,
        );
        await queryRunner.query(
            `ALTER TABLE "chat" DROP CONSTRAINT "FK_6dba494433f6420f81d19fa6de4"`,
        );
        await queryRunner.query(
            `ALTER TABLE "chat" DROP CONSTRAINT "FK_52af74c7484586ef4bdfd8e4dbb"`,
        );
        await queryRunner.query(`DROP TABLE "chat"`);
    }
}
