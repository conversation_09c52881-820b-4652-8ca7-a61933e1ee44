import { MigrationInterface, QueryRunner } from "typeorm";

export class FaqAssignToUser1733572985649 implements MigrationInterface {
    name = "FaqAssignToUser1733572985649";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "faq" DROP CONSTRAINT "FK_2526e0b32f1c4c7166a79cbddbd"`,
        );
        await queryRunner.query(
            `ALTER TABLE "faq" RENAME COLUMN "channelId" TO "userId"`,
        );
        await queryRunner.query(
            `ALTER TABLE "faq" ADD CONSTRAINT "FK_2b6dcfbef5586e37112cd3b6ee8" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "faq" DROP CONSTRAINT "FK_2b6dcfbef5586e37112cd3b6ee8"`,
        );
        await queryRunner.query(
            `ALTER TABLE "faq" RENAME COLUMN "userId" TO "channelId"`,
        );
        await queryRunner.query(
            `ALTER TABLE "faq" ADD CONSTRAINT "FK_2526e0b32f1c4c7166a79cbddbd" FOREIGN KEY ("channelId") REFERENCES "channel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }
}
