import { MigrationInterface, QueryRunner } from "typeorm";

export class ClientPlatformData1725372696403 implements MigrationInterface {
    name = "ClientPlatformData1725372696403";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "client" ADD "platformId" character varying`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_CHANNEL_CLIENT" ON "client" ("channelId", "platformId") `,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_CHANNEL_CLIENT"`,
        );
        await queryRunner.query(
            `ALTER TABLE "client" DROP COLUMN "platformId"`,
        );
    }
}
