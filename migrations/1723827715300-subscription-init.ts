import { MigrationInterface, QueryRunner } from "typeorm";

export class SubscriptionInit1723827715300 implements MigrationInterface {
    name = "SubscriptionInit1723827715300";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "subscription" ("id" SERIAL NOT NULL, "planId" integer NOT NULL, "trackId" character varying, "userId" integer NOT NULL, "description" character varying, "status" character varying NOT NULL DEFAULT 'PENDING', "rejectionReason" character varying, "endDate" TIMESTAMP NOT NULL, CONSTRAINT "PK_8c3e00ebd02103caa1174cd5d9d" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "subscription-plan" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "price" numeric NOT NULL, "duration" integer NOT NULL, "isPopular" boolean NOT NULL DEFAULT false, "isBase" boolean NOT NULL DEFAULT false, "inventoryLimit" integer NOT NULL, "botLimit" integer NOT NULL, "reportLimit" integer NOT NULL, "isActive" boolean NOT NULL DEFAULT true, CONSTRAINT "UQ_71080c3ece5227802e999dac4c1" UNIQUE ("name"), CONSTRAINT "PK_195f33d6c3f5b8d54de95ebd060" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "subscription" ADD CONSTRAINT "FK_6b6d0e4dc88105a4a11103dd2cd" FOREIGN KEY ("planId") REFERENCES "subscription-plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "subscription" ADD CONSTRAINT "FK_cc906b4bc892b048f1b654d2aa0" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "subscription" DROP CONSTRAINT "FK_cc906b4bc892b048f1b654d2aa0"`,
        );
        await queryRunner.query(
            `ALTER TABLE "subscription" DROP CONSTRAINT "FK_6b6d0e4dc88105a4a11103dd2cd"`,
        );
        await queryRunner.query(`DROP TABLE "subscription-plan"`);
        await queryRunner.query(`DROP TABLE "subscription"`);
    }
}
