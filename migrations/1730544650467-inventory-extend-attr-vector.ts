import { MigrationInterface, QueryRunner } from "typeorm";
import { utils } from "../modules/common";

export class InventoryExtendAttrVector1730544650467
    implements MigrationInterface
{
    public async up(queryRunner: QueryRunner): Promise<void> {
        const inventories = (await queryRunner.query(
            `
                SELECT "i"."id", "i"."name", "c"."name" AS "categoryName", "a"."value"
                FROM "inventory" "i"
                LEFT JOIN "category" "c" ON "i"."categoryId" = "c"."id"
                LEFT JOIN "attribute" "a" ON "i"."id" = "a"."inventoryId" 
            `,
        )) as {
            id: number;
            name: string;
            categoryName: string;
            value: string;
        }[];

        const mergedInventories =
            inventories?.reduce(
                (acc, { id, name, categoryName, value }) => {
                    const strId = `${id}`;
                    if (utils.isNil(acc[strId])) {
                        acc[strId] = {
                            id,
                            name,
                            categoryName,
                            attributes: [],
                        };
                    }

                    acc[strId].attributes.push(value);

                    return acc;
                },
                {} as Record<
                    string,
                    {
                        id: number;
                        name: string;
                        categoryName: string;
                        attributes: string[];
                    }
                >,
            ) ?? {};

        for (const inventory of Object.values(mergedInventories)) {
            const attributesVector =
                (inventory?.categoryName ?? "") +
                " " +
                inventory.name +
                " " +
                (inventory.attributes?.join(" ") ?? "");

            await queryRunner.query(
                `UPDATE "inventory" SET "attributesVector" = $1 WHERE id = $2`,
                [attributesVector, inventory.id],
            );
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public async down(queryRunner: QueryRunner): Promise<void> {}
}
