import { MigrationInterface, QueryRunner } from "typeorm";

export class ActionInit1723998680366 implements MigrationInterface {
    name = "ActionInit1723998680366";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "action" ("id" SERIAL NOT NULL, "automationId" integer NOT NULL, "type" character varying NOT NULL, "text" character varying, "file" character varying, "url" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_2d9db9cf5edfbbae74eb56e3a39" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "action" ADD CONSTRAINT "FK_74d5993ad378a2d5058e13a5a4a" FOREIGN KEY ("automationId") REFERENCES "automation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "action" DROP CONSTRAINT "FK_74d5993ad378a2d5058e13a5a4a"`,
        );

        await queryRunner.query(`DROP TABLE "action"`);
    }
}
