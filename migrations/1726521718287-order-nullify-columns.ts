import { MigrationInterface, QueryRunner } from "typeorm";

export class OrderNullifyColumns1726521718287 implements MigrationInterface {
    name = "OrderNullifyColumns1726521718287";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "origin" DROP NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "destination" DROP NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "postalCode" DROP NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "postalCode" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "destination" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "origin" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "url" character varying`,
        );
    }
}
