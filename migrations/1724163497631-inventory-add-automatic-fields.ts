import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryAddAutomaticFields1724163497631
    implements MigrationInterface
{
    name = "InventoryAddAutomaticFields1724163497631";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "url" character varying`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "price" DROP NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "cost" DROP NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "reserved" DROP NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "total" DROP NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "total" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "reserved" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "cost" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "price" SET NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "url"`);
    }
}
