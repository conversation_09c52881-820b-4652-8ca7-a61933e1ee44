import { MigrationInterface, QueryRunner } from "typeorm";

export class NotificationInit1729593955308 implements MigrationInterface {
    name = "NotificationInit1729593955308";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "notification-config" ("id" SERIAL NOT NULL, "userId" integer, "token" character varying NOT NULL, "type" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f73544f2ebede44bb7954382846" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "notification" ("id" SERIAL NOT NULL, "userId" integer, "type" character varying NOT NULL, "sourceId" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "notification-config" ADD CONSTRAINT "FK_b084c5353dcdafb5827c1697b5c" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "notification" ADD CONSTRAINT "FK_1ced25315eb974b73391fb1c81b" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "notification" DROP CONSTRAINT "FK_1ced25315eb974b73391fb1c81b"`,
        );
        await queryRunner.query(
            `ALTER TABLE "notification-config" DROP CONSTRAINT "FK_b084c5353dcdafb5827c1697b5c"`,
        );
        await queryRunner.query(`DROP TABLE "notification"`);
        await queryRunner.query(`DROP TABLE "notification-config"`);
    }
}
