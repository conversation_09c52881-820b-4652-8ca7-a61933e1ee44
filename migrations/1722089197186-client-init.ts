import { MigrationInterface, QueryRunner } from "typeorm";

export class ClientInit1722089197186 implements MigrationInterface {
    name = "ClientInit1722089197186";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "client" ("id" SERIAL NOT NULL, "username" character varying NOT NULL, "userId" integer NOT NULL, "messagingEnabled" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_96da49381769303a6515a8785c7" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "client" ADD CONSTRAINT "FK_ad3b4bf8dd18a1d467c5c0fc13a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "client" DROP CONSTRAINT "FK_ad3b4bf8dd18a1d467c5c0fc13a"`,
        );
        await queryRunner.query(`DROP TABLE "client"`);
    }
}
