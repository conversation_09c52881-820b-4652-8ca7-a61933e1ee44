import { MigrationInterface, QueryRunner } from "typeorm";

export class FaqInit1723971922867 implements MigrationInterface {
    name = "FaqInit1723971922867";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "faq" ("id" SERIAL NOT NULL, "channelId" integer NOT NULL, "question" character varying(255) NOT NULL, "answer" character varying(255) NOT NULL, "status" character varying NOT NULL, "order" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d6f5a52b1a96dd8d0591f9fbc47" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "faq" ADD CONSTRAINT "FK_2526e0b32f1c4c7166a79cbddbd" FOREIGN KEY ("channelId") REFERENCES "channel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "faq" DROP CONSTRAINT "FK_2526e0b32f1c4c7166a79cbddbd"`,
        );
        await queryRunner.query(`DROP TABLE "faq"`);
    }
}
