import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsDisconnectedInChannel1748764192011 implements MigrationInterface {
    name = 'AddIsDisconnectedInChannel1748764192011'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" ALTER COLUMN "isDisconnected" SET DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" ALTER COLUMN "isDisconnected" SET DEFAULT true`);
    }

}
