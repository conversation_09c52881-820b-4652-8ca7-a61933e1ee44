import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryInit1720877690466 implements MigrationInterface {
    name = "InventoryInit1720877690466";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_UNIQUE-NAME"`);
        await queryRunner.query(
            `CREATE TABLE "inventory" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "userId" integer, "categoryId" integer, "price" integer NOT NULL, "cost" integer NOT NULL, "image" character varying NOT NULL, "reserved" integer NOT NULL, "available" integer NOT NULL, "total" integer NOT NULL, "isActive" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_82aa5da437c5bbfb80703b08309" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_INVENTORY_NAME" ON "inventory" ("name", "userId") `,
        );
        await queryRunner.query(
            `ALTER TABLE "category" DROP CONSTRAINT "FK_32b856438dffdc269fa84434d9f"`,
        );
        await queryRunner.query(
            `ALTER TABLE "category" ALTER COLUMN "userId" DROP NOT NULL`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_CATEGORY_NAME" ON "category" ("name", "userId") `,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD CONSTRAINT "FK_4156c96b439e425420e79a78edb" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD CONSTRAINT "FK_fe4917e809e078929fe517ab762" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "category" ADD CONSTRAINT "FK_32b856438dffdc269fa84434d9f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "category" DROP CONSTRAINT "FK_32b856438dffdc269fa84434d9f"`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP CONSTRAINT "FK_fe4917e809e078929fe517ab762"`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP CONSTRAINT "FK_4156c96b439e425420e79a78edb"`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_CATEGORY_NAME"`,
        );
        await queryRunner.query(
            `ALTER TABLE "category" ALTER COLUMN "userId" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "category" ADD CONSTRAINT "FK_32b856438dffdc269fa84434d9f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_INVENTORY_NAME"`,
        );
        await queryRunner.query(`DROP TABLE "inventory"`);
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE-NAME" ON "category" ("name", "userId") `,
        );
    }
}
