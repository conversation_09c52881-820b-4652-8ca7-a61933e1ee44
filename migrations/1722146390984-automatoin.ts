import { MigrationInterface, QueryRunner } from "typeorm";

export class Automatoin1722146390984 implements MigrationInterface {
    name = "Automatoin1722146390984";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "condition" ("id" SERIAL NOT NULL, "keyword" character varying NOT NULL, "automationId" integer NOT NULL, "types" text NOT NULL, CONSTRAINT "PK_f0f824897e3acf880a6e488b632" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "automation" ("id" SERIAL NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_6c0430b160cab96bd145ca5297d" PRIMARY KEY ("id"))`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "automation"`);
        await queryRunner.query(`DROP TABLE "condition"`);
    }
}
