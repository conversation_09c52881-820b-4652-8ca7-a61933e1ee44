import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUserIdAutomatoin1722427591340 implements MigrationInterface {
    name = "AddUserIdAutomatoin1722427591340";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "automation" ADD "userId" integer NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "automation" DROP COLUMN "userId"`,
        );
    }
}
