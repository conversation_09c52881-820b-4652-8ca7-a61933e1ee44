import { MigrationInterface, QueryRunner } from "typeorm";

export class OrderItemInfo1723208599313 implements MigrationInterface {
    name = "OrderItemInfo1723208599313";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "order-items" ADD "amount" integer`,
        );
        await queryRunner.query(`ALTER TABLE "order-items" ADD "cost" numeric`);
        await queryRunner.query(
            `ALTER TABLE "order-items" ADD "price" numeric`,
        );

        await queryRunner.query(
            `UPDATE "order-items" SET "amount" = 0, "cost" = 0, "price" = 0`,
        );

        await queryRunner.query(
            `ALTER TABLE "order-items" ALTER COLUMN "amount" SET NOT NULL`,
        );

        await queryRunner.query(
            `ALTER TABLE "order-items" ALTER COLUMN "cost" SET NOT NULL`,
        );

        await queryRunner.query(
            `ALTER TABLE "order-items" ALTER COLUMN "price" SET NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "order-items" DROP COLUMN "price"`,
        );
        await queryRunner.query(`ALTER TABLE "order-items" DROP COLUMN "cost"`);
        await queryRunner.query(
            `ALTER TABLE "order-items" DROP COLUMN "amount"`,
        );
    }
}
