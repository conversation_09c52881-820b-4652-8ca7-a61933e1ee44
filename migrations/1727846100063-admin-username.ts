import { MigrationInterface, QueryRunner } from "typeorm";

export class AdminUsername1727846100063 implements MigrationInterface {
    name = "AdminUsername1727846100063";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "admin" ADD CONSTRAINT "UQ_5e568e001f9d1b91f67815c580f" UNIQUE ("username")`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "admin" DROP CONSTRAINT "UQ_5e568e001f9d1b91f67815c580f"`,
        );
    }
}
