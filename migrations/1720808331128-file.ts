import { MigrationInterface, QueryRunner } from "typeorm";

export class File1720808331128 implements MigrationInterface {
    name = "File1720808331128";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "file" ("id" SERIAL NOT NULL, "path" character varying NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_36b46d232307066b3a2c9ea3a1d" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "file" ADD CONSTRAINT "FK_b2d8e683f020f61115edea206b3" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "file" DROP CONSTRAINT "FK_b2d8e683f020f61115edea206b3"`,
        );
        await queryRunner.query(`DROP TABLE "file"`);
    }
}
