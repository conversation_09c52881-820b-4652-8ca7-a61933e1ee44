import { MigrationInterface, QueryRunner } from "typeorm";

export class ChannelTokenData1725296977342 implements MigrationInterface {
    name = "ChannelTokenData1725296977342";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "username" character varying`,
        );
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "accessToken" character varying`,
        );
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "accessTokenExpiresAt" TIMESTAMP`,
        );
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "platformId" character varying`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" DROP COLUMN "platformId"`,
        );
        await queryRunner.query(
            `ALTER TABLE "channel" DROP COLUMN "accessTokenExpiresAt"`,
        );
        await queryRunner.query(
            `ALTER TABLE "channel" DROP COLUMN "accessToken"`,
        );
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "username"`);
    }
}
