import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOrderModelChanges1748443006087 implements MigrationInterface {
    name = 'AddOrderModelChanges1748443006087'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" ADD "chatbotEnabled" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "chatbotEnabled"`);
    }

}
