import { MigrationInterface, QueryRunner } from "typeorm";

export class AddChatbotEnabledToChannel1748765400000 implements MigrationInterface {
    name = 'AddChatbotEnabledToChannel1748765400000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Only add chatbotEnabled since isDisconnected already exists
        await queryRunner.query(`ALTER TABLE "channel" ADD "chatbotEnabled" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "chatbotEnabled"`);
    }
}
