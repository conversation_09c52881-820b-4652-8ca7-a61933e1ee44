import { MigrationInterface, QueryRunner } from "typeorm";

export class ClientAddChannel1722089709495 implements MigrationInterface {
    name = "ClientAddChannel1722089709495";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "client" ADD "channelId" integer NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "client" ADD CONSTRAINT "FK_f22ea559c63fe138b9d0d617629" FOREIGN KEY ("channelId") REFERENCES "channel"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "client" DROP CONSTRAINT "FK_f22ea559c63fe138b9d0d617629"`,
        );
        await queryRunner.query(`ALTER TABLE "client" DROP COLUMN "channelId"`);
    }
}
