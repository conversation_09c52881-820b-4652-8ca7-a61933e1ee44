import { MigrationInterface, QueryRunner } from "typeorm";

export class UniqueInventorySku1727171206335 implements MigrationInterface {
    name = "UniqueInventorySku1727171206335";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_WEBSITE_INVENTORY"`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_WEBSITE_INVENTORY_SKU" ON "inventory" ("websiteId", "referenceId", "sku") `,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_WEBSITE_INVENTORY_SKU"`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_WEBSITE_INVENTORY" ON "inventory" ("referenceId", "websiteId") `,
        );
    }
}
