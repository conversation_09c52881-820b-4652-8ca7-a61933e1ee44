import { MigrationInterface, QueryRunner } from "typeorm";

export class FaqFieldsChanged1727523431061 implements MigrationInterface {
    name = "FaqFieldsChanged1727523431061";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "question"`);
        await queryRunner.query(
            `ALTER TABLE "faq" ADD "question" text NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "answer"`);
        await queryRunner.query(`ALTER TABLE "faq" ADD "answer" text NOT NULL`);
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "status"`);
        await queryRunner.query(
            `ALTER TABLE "faq" ADD "status" boolean NOT NULL DEFAULT true`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "status"`);
        await queryRunner.query(
            `ALTER TABLE "faq" ADD "status" character varying NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "answer"`);
        await queryRunner.query(
            `ALTER TABLE "faq" ADD "answer" character varying(255) NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "faq" DROP COLUMN "question"`);
        await queryRunner.query(
            `ALTER TABLE "faq" ADD "question" character varying(255) NOT NULL`,
        );
    }
}
