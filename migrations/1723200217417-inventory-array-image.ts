import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryArrayImage1723200217417 implements MigrationInterface {
    name = "InventoryArrayImage1723200217417";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "image"`);
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "image" character varying array`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "image"`);
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "image" character varying NOT NULL`,
        );
    }
}
