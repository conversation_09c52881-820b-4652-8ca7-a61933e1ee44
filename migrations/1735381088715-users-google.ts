import { MigrationInterface, QueryRunner } from "typeorm";
import { REGISTRATION_METHOD } from "../modules/common/base/types/typing";

export class UsersGoogle1735381088715 implements MigrationInterface {
    name = "UsersGoogle1735381088715";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "user" ADD "email" character varying`,
        );
        await queryRunner.query(
            `ALTER TABLE "user" ADD "registrationMethod" character varying NOT NULL DEFAULT '${REGISTRATION_METHOD.PHONE}'`,
        );
        await queryRunner.query(
            `ALTER TABLE "user" ALTER COLUMN "phone" DROP NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "user" ALTER COLUMN "phone" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "user" DROP COLUMN "registrationMethod"`,
        );
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "email"`);
    }
}
