import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryIsAutomated1723479964615 implements MigrationInterface {
    name = "InventoryIsAutomated1723479964615";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "isAutomated" boolean NOT NULL DEFAULT false`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP COLUMN "isAutomated"`,
        );
    }
}
