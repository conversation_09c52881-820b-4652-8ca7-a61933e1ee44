import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryAddWebsite1724165394189 implements MigrationInterface {
    name = "InventoryAddWebsite1724165394189";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "referenceId" integer`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "websiteId" integer`,
        );
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_WEBSITE_INVENTORY" ON "inventory" ("websiteId", "referenceId") `,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD CONSTRAINT "FK_cdef685bf8ab36d38318d5c0cca" FOREIGN KEY ("websiteId") REFERENCES "website"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP CONSTRAINT "FK_cdef685bf8ab36d38318d5c0cca"`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_WEBSITE_INVENTORY"`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP COLUMN "websiteId"`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP COLUMN "referenceId"`,
        );
    }
}
