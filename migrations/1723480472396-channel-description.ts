import { MigrationInterface, QueryRunner } from "typeorm";

export class ChannelDescription1723480472396 implements MigrationInterface {
    name = "ChannelDescription1723480472396";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "description" character varying`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" DROP COLUMN "description"`,
        );
    }
}
