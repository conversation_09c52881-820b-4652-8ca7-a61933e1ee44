import { MigrationInterface, QueryRunner } from "typeorm";

export class AutiomationIsActive1724097475946 implements MigrationInterface {
    name = "AutiomationIsActive1724097475946";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "automation" ADD "isActive" boolean NOT NULL DEFAULT true`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "automation" DROP COLUMN "isActive"`,
        );
    }
}
