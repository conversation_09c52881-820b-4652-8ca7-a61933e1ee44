import { MigrationInterface, QueryRunner } from "typeorm";

export class AutomationNullableKeyword1723546925789
    implements MigrationInterface
{
    name = "AutomationNullableKeyword1723546925789";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "condition" ALTER COLUMN "keyword" DROP NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "condition" ALTER COLUMN "keyword" SET NOT NULL`,
        );
    }
}
