import { MigrationInterface, QueryRunner } from "typeorm";

export class FilesIsGlobal1723479077589 implements MigrationInterface {
    name = "FilesIsGlobal1723479077589";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "file" ADD "isGlobal" boolean NOT NULL DEFAULT true`,
        );
        await queryRunner.query(
            `ALTER TABLE "file" DROP CONSTRAINT "FK_b2d8e683f020f61115edea206b3"`,
        );
        await queryRunner.query(
            `ALTER TABLE "file" ALTER COLUMN "userId" DROP NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "file" ADD CONSTRAINT "FK_b2d8e683f020f61115edea206b3" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "file" DROP CONSTRAINT "FK_b2d8e683f020f61115edea206b3"`,
        );
        await queryRunner.query(
            `ALTER TABLE "file" ALTER COLUMN "userId" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "file" ADD CONSTRAINT "FK_b2d8e683f020f61115edea206b3" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(`ALTER TABLE "file" DROP COLUMN "isGlobal"`);
    }
}
