import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAttributesToInventory1722089151603
    implements MigrationInterface
{
    name = "AddAttributesToInventory1722089151603";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "attribute" ("id" SERIAL NOT NULL, "inventoryId" integer NOT NULL, "key" character varying NOT NULL, "value" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_b13fb7c5c9e9dff62b60e0de729" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "attribute" ADD CONSTRAINT "FK_a718481c2caad025bf04f98790d" FOREIGN KEY ("inventoryId") REFERENCES "inventory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "attribute" DROP CONSTRAINT "FK_a718481c2caad025bf04f98790d"`,
        );
        await queryRunner.query(`DROP TABLE "attribute"`);
    }
}
