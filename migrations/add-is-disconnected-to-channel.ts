import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsDisconnectedToChannel1234567890123 implements MigrationInterface {
    name = "AddIsDisconnectedToChannel1234567890123";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "channel" ADD "isDisconnected" boolean NOT NULL DEFAULT false`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "channel" DROP COLUMN "isDisconnected"`);
    }
}
