import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryFts1728131676436 implements MigrationInterface {
    name = "InventoryFts1728131676436";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "attributesVector" tsvector`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP COLUMN "attributesVector"`,
        );
    }
}
