import { MigrationInterface, QueryRunner } from "typeorm";

export class ChatUnqiueKey1725373904336 implements MigrationInterface {
    name = "ChatUnqiueKey1725373904336";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "chat" ADD "uniqueKey" character varying NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "chat" ADD CONSTRAINT "UQ_8febe36332a28b664a2b2434fad" UNIQUE ("uniqueKey")`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "chat" DROP CONSTRAINT "UQ_8febe36332a28b664a2b2434fad"`,
        );
        await queryRunner.query(`ALTER TABLE "chat" DROP COLUMN "uniqueKey"`);
    }
}
