import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryRemoveNameIndex1727016476873
    implements MigrationInterface
{
    name = "InventoryRemoveNameIndex1727016476873";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP INDEX "public"."IDX_UNIQUE_INVENTORY_NAME"`,
        );
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(
            `CREATE UNIQUE INDEX "IDX_UNIQUE_INVENTORY_NAME" ON "inventory" ("name", "userId") `,
        );
    }
}
