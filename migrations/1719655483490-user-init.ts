import { MigrationInterface, QueryRunner } from "typeorm";

export class UserInit1719655483490 implements MigrationInterface {
    name = "UserInit1719655483490";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "user" ("id" SERIAL NOT NULL, "state" character varying NOT NULL DEFAULT 'OTP_NOT_VERIFIED', "phone" character varying NOT NULL, CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "user"`);
    }
}
