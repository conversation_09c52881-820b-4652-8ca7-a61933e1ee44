import { MigrationInterface, QueryRunner } from "typeorm";

export class OrderInit1722352592103 implements MigrationInterface {
    name = "OrderInit1722352592103";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TYPE "public"."order_exchangeratetype_enum" AS ENUM('PERCENTAGE', 'FIXED')`,
        );
        await queryRunner.query(
            `CREATE TABLE "order" ("id" SERIAL NOT NULL, "origin" character varying NOT NULL, "destination" character varying NOT NULL, "receiver" character varying NOT NULL, "exchangeRateType" "public"."order_exchangeratetype_enum" NOT NULL, "exchangeRateValue" integer NOT NULL, "userId" integer NOT NULL, "clientId" integer NOT NULL, "status" character varying NOT NULL DEFAULT 'AWAITING-PAYMENT', "postalCode" character varying NOT NULL, "profit" integer NOT NULL, "price" integer NOT NULL, "cost" integer NOT NULL, "shippingPrice" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1031171c13130102495201e3e20" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `CREATE TABLE "order-items" ("id" SERIAL NOT NULL, "orderId" integer NOT NULL, "inventoryId" integer NOT NULL, CONSTRAINT "PK_605fbaee38242facaa1a34b67ad" PRIMARY KEY ("id"))`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" DROP COLUMN "available"`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ADD CONSTRAINT "FK_caabe91507b3379c7ba73637b84" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ADD CONSTRAINT "FK_9b27855a9c2ade186e5c55d1ec3" FOREIGN KEY ("clientId") REFERENCES "client"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "order-items" ADD CONSTRAINT "FK_d42918a88740ece11347e20918f" FOREIGN KEY ("orderId") REFERENCES "order"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "order-items" ADD CONSTRAINT "FK_4e88b53ed5ac75d0972bff12030" FOREIGN KEY ("inventoryId") REFERENCES "inventory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "order-items" DROP CONSTRAINT "FK_4e88b53ed5ac75d0972bff12030"`,
        );
        await queryRunner.query(
            `ALTER TABLE "order-items" DROP CONSTRAINT "FK_d42918a88740ece11347e20918f"`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" DROP CONSTRAINT "FK_9b27855a9c2ade186e5c55d1ec3"`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" DROP CONSTRAINT "FK_caabe91507b3379c7ba73637b84"`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "available" integer NOT NULL`,
        );
        await queryRunner.query(`DROP TABLE "order-items"`);
        await queryRunner.query(`DROP TABLE "order"`);
        await queryRunner.query(
            `DROP TYPE "public"."order_exchangeratetype_enum"`,
        );
    }
}
