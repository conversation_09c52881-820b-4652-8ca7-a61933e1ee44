import { MigrationInterface, QueryRunner } from "typeorm";

export class NumericType1723489253786 implements MigrationInterface {
    name = "NumericType1723489253786";

    public async up(queryRunner: QueryRunner): Promise<void> {
        // INVENTORIES
        const inventories = await queryRunner.query(
            `SELECT "id", "cost", "price" FROM "inventory"`,
        );

        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "price"`);
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "cost"`);

        await queryRunner.query(`ALTER TABLE "inventory" ADD "price" numeric`);
        await queryRunner.query(`ALTER TABLE "inventory" ADD "cost" numeric`);

        for (const { id, cost, price } of inventories) {
            await queryRunner.query(
                `UPDATE "inventory" SET "cost" = $1, "price" = $2 WHERE id = $3`,
                [cost, price, id],
            );
        }

        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "price" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ALTER COLUMN "cost" SET NOT NULL`,
        );

        // ORDERS
        const orders = await queryRunner.query(
            `SELECT "id", "cost", "price", "profit" FROM "order"`,
        );

        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "profit"`);
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "price"`);
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "cost"`);
        await queryRunner.query(
            `ALTER TABLE "order" DROP COLUMN "shippingPrice"`,
        );

        await queryRunner.query(`ALTER TABLE "order" ADD "profit" numeric`);
        await queryRunner.query(`ALTER TABLE "order" ADD "price" numeric`);
        await queryRunner.query(`ALTER TABLE "order" ADD "cost" numeric`);
        await queryRunner.query(
            `ALTER TABLE "order" ADD "shippingPrice" numeric NOT NULL DEFAULT '0'`,
        );

        for (const { id, cost, price, profit } of orders) {
            await queryRunner.query(
                `UPDATE "order" SET "cost" = $1, "price" = $2, "profit" = $3 WHERE id = $4`,
                [cost, price, profit, id],
            );
        }

        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "profit" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "price" SET NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ALTER COLUMN "cost" SET NOT NULL`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "cost"`);
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "cost" integer NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "price"`);
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "price" integer NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" DROP COLUMN "shippingPrice"`,
        );
        await queryRunner.query(
            `ALTER TABLE "order" ADD "shippingPrice" integer NOT NULL DEFAULT '0'`,
        );
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "cost"`);
        await queryRunner.query(
            `ALTER TABLE "order" ADD "cost" integer NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "price"`);
        await queryRunner.query(
            `ALTER TABLE "order" ADD "price" integer NOT NULL`,
        );
        await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "profit"`);
        await queryRunner.query(
            `ALTER TABLE "order" ADD "profit" integer NOT NULL`,
        );
    }
}
