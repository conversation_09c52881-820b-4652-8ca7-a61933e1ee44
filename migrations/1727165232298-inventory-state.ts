import { MigrationInterface, QueryRunner } from "typeorm";

export class InventoryState1727165232298 implements MigrationInterface {
    name = "InventoryState1727165232298";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "sku" character varying`,
        );
        await queryRunner.query(
            `ALTER TABLE "inventory" ADD "state" character varying NOT NULL DEFAULT 'AVAILABLE'`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "state"`);
        await queryRunner.query(`ALTER TABLE "inventory" DROP COLUMN "sku"`);
    }
}
