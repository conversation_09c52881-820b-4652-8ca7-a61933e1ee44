import { MigrationInterface, QueryRunner } from "typeorm";

export class AutomationCondition1722499090021 implements MigrationInterface {
    name = "AutomationCondition1722499090021";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "condition" RENAME COLUMN "types" TO "type"`,
        );
        await queryRunner.query(`ALTER TABLE "condition" DROP COLUMN "type"`);
        await queryRunner.query(
            `ALTER TABLE "condition" ADD "type" character varying NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "condition" ADD CONSTRAINT "FK_7abc5f9aa99ef8ea0ad62dc149b" FOREIGN KEY ("automationId") REFERENCES "automation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
        await queryRunner.query(
            `ALTER TABLE "automation" ADD CONSTRAINT "FK_7b5dbe61f0c2c0c3284147c909f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "automation" DROP CONSTRAINT "FK_7b5dbe61f0c2c0c3284147c909f"`,
        );
        await queryRunner.query(
            `ALTER TABLE "condition" DROP CONSTRAINT "FK_7abc5f9aa99ef8ea0ad62dc149b"`,
        );
        await queryRunner.query(`ALTER TABLE "condition" DROP COLUMN "type"`);
        await queryRunner.query(
            `ALTER TABLE "condition" ADD "type" text NOT NULL`,
        );
        await queryRunner.query(
            `ALTER TABLE "condition" RENAME COLUMN "type" TO "types"`,
        );
    }
}
