import { MigrationInterface, QueryRunner } from "typeorm";

export class ChatAddType1725701643182 implements MigrationInterface {
    name = "ChatAddType1725701643182";

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "chat" ADD "type" character varying NOT NULL DEFAULT 'user'`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chat" DROP COLUMN "type"`);
    }
}
