# Instagram Connection Management Implementation

## Overview
This implementation provides a simple and efficient way to manage Instagram account connection status using a boolean flag `isDisconnected`. Instead of clearing Instagram data, we use a flag-based approach that preserves all Instagram credentials while allowing users to toggle their connection status.

## Key Benefits
✅ **Simple Logic**: Single boolean field to manage connection state  
✅ **Data Preservation**: All Instagram credentials remain intact  
✅ **Fast Operations**: No data clearing/restoration needed  
✅ **Frontend Friendly**: Easy to check connection status  
✅ **Reversible**: Users can reconnect instantly  

## Implementation Details

### Database Changes
- **New Field**: `isDisconnected` (boolean, default: false)
- **Migration**: `add-is-disconnected-to-channel.ts`

### API Endpoints

#### 1. Disconnect Instagram
```
POST /api/v1/channels/instagram/disconnect
Authorization: Bearer <user_token>
```

#### 2. Connect Instagram (Toggle)
```
POST /api/v1/channels/instagram/connect-toggle
Authorization: Bearer <user_token>
```

### Response Format
Both endpoints return:
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "Instagram account [connected/disconnected] successfully"
  },
  "message": "با موفقیت انجام شد"
}
```

## Profile API Changes

### Connected State (`isDisconnected: false`)
```json
{
  "success": true,
  "data": {
    "id": 2,
    "phone": "***********",
    "role": "user",
    "subscription": { ... },
    "channel": {
      "id": 1,
      "name": "palette",
      "username": "plt.tech",
      "exchangeRateType": "PERCENTAGE",
      "exchangeRateValue": 0,
      "isDisconnected": false,
      "createdAt": "2025-05-14T08:58:30.011Z",
      "updatedAt": "2025-05-14T08:58:30.011Z"
    }
  }
}
```

### Disconnected State (`isDisconnected: true`)
```json
{
  "success": true,
  "data": {
    "id": 2,
    "phone": "***********",
    "role": "user",
    "subscription": { ... },
    "channel": {
      "id": 1,
      "name": "palette",
      "username": "plt.tech",
      "exchangeRateType": "PERCENTAGE",
      "exchangeRateValue": 0,
      "isDisconnected": true,
      "createdAt": "2025-05-14T08:58:30.011Z",
      "updatedAt": "2025-05-14T08:58:30.011Z"
    }
  }
}
```

## Frontend Integration

### Connection Status Detection
```javascript
// Simple check for Instagram connection
const isInstagramConnected = (profile) => {
  return profile?.channel && !profile.channel.isDisconnected;
};

// Usage
const profile = useProfile();
const isConnected = isInstagramConnected(profile.data);
```

### UI Implementation
```javascript
const InstagramConnection = () => {
  const { data: profile } = useProfile();
  const isConnected = isInstagramConnected(profile);
  
  const handleDisconnect = async () => {
    try {
      await api.post('/channels/instagram/disconnect');
      refetchProfile();
      toast.success('Instagram disconnected successfully');
    } catch (error) {
      toast.error('Failed to disconnect Instagram');
    }
  };
  
  const handleConnect = async () => {
    try {
      await api.post('/channels/instagram/connect-toggle');
      refetchProfile();
      toast.success('Instagram connected successfully');
    } catch (error) {
      toast.error('Failed to connect Instagram');
    }
  };
  
  return (
    <div>
      {isConnected ? (
        <div>
          <p>✅ Instagram Connected</p>
          <p>Username: @{profile.channel.username}</p>
          <button onClick={handleDisconnect}>Disconnect</button>
        </div>
      ) : (
        <div>
          <p>❌ Instagram Disconnected</p>
          <button onClick={handleConnect}>Connect</button>
        </div>
      )}
    </div>
  );
};
```

## Logic Flow

### Disconnect Process
1. User clicks "Disconnect" button
2. Frontend calls `POST /channels/instagram/disconnect`
3. Backend sets `isDisconnected = true`
4. Profile API returns `isDisconnected: true`
5. Frontend shows disconnected state

### Connect Process
1. User clicks "Connect" button
2. Frontend calls `POST /channels/instagram/connect-toggle`
3. Backend sets `isDisconnected = false`
4. Profile API returns `isDisconnected: false`
5. Frontend shows connected state

## Error Handling
- **400 Bad Request**: When trying to disconnect already disconnected account
- **400 Bad Request**: When trying to connect already connected account
- **404 Not Found**: When user doesn't have a channel
- **500 Internal Server Error**: For unexpected errors

## Files Modified
1. `modules/channels/apps/channels/channel.model.ts` - Added `isDisconnected` field
2. `modules/channels/apps/channels/types/entities.ts` - Updated interface
3. `modules/channels/apps/channels/types/schemas.ts` - Updated response type
4. `modules/channels/apps/channels/responses/get-channel.ts` - Added field to schema
5. `modules/channels/apps/channels/channel.service.ts` - Added connect/disconnect methods
6. `modules/channels/apps/channels/channel.controller.ts` - Added endpoints
7. `modules/channels/apps/channels/channel.router.ts` - Added routes
8. `migrations/add-is-disconnected-to-channel.ts` - Database migration

## Deployment Steps
1. Run migration: `npm run migration:run`
2. Deploy updated code
3. Test both connect and disconnect functionality
4. Update frontend to use new logic

## Advantages Over Previous Approach
- **Simpler**: Single boolean vs multiple nullable fields
- **Faster**: No data clearing/restoration
- **Safer**: No risk of losing Instagram credentials
- **Cleaner**: Clear intent with boolean flag
- **Reversible**: Instant reconnection without re-authentication
