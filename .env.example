# ─────────────────────────────────────────────────────
# 🌐 Application Settings
# ─────────────────────────────────────────────────────
#ir or us
ENV_NAME=ir
# Use "fa" or "en" to set app language
PUBLIC_APP_LANG=fa

# Environment: development, staging, production
NODE_ENV=

# Project name
PROJECT=

# Domain and port where the app is served
DOMAIN=
PORT=

# Web frontend URL (e.g. https://palette-tech.io)
WEB_APP_URL=

# ─────────────────────────────────────────────────────
# 🛢️ Database Configuration
# ─────────────────────────────────────────────────────

POSTGRES_DB_HOST=
POSTGRES_DB_PORT=
POSTGRES_DB_NAME=
POSTGRES_DB_USER=
POSTGRES_DB_PASSWORD=

# ─────────────────────────────────────────────────────
# 🔁 Redis Configuration
# ─────────────────────────────────────────────────────

REDIS_HOST=
REDIS_PORT=
REDIS_PREFIX=

# ─────────────────────────────────────────────────────
# 🔐 Security & Auth
# ─────────────────────────────────────────────────────

# Number of salt rounds for password hashing
SALT_ROUNDS=

# Session secret key
SESSION_SECRET=

# JWT secret key for token signing
JWT_SECRET=

# ─────────────────────────────────────────────────────
# 🔗 Google OAuth
# ─────────────────────────────────────────────────────

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_USERINFO_URL=https://www.googleapis.com/oauth2/v3/userinfo

# ─────────────────────────────────────────────────────
# 📲 SMS Provider
# ─────────────────────────────────────────────────────

KAVENEGAR_API_KEY=

# ─────────────────────────────────────────────────────
# 📝 Logging & Monitoring
# ─────────────────────────────────────────────────────

# Enable console logs (true/false)
ENABLE_CONSOLE_LOG=

# File path for logs
LOG_FILE_PATH=

# Enable Prometheus metrics (true/false)
ENABLE_PROMETHEUS_LOG=

# ─────────────────────────────────────────────────────
# 🌍 Docker NGINX Proxy Configuration
# ─────────────────────────────────────────────────────

VIRTUAL_HOST=
LETSENCRYPT_HOST=
VIRTUAL_PORT=

# ─────────────────────────────────────────────────────
# 📦 Static Server
# ─────────────────────────────────────────────────────

# Static file access URL
STATIC_SERVER_URL=https://backend.palette-tech.io/api/v1/static-server

# ─────────────────────────────────────────────────────
# 🧪 Testing
# ─────────────────────────────────────────────────────

# Mock users for testing purpose (format: phone1:password1,phone2:password2,...)
# OTP and seller notifications will not be sent to these numbers
TEST_USERS=09999999999:99999,09888888888:88888