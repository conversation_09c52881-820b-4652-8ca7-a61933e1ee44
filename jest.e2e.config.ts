import { Config } from "@jest/types";

const e2eConfig: Config.InitialOptions = {
    preset: "ts-jest",
    testEnvironment: "node",
    moduleFileExtensions: ["ts", "js"],
    transform: {
        "^.+\\.ts$": "ts-jest",
    },
    testMatch: ["**/modules/**/tests/*.e2e.test.ts"],
    moduleDirectories: ["node_modules", "modules"],
    testTimeout: 20000,
    // setupFiles: ["reflect-metadata"],
};

export default e2eConfig;
