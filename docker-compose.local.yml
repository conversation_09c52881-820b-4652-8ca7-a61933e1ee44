services:
    pgadmin:
        container_name: palette-pgadmin
        image: docker.arvancloud.ir/dpage/pgadmin4
        restart: unless-stopped
        ports:
            - "5050:80"
        environment:
            - PGADMIN_DEFAULT_EMAIL=<EMAIL>
            - PGADMIN_DEFAULT_PASSWORD=admin
        volumes:
            - pgadmin_data:/var/lib/pgadmin
        networks:
            - local_palette_network

    db:
        container_name: palette-db
        image: docker.arvancloud.ir/postgres
        restart: unless-stopped
        ports:
            - "5432:5432"
        env_file: /etc/palette/local/.env
        environment:
            - POSTGRES_DB=palette
            - POSTGRES_USER=root
            - POSTGRES_PASSWORD=root
        volumes:
            - postgres_data:/var/lib/postgresql/data
        networks:
            - local_palette_network

    redis:
        container_name: palette-cache
        image: docker.arvancloud.ir/redis
        restart: unless-stopped
        ports:
            - "6379:6379"
        env_file: /etc/palette/local/.env
        environment:
            - ALLOW_EMPTY_PASSWORD=yes
        volumes:
            - redis_data:/data
        networks:
            - local_palette_network

    elasticsearch:
        container_name: elasticsearch
        image: docker.arvancloud.ir/elasticsearch:8.15.3
        restart: unless-stopped
        ports:
            - "9200:9200"
            - "9300:9300"
        environment:
            - discovery.type=single-node
            - ES_JAVA_OPTS=-Xms1024m -Xmx1024m
            - xpack.security.enabled=false
        volumes:
            - es_data:/usr/share/elasticsearch/data
        networks:
            - local_palette_network

    kibana:
        container_name: kibana
        image: docker.elastic.co/kibana/kibana:8.5.1
        restart: unless-stopped
        ports:
            - "5601:5601"
        environment:
            - ELASTICSEARCH_URL=http://elasticsearch:9200
        networks:
            - local_palette_network
    api:
        container_name: palette-api
        build:
            context: .
            dockerfile: Dockerfile.dev
        restart: unless-stopped
        ports:
            - "3000:3000"
            - "9229:9229"
        env_file: /etc/palette/local/.env
        command: sh -c "yarn migration:run && yarn dev"
        volumes:
            - .:/app
            - /app/node_modules
            - /etc/palette/local/credentials.json:/etc/palette/local/credentials.json:ro

        depends_on:
            - db
            - redis
            - elasticsearch
        networks:
            - local_palette_network

volumes:
    postgres_data:
    redis_data:
    es_data:
    pgadmin_data:

networks:
    local_palette_network:
        driver: bridge
