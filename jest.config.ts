import { Config } from "@jest/types";

const unitConfig: Config.InitialOptions = {
    preset: "ts-jest",
    testEnvironment: "node",
    moduleFileExtensions: ["ts", "js"],
    transform: {
        "^.+\\.ts$": "ts-jest",
    },
    testMatch: ["**/modules/**/tests/*.spec.ts"],
    moduleDirectories: ["node_modules", "modules"],
    testTimeout: 20000,
    // setupFiles: ["reflect-metadata"],
};

export default unitConfig;
